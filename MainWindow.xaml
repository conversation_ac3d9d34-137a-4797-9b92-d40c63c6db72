<ui:FluentWindow x:Class="RFID_UI.MainWindow"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
                 xmlns:local="clr-namespace:RFID_UI"
                 mc:Ignorable="d"
                 Title="UHF RFID Reader Demo v4.3 - 全新界面设计"
                 Height="800" Width="1200"
                 WindowStartupLocation="CenterScreen"
                 d:DataContext="{d:DesignInstance local:ViewModels.MainWindowViewModel}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="5"/>
            <RowDefinition Height="300"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <ui:TitleBar Grid.Row="0"
                     Title="UHF RFID Reader Demo v4.3"
                     Icon="{ui:SymbolIcon Symbol=Cellular5g24}"
                     ShowMaximize="True"
                     ShowMinimize="True"/>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="1" x:Name="MainTabControl">
            <TabItem Header="读写器设置">
                <Frame x:Name="ReaderSettingsFrame"/>
            </TabItem>
            <TabItem Header="高级命令">
                <Frame x:Name="AdvancedCommandsFrame"/>
            </TabItem>
            <TabItem Header="盘点测试">
                <Frame x:Name="InventoryTestFrame"/>
            </TabItem>
        </TabControl>

        <!-- 分割器 -->
        <GridSplitter Grid.Row="2"
                     HorizontalAlignment="Stretch"
                     VerticalAlignment="Stretch"
                     Background="Gray"
                     ResizeDirection="Rows"/>

        <!-- 底部日志区域 -->
        <Grid Grid.Row="3" Background="White" Margin="2,0,2,2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 操作按钮栏 -->
            <Grid Grid.Row="0" Margin="3,2,3,2" Background="#FFF8F8F8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：串口日志开关 -->
                <CheckBox Grid.Column="0"
                         Content="串口日志"
                         IsChecked="{Binding ShowSerialLogs}"
                         FontSize="14"
                         VerticalAlignment="Center"
                         Margin="2,2,20,2"/>

                <!-- 右侧：操作按钮 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <ui:Button Content="清除"
                              Icon="{ui:SymbolIcon Symbol=Delete24}"
                              Command="{Binding ClearLogCommand}"
                              Margin="2,2,2,2"
                              FontSize="14"/>
                    <ui:Button Content="保存"
                              Icon="{ui:SymbolIcon Symbol=Save24}"
                              Command="{Binding SaveLogCommand}"
                              FontSize="14"/>
                </StackPanel>
            </Grid>

            <!-- 性能监控区域 -->
            <Border Grid.Row="1"
                    Background="#FFF0F8FF"
                    BorderBrush="#FFD0E8FF"
                    BorderThickness="1"
                    Margin="3,2,3,2"
                    CornerRadius="3"
                    Visibility="{Binding AdvancedCommandsViewModel.ShowPerformanceMonitor, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid Margin="8,4">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ui:SymbolIcon Grid.Column="0"
                                   Symbol="Settings24"
                                   FontSize="14"
                                   Foreground="#FF4A90E2"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>

                    <ui:TextBlock Grid.Column="1"
                                  FontSize="12"
                                  Foreground="#FF666666"
                                  VerticalAlignment="Center">
                        <Run Text="性能状态: "/>
                        <Run Text="{Binding AdvancedCommandsViewModel.PerformanceStatus}"/>
                    </ui:TextBlock>
                </Grid>
            </Border>

            <!-- 日志内容 -->
            <ScrollViewer x:Name="LogScrollViewer"
                         Grid.Row="2"
                         Margin="3,0,3,3"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Auto"
                         Loaded="LogScrollViewer_Loaded">
                <TextBox x:Name="LogTextBox"
                        Text="{Binding LogContent}"
                        IsReadOnly="True"
                        TextWrapping="Wrap"
                        FontFamily="Consolas"
                        FontSize="14"
                        Background="Transparent"
                        BorderThickness="0"
                        Padding="0"
                        Loaded="LogTextBox_Loaded"/>
            </ScrollViewer>
        </Grid>
    </Grid>
</ui:FluentWindow>
