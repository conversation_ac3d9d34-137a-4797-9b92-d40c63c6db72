using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Controls;
using Microsoft.Win32;
using RFID_UI.Models;
using RFID_UI.Commands;
using RFID_UI.Services;

namespace RFID_UI.ViewModels
{
    public class AdvancedCommandsViewModel : INotifyPropertyChanged
    {
        private readonly ReaderManager _readerManager;
        private readonly Action<string>? _addLogAction;
        private bool _isFastAntennaMode = true;
        private bool _isCustomSessionMode = false;
        private string _commandStatus = "就绪";
        private Brush _commandStatusColor = Brushes.Green;
        private UserControl? _currentParameterControl;
        private bool _isCommandRunning = false;
        private bool _currentCommandHasPhase = false; // 记录当前命令是否启用相位

        // EPC去重和累加相关
        // 高性能EPC索引（使用ConcurrentDictionary提升并发性能）
        private readonly System.Collections.Concurrent.ConcurrentDictionary<string, TagData> _epcDictionary =
            new System.Collections.Concurrent.ConcurrentDictionary<string, TagData>();

        // 批量更新缓存
        private readonly List<TagData> _batchUpdateCache = new List<TagData>();
        private bool _isReceiving = false;

        // 统计信息
        private string _inventoryStatistics = "";
        private bool _showStatistics = false;

        // 日志控制
        private bool _showSerialLogs = false;

        // 标签过滤相关
        private bool _isTagFilterVisible = false;
        private string _selectedMaskId = "Mask No.1";
        private string _selectedSessionId = "S0";
        private string _selectedAction = "00";
        private string _selectedMemBank = "EPC";
        private string _startAddress = "32";
        private string _maskValue = "";
        private string _calculatedMaskLength = "0";
        private string _selectedClearMaskId = "Mask ALL";
        private ObservableCollection<TagMaskInfo> _maskList = new();

        // 批量更新相关
        private readonly List<TagDataEventArgs> _pendingTagUpdates = new List<TagDataEventArgs>();
        private readonly System.Windows.Threading.DispatcherTimer _updateTimer;

        // 性能优化器
        private readonly RFID_UI.Utils.PerformanceOptimizer _performanceOptimizer = new RFID_UI.Utils.PerformanceOptimizer();

        // 盘点计时器和统计数据
        private readonly System.Windows.Threading.DispatcherTimer _inventoryTimer;
        private DateTime _inventoryStartTime;
        private string _inventoryDuration = "00:00.000";
        private string _totalReadCount = "-";
        private string _readRate = "-";
        private string _discoveredTagCount = "0";

        // 性能监控数据
        private string _performanceStatus = "就绪";
        private bool _showPerformanceMonitor = false;

        public ObservableCollection<TagData> TagDataCollection { get; }

        // 属性
        public bool IsFastAntennaMode
        {
            get => _isFastAntennaMode;
            set
            {
                _isFastAntennaMode = value;
                if (value) { _isCustomSessionMode = false; }
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsCustomSessionMode));
                UpdateCurrentCommand();
            }
        }

        public bool IsCustomSessionMode
        {
            get => _isCustomSessionMode;
            set
            {
                _isCustomSessionMode = value;
                if (value) { _isFastAntennaMode = false; }
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsFastAntennaMode));
                UpdateCurrentCommand();
            }
        }

        public string CommandStatus
        {
            get => _commandStatus;
            set
            {
                _commandStatus = value;
                OnPropertyChanged();
            }
        }

        public Brush CommandStatusColor
        {
            get => _commandStatusColor;
            set
            {
                _commandStatusColor = value;
                OnPropertyChanged();
            }
        }



        public UserControl? CurrentParameterControl
        {
            get => _currentParameterControl;
            set
            {
                _currentParameterControl = value;
                OnPropertyChanged();
            }
        }

        public bool IsReceiving
        {
            get => _isReceiving;
            set
            {
                _isReceiving = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanStopReceiving));
            }
        }

        public bool CanStopReceiving => _isReceiving;

        public string InventoryStatistics
        {
            get => _inventoryStatistics;
            set
            {
                _inventoryStatistics = value;
                OnPropertyChanged();
            }
        }

        public bool ShowStatistics
        {
            get => _showStatistics;
            set
            {
                _showStatistics = value;
                OnPropertyChanged();
            }
        }

        public bool ShowSerialLogs
        {
            get => _showSerialLogs;
            set
            {
                _showSerialLogs = value;
                OnPropertyChanged();
            }
        }

        public string InventoryDuration
        {
            get => _inventoryDuration;
            set
            {
                _inventoryDuration = value;
                OnPropertyChanged();
            }
        }

        public string TotalReadCount
        {
            get => _totalReadCount;
            set
            {
                _totalReadCount = value;
                OnPropertyChanged();
            }
        }

        public string ReadRate
        {
            get => _readRate;
            set
            {
                _readRate = value;
                OnPropertyChanged();
            }
        }

        public string DiscoveredTagCount
        {
            get => _discoveredTagCount;
            set
            {
                _discoveredTagCount = value;
                OnPropertyChanged();
            }
        }

        public string PerformanceStatus
        {
            get => _performanceStatus;
            set { _performanceStatus = value; OnPropertyChanged(); }
        }

        public bool ShowPerformanceMonitor
        {
            get => _showPerformanceMonitor;
            set { _showPerformanceMonitor = value; OnPropertyChanged(); }
        }

        // 标签过滤属性
        public bool IsTagFilterVisible
        {
            get => _isTagFilterVisible;
            set { _isTagFilterVisible = value; OnPropertyChanged(); }
        }

        public ObservableCollection<string> MaskIdOptions { get; private set; } = new();
        public ObservableCollection<string> SessionIdOptions { get; private set; } = new();
        public ObservableCollection<string> ActionOptions { get; private set; } = new();
        public ObservableCollection<string> MemBankOptions { get; private set; } = new();
        public ObservableCollection<string> ClearMaskIdOptions { get; private set; } = new();

        public string SelectedMaskId
        {
            get => _selectedMaskId;
            set { _selectedMaskId = value; OnPropertyChanged(); }
        }

        public string SelectedSessionId
        {
            get => _selectedSessionId;
            set { _selectedSessionId = value; OnPropertyChanged(); }
        }

        public string SelectedAction
        {
            get => _selectedAction;
            set { _selectedAction = value; OnPropertyChanged(); }
        }

        public string SelectedMemBank
        {
            get => _selectedMemBank;
            set { _selectedMemBank = value; OnPropertyChanged(); }
        }

        public string StartAddress
        {
            get => _startAddress;
            set { _startAddress = value; OnPropertyChanged(); }
        }

        public string MaskValue
        {
            get => _maskValue;
            set
            {
                if (ValidateHexInput(value))
                {
                    _maskValue = value.ToUpper();
                    OnPropertyChanged();
                    // 实时计算长度
                    CalculatedMaskLength = CalculateMaskLength(value);
                }
            }
        }

        public string CalculatedMaskLength
        {
            get => _calculatedMaskLength;
            private set { _calculatedMaskLength = value; OnPropertyChanged(); }
        }

        public string SelectedClearMaskId
        {
            get => _selectedClearMaskId;
            set { _selectedClearMaskId = value; OnPropertyChanged(); }
        }

        public ObservableCollection<TagMaskInfo> MaskList
        {
            get => _maskList;
            set { _maskList = value; OnPropertyChanged(); }
        }

        private string _inventoryButtonText = "🚀 开始盘点";
        public string InventoryButtonText
        {
            get => _inventoryButtonText;
            set
            {
                _inventoryButtonText = value;
                OnPropertyChanged();
            }
        }

        // Commands
        public ICommand InventoryCommand { get; }
        public ICommand ResetParametersCommand { get; }
        public ICommand ClearTagDataCommand { get; }
        public ICommand ExportTagDataCommand { get; }
        public ICommand SaveToTargetListCommand { get; }
        public ICommand TagFilterCommand { get; }
        public ICommand SetFilterCommand { get; }
        public ICommand ClearFilterCommand { get; }
        public ICommand QueryFilterCommand { get; }
        public ICommand ReturnToDataViewCommand { get; }

        public AdvancedCommandsViewModel(ReaderManager readerManager, Action<string>? addLogAction = null)
        {
            _readerManager = readerManager;
            _addLogAction = addLogAction;
            TagDataCollection = new ObservableCollection<TagData>();

            InventoryCommand = new RelayCommand(ExecuteInventoryCommand, CanExecuteInventory);
            ResetParametersCommand = new RelayCommand(ResetParameters);
            ClearTagDataCommand = new RelayCommand(ClearTagData);
            ExportTagDataCommand = new RelayCommand(ExportTagData);
            SaveToTargetListCommand = new RelayCommand(ExecuteSaveToTargetList);
            TagFilterCommand = new RelayCommand(ShowTagFilterView);
            SetFilterCommand = new RelayCommand(ExecuteSetFilter);
            ClearFilterCommand = new RelayCommand(ExecuteClearFilter);
            QueryFilterCommand = new RelayCommand(ExecuteQueryFilter);
            ReturnToDataViewCommand = new RelayCommand(ExecuteReturnToDataView);

            // 初始化标签过滤选项
            InitializeTagFilterOptions();

            // 订阅ReaderManager事件
            _readerManager.TagDataReceived += OnTagDataReceived;
            _readerManager.CommandCompleted += OnCommandCompleted;
            _readerManager.ReceiveTimeout += OnReceiveTimeout;

            // 初始化批量更新定时器（使用性能优化器的参数）
            _updateTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(_performanceOptimizer.BatchUpdateInterval)
            };
            _updateTimer.Tick += BatchUpdateUI;

            // 初始化盘点计时器
            _inventoryTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100)
            };
            _inventoryTimer.Tick += UpdateInventoryDuration;

            // 初始化性能监控定时器
            var performanceTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            performanceTimer.Tick += UpdatePerformanceStatus;
            performanceTimer.Start();

            // 初始化时查询当前掩码
            try
            {
                ExecuteQueryFilter();
            }
            catch (Exception ex)
            {
                AddLog($"❌ 初始化查询掩码失败: {ex.Message}");
            }

            // 初始化默认控件
            UpdateCurrentCommand();
        }

        private bool CanExecuteInventory()
        {
            return true; // 按钮始终可点击，内部逻辑判断状态
        }

        private async void ExecuteInventoryCommand()
        {
            if (!_isReceiving)
            {
                // 切换回数据展示界面
                IsTagFilterVisible = false;

                // 开始盘点
                await StartInventory();
            }
            else
            {
                // 停止盘点
                StopInventory();
            }
        }

        private async Task StartInventory()
        {
            // 检查设备连接状态
            if (!_readerManager.IsConnected())
            {
                AddLog("❌ 设备未连接，请先连接设备");
                CommandStatus = "设备未连接";
                CommandStatusColor = Brushes.Red;
                return;
            }

            _isCommandRunning = true;
            IsReceiving = true;
            InventoryButtonText = "🛑 停止盘点";
            CommandStatus = "执行中...";
            CommandStatusColor = Brushes.Orange;

            // 重置并启动统计
            ResetStatistics();
            _inventoryStartTime = DateTime.Now;
            _inventoryTimer.Start();

            try
            {
                if (_isFastAntennaMode)
                {
                    await ExecuteFastAntennaCommandAsync();
                }
                else if (_isCustomSessionMode)
                {
                    await ExecuteCustomSessionCommandAsync();
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 命令执行异常: {ex.Message}");
                StopInventory(); // 异常时停止盘点
            }
        }

        private void StopInventory()
        {
            _isCommandRunning = false;
            IsReceiving = false;
            InventoryButtonText = "🚀 开始盘点";

            // 更新命令状态
            CommandStatus = "就绪";
            CommandStatusColor = Brushes.Green;

            // 停止所有计时器
            _inventoryTimer.Stop();
            _updateTimer.Stop(); // 停止批量更新定时器

            // 停止接收
            _readerManager.StopContinuousReceive();

            // 计算最终耗时
            if (_inventoryStartTime != default)
            {
                var finalDuration = DateTime.Now - _inventoryStartTime;
                InventoryDuration = finalDuration.ToString(@"mm\:ss\.fff");
            }

            // 刷新按钮状态
            OnPropertyChanged(nameof(CanStopReceiving));
        }





        private void ResetParameters()
        {
            try
            {
                // 根据当前选择的命令类型重置参数
                if (_isFastAntennaMode)
                {
                    var fastAntennaControl = CurrentParameterControl as Views.UserControls.FastAntennaControl;
                    var fastAntennaViewModel = fastAntennaControl?.DataContext as ViewModels.FastAntennaViewModel;

                    if (fastAntennaViewModel != null)
                    {
                        fastAntennaViewModel.ResetToDefaults();
                        CommandStatus = "快速天线参数已重置";
                        CommandStatusColor = Brushes.Blue;
                        AddLog("🔄 快速天线切换参数已重置为默认值");
                    }
                    else
                    {
                        CommandStatus = "重置失败：无法获取参数控件";
                        CommandStatusColor = Brushes.Red;
                        AddLog("❌ 参数重置失败：无法获取快速天线参数控件");
                    }
                }
                else if (_isCustomSessionMode)
                {
                    var customSessionControl = CurrentParameterControl as Views.UserControls.CustomSessionControl;
                    var customSessionViewModel = customSessionControl?.DataContext as ViewModels.CustomSessionViewModel;

                    if (customSessionViewModel != null)
                    {
                        // 重置自定义会话参数到默认值
                        customSessionViewModel.SelectedSession = "S1";
                        customSessionViewModel.IsTargetASelected = true;
                        customSessionViewModel.IsTargetBSelected = false;
                        customSessionViewModel.RepeatCount = 1;
                        customSessionViewModel.UseSL = false;
                        customSessionViewModel.SelectedSL = "00";
                        customSessionViewModel.EnablePhase = false;

                        CommandStatus = "自定义会话参数已重置";
                        CommandStatusColor = Brushes.Blue;
                        AddLog("🔄 自定义会话参数已重置为默认值");
                    }
                    else
                    {
                        CommandStatus = "重置失败：无法获取参数控件";
                        CommandStatusColor = Brushes.Red;
                        AddLog("❌ 参数重置失败：无法获取自定义会话参数控件");
                    }
                }
                else
                {
                    CommandStatus = "未选择命令类型";
                    CommandStatusColor = Brushes.Orange;
                    AddLog("⚠️ 参数重置失败：未选择有效的命令类型");
                }
            }
            catch (Exception ex)
            {
                CommandStatus = $"重置异常: {ex.Message}";
                CommandStatusColor = Brushes.Red;
                AddLog($"❌ 参数重置异常: {ex.Message}");
            }
        }

        private void ClearTagData()
        {
            TagDataCollection.Clear();
            _epcDictionary.Clear();
            ShowStatistics = false;
            InventoryStatistics = "";
            ResetStatistics();
            AddLog("🗑️ 已清空标签数据");
        }

        private void ExportTagData()
        {
            try
            {
                if (TagDataCollection.Count == 0)
                {
                    System.Windows.MessageBox.Show("没有标签数据可导出。", "提示",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    AddLog("⚠️ 导出失败：没有标签数据");
                    return;
                }

                // 创建保存文件对话框
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出标签数据",
                    Filter = "CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                    DefaultExt = "csv",
                    FileName = $"RFID_TagData_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var csvContent = new StringBuilder();

                    // 添加CSV头部
                    csvContent.AppendLine("序号,EPC号,PC,RSSI,天线,频点,相位,读取次数,最后读取时间,原始FreqAnt");

                    // 添加数据行
                    foreach (var tag in TagDataCollection)
                    {
                        csvContent.AppendLine($"{tag.Index},{tag.Epc},{tag.PC},{tag.Rssi},{tag.Antenna},{tag.FreqPoint},{tag.Phase},{tag.Count},{tag.LastSeen},{tag.RawFreqAnt:X2}");
                    }

                    // 保存文件
                    File.WriteAllText(saveFileDialog.FileName, csvContent.ToString(), Encoding.UTF8);

                    // 显示成功消息
                    System.Windows.MessageBox.Show($"标签数据已成功导出到:\n{saveFileDialog.FileName}\n\n共导出 {TagDataCollection.Count} 条记录",
                        "导出成功", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    CommandStatus = "导出完成";
                    CommandStatusColor = Brushes.Green;
                    AddLog($"📊 标签数据已导出: {Path.GetFileName(saveFileDialog.FileName)} ({TagDataCollection.Count} 条记录)");
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"导出标签数据失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                CommandStatus = $"导出失败: {ex.Message}";
                CommandStatusColor = Brushes.Red;
                AddLog($"❌ 导出标签数据失败: {ex.Message}");
            }
        }

        private void ExecuteSaveToTargetList()
        {
            try
            {
                if (TagDataCollection.Count == 0)
                {
                    System.Windows.MessageBox.Show("没有标签数据可保存到目标列表。", "提示",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    AddLog("⚠️ 保存失败：没有标签数据");
                    return;
                }

                // 显示确认对话框
                var result = System.Windows.MessageBox.Show(
                    $"确定要将当前 {TagDataCollection.Count} 个标签保存到目标标签列表吗？\n\n" +
                    "注意：重复的EPC将被自动跳过。",
                    "确认保存到目标列表",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    // 使用TargetTagService添加标签
                    var targetTagService = TargetTagService.Instance;
                    int addedCount = targetTagService.AddTargetTagsFromTagData(TagDataCollection);

                    // 显示结果
                    if (addedCount > 0)
                    {
                        System.Windows.MessageBox.Show(
                            $"成功保存 {addedCount} 个标签到目标列表！\n" +
                            $"跳过重复标签：{TagDataCollection.Count - addedCount} 个\n\n" +
                            $"目标列表总数：{targetTagService.TargetTagCount} 个",
                            "保存成功",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);

                        AddLog($"✅ 已保存 {addedCount} 个标签到目标列表（跳过重复 {TagDataCollection.Count - addedCount} 个）");
                    }
                    else
                    {
                        System.Windows.MessageBox.Show(
                            "所有标签都已存在于目标列表中，未添加新标签。",
                            "提示",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Information);

                        AddLog("ℹ️ 所有标签都已存在于目标列表中");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"保存到目标列表失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                AddLog($"❌ 保存到目标列表失败: {ex.Message}");
            }
        }

        private void UpdateCurrentCommand()
        {
            if (_isFastAntennaMode)
            {
                var fastAntennaViewModel = new FastAntennaViewModel();
                CurrentParameterControl = new Views.UserControls.FastAntennaControl(fastAntennaViewModel);
            }
            else if (_isCustomSessionMode)
            {
                var customSessionViewModel = new CustomSessionViewModel();
                CurrentParameterControl = new Views.UserControls.CustomSessionControl(customSessionViewModel);
            }
        }

        private async Task ExecuteFastAntennaCommandAsync()
        {
            if (!_readerManager.IsConnected())
            {
                CommandStatus = "设备未连接";
                CommandStatusColor = Brushes.Red;
                AddLog("❌ 快速天线切换失败：设备未连接");
                IsReceiving = false;
                _isCommandRunning = false;
                return;
            }

            try
            {
                // 获取FastAntennaControl的参数
                var fastAntennaControl = CurrentParameterControl as Views.UserControls.FastAntennaControl;
                var fastAntennaViewModel = fastAntennaControl?.DataContext as ViewModels.FastAntennaViewModel;

                if (fastAntennaViewModel == null)
                {
                    CommandStatus = "参数获取失败";
                    CommandStatusColor = Brushes.Red;
                    AddLog("❌ 快速天线切换失败：无法获取参数配置");
                    IsReceiving = false;
                    _isCommandRunning = false;
                    return;
                }

                // 获取天线配置
                var antennaConfigs = fastAntennaViewModel.GetAntennaConfigs();
                if (antennaConfigs.Length == 0)
                {
                    CommandStatus = "未选择天线";
                    CommandStatusColor = Brushes.Red;
                    AddLog("❌ 快速天线切换失败：请至少选择一个天线");
                    IsReceiving = false;
                    _isCommandRunning = false;
                    return;
                }

                // 构建天线和停留时间数组
                var antennas = antennaConfigs.Select(c => (byte)c.AntennaNumber).ToArray();
                var stayTimes = antennaConfigs.Select(c => c.StayTime).ToArray();

                // 获取其他参数
                byte session = fastAntennaViewModel.GetSessionValue();
                byte target = fastAntennaViewModel.GetTargetValue();
                byte interval = byte.TryParse(fastAntennaViewModel.IntervalTime, out byte intervalVal) ? intervalVal : (byte)0;
                byte repeat = byte.TryParse(fastAntennaViewModel.RepeatCount, out byte repeatVal) ? repeatVal : (byte)1;

                // 获取临时功率参数
                bool enableTempPower = fastAntennaViewModel.EnableTempPower;

                AddLog($"🚀 盘存开始");

                byte[]? response;

                if (enableTempPower)
                {
                    // 使用V3.8格式（支持SL和临时功率）
                    bool enableSL = fastAntennaViewModel.EnableSL;
                    byte slValue = fastAntennaViewModel.GetSLValue();
                    bool enablePhase = fastAntennaViewModel.EnablePhase;
                    byte tempPowerValue = byte.TryParse(fastAntennaViewModel.TempPowerValue, out byte tempPowerVal) ? tempPowerVal : (byte)30;

                    // 记录当前命令是否启用相位，用于后续数据解析
                    _currentCommandHasPhase = enablePhase;

                    response = _readerManager.FastSwitchAntennaInventoryV38(
                        antennas, stayTimes, session, target, interval, repeat,
                        enableSL, slValue, enablePhase, tempPowerValue);
                }
                else
                {
                    // 使用原始格式（向后兼容）
                    _currentCommandHasPhase = false; // 原始格式不支持相位

                    response = _readerManager.FastSwitchAntennaInventory(
                        antennas, stayTimes, session, target, interval, repeat);
                }

                if (response != null)
                {
                    CommandStatus = "正在接收数据...";
                    CommandStatusColor = Brushes.Blue;
                    AddLog("📡 命令发送成功，开始持续接收数据...");

                    // 开始持续接收数据
                    await _readerManager.StartContinuousReceiveAsync(0x8A, 3);
                }
                else
                {
                    AddLog("❌ 0x8A命令发送失败");
                    StopInventory(); // 使用StopInventory统一处理状态恢复
                    CommandStatus = "命令发送失败";
                    CommandStatusColor = Brushes.Red;
                }


            }
            catch (Exception ex)
            {
                AddLog($"❌ 快速天线切换异常: {ex.Message}");
                StopInventory(); // 使用StopInventory统一处理状态恢复
                CommandStatus = $"执行异常: {ex.Message}";
                CommandStatusColor = Brushes.Red;
            }

            CommandManager.InvalidateRequerySuggested();
        }

        private async Task ExecuteCustomSessionCommandAsync()
        {
            if (!_readerManager.IsConnected())
            {
                CommandStatus = "设备未连接";
                CommandStatusColor = Brushes.Red;
                AddLog("❌ 自定义会话盘存失败：设备未连接");
                IsReceiving = false;
                _isCommandRunning = false;
                return;
            }

            try
            {
                // 获取CustomSessionControl的参数
                var customSessionControl = CurrentParameterControl as Views.UserControls.CustomSessionControl;
                var customSessionViewModel = customSessionControl?.DataContext as ViewModels.CustomSessionViewModel;

                if (customSessionViewModel == null)
                {
                    CommandStatus = "参数获取失败";
                    CommandStatusColor = Brushes.Red;
                    AddLog("❌ 自定义会话盘存失败：无法获取参数配置");
                    IsReceiving = false;
                    _isCommandRunning = false;
                    return;
                }

                // 获取参数
                byte session = customSessionViewModel.GetSessionValue();
                byte target = customSessionViewModel.GetTargetValue();
                byte repeat = (byte)customSessionViewModel.RepeatCount;

                byte? sl = customSessionViewModel.UseSL ? customSessionViewModel.GetSLValue() : null;
                byte? phase = customSessionViewModel.EnablePhase ? customSessionViewModel.GetPhaseValue() : null;

                // 验证相位和SL的依赖关系
                if (customSessionViewModel.EnablePhase && !customSessionViewModel.UseSL)
                {
                    CommandStatus = "参数错误：启用相位需要同时启用SL";
                    CommandStatusColor = Brushes.Red;
                    AddLog("❌ 参数验证失败：启用相位检测需要同时启用SL参数");
                    IsReceiving = false;
                    _isCommandRunning = false;
                    return;
                }

                // 记录当前命令是否启用相位，用于后续数据解析
                _currentCommandHasPhase = customSessionViewModel.EnablePhase && customSessionViewModel.UseSL;

                AddLog($"🚀 盘存开始");

                // 执行0x8B命令（只发送，不等待响应）
                byte[]? response = _readerManager.CustomSessionInventory(session, target, repeat, sl, phase);

                if (response != null)
                {
                    CommandStatus = "正在接收数据...";
                    CommandStatusColor = Brushes.Blue;
                    AddLog("📡 命令发送成功，开始持续接收数据...");

                    // 开始持续接收数据
                    await _readerManager.StartContinuousReceiveAsync(0x8B, 3);
                }
                else
                {
                    AddLog("❌ 0x8B命令发送失败");
                    StopInventory(); // 使用StopInventory统一处理状态恢复
                    CommandStatus = "命令发送失败";
                    CommandStatusColor = Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 自定义会话盘存异常: {ex.Message}");
                StopInventory(); // 使用StopInventory统一处理状态恢复
                CommandStatus = $"执行异常: {ex.Message}";
                CommandStatusColor = Brushes.Red;
            }
        }

        private void AddSampleTagData()
        {
            var random = new Random();
            var sampleEpc = $"E2003412005F02A00000{random.Next(1000, 9999)}";

            var tagData = new TagData
            {
                Index = TagDataCollection.Count + 1,
                Epc = sampleEpc,
                PC = "3000",
                Rssi = $"-{random.Next(30, 60)} dBm",
                Antenna = $"天线{random.Next(1, 9)}",
                FreqPoint = random.Next(0, 63).ToString(),
                Phase = random.Next(0, 2) == 1 ? random.Next(0, 65535).ToString("X4") : "",
                Count = 1,
                LastSeen = DateTime.Now.ToString("HH:mm:ss.fff"),
                RawFreqAnt = (byte)random.Next(0, 255)
            };

            TagDataCollection.Add(tagData);
        }

        /// <summary>
        /// 解析RFID响应数据并添加到TagDataCollection
        /// 根据协议V3.8，0x8B命令响应格式: Head|Len|Address|Cmd|FreqAnt|PC|EPC|RSSI|Check
        /// </summary>
        /// <param name="response">RFID设备响应数据</param>
        private void ParseAndAddTagData(byte[]? response)
        {
            if (response == null || response.Length < 9)
            {
                AddLog("⚠️ 收到无效的标签数据响应");
                return;
            }

            try
            {
                // 根据协议V3.8，响应格式: Head|Len|Address|Cmd|FreqAnt|PC|EPC|RSSI|Check
                // 检查是否为命令完成响应
                if (response.Length == 5 && response[1] == 0x04)
                {
                    // 这是命令完成响应，不是标签数据
                    byte statusCode = response[4];
                    string statusMsg = _readerManager.ParseErrorCode(statusCode);
                    AddLog($"📋 命令执行完成: {statusMsg} (0x{statusCode:X2})");

                    // 如果是成功状态，说明命令执行完成但没有读到标签
                    if (statusCode == 0x10)
                    {
                        AddLog("ℹ️ 盘存完成，未发现标签");
                    }
                    else
                    {
                        AddLog($"⚠️ 命令执行异常: {statusMsg}");
                    }
                    return;
                }

                // 检查是否为6字节的命令完成响应 (某些命令可能返回6字节)
                if (response.Length == 6 && response[1] == 0x04)
                {
                    byte statusCode = response[4];
                    string statusMsg = _readerManager.ParseErrorCode(statusCode);
                    AddLog($"📋 命令执行完成: {statusMsg} (0x{statusCode:X2})");

                    if (statusCode == 0x10)
                    {
                        AddLog("ℹ️ 盘存完成，未发现标签");
                    }
                    else
                    {
                        AddLog($"⚠️ 命令执行异常: {statusMsg}");
                    }
                    return;
                }

                if (response.Length < 9) // 最少: Head+Len+Addr+Cmd+FreqAnt+PC(2)+EPC(至少1字节)+RSSI+Check
                {
                    AddLog("⚠️ 标签数据长度不足");
                    return;
                }

                // 解析各个字段
                int pos = 4; // 跳过Head+Len+Address+Cmd

                // FreqAnt (1字节)
                byte freqAnt = response[pos++];

                // PC (2字节)
                byte[] pcBytes = new byte[2];
                pcBytes[0] = response[pos++];
                pcBytes[1] = response[pos++];
                string pc = BitConverter.ToString(pcBytes).Replace("-", "");

                // 从PC字段计算EPC长度
                int epcLengthWords = (pcBytes[0] >> 3) & 0x1F; // PC高5位表示EPC长度(字)
                int epcLengthBytes = epcLengthWords * 2;

                // 根据实际响应数据判断是否包含相位
                // 无相位: Head+Len+Addr+Cmd+FreqAnt+PC+EPC+RSSI+Check
                // 带相位: Head+Len+Addr+Cmd+FreqAnt+PC+EPC+RSSI+Phase+Check
                int expectedLengthNoPhase = 4 + 1 + 2 + epcLengthBytes + 1 + 1; // 19字节 (对于10字节EPC)
                int expectedLengthWithPhase = expectedLengthNoPhase + 2; // 21字节 (对于10字节EPC)

                // 根据实际长度判断是否包含相位数据
                bool actualHasPhase = false;
                if (response.Length == expectedLengthWithPhase)
                {
                    actualHasPhase = true;
                }
                else if (response.Length == expectedLengthNoPhase)
                {
                    actualHasPhase = false;
                }
                else
                {
                    AddLog($"⚠️ 数据长度异常，期望{expectedLengthNoPhase}字节(无相位)或{expectedLengthWithPhase}字节(带相位)，实际{response.Length}字节");
                    AddLog($"📊 PC字段: {pc}, EPC长度: {epcLengthBytes}字节");
                    return;
                }

                // 解析EPC
                byte[] epcBytes = new byte[epcLengthBytes];
                Array.Copy(response, pos, epcBytes, 0, epcLengthBytes);
                string epc = BitConverter.ToString(epcBytes).Replace("-", "");
                pos += epcLengthBytes;

                // 解析RSSI
                byte rssi = response[pos++];

                // 解析相位 (根据实际数据判断)
                string phaseValue = "";
                if (actualHasPhase)
                {
                    byte[] phaseBytes = new byte[2];
                    phaseBytes[0] = response[pos++];
                    phaseBytes[1] = response[pos++];
                    ushort phase = (ushort)((phaseBytes[0] << 8) | phaseBytes[1]);
                    phaseValue = phase.ToString("X4");
                }

                // 解析天线号和频点 (从FreqAnt字段)
                int antennaNumber = (freqAnt & 0x03) + 1; // 低2位 + 1 (天线1-8)
                int freqPoint = (freqAnt >> 2) & 0x3F;    // 高6位 (频点0-63)

                // 转换RSSI值 (根据设备规格，可能需要调整)
                int rssiDbm = rssi > 128 ? rssi - 256 : rssi; // 有符号转换

                // 检查是否已存在相同EPC的标签
                var existingTag = TagDataCollection.FirstOrDefault(t => t.Epc == epc);
                if (existingTag != null)
                {
                    // 更新现有标签的计数和时间
                    existingTag.Count++;
                    existingTag.LastSeen = DateTime.Now.ToString("HH:mm:ss.fff");
                    existingTag.Rssi = $"{rssiDbm} dBm";
                    AddLog($"🔄 更新标签: EPC={epc.Substring(0, Math.Min(8, epc.Length))}..., 计数={existingTag.Count}");
                }
                else
                {
                    // 添加新标签
                    var tagData = new TagData
                    {
                        Index = TagDataCollection.Count + 1,
                        Epc = epc,
                        PC = pc,
                        Rssi = $"{rssiDbm} dBm",
                        Antenna = $"天线{antennaNumber}",
                        FreqPoint = freqPoint.ToString(),
                        Phase = phaseValue, // 使用解析出的相位值
                        Count = 1,
                        LastSeen = DateTime.Now.ToString("HH:mm:ss.fff"),
                        RawFreqAnt = freqAnt
                    };

                    TagDataCollection.Add(tagData);

                    // 根据实际是否有相位显示不同的日志
                    if (actualHasPhase && !string.IsNullOrEmpty(phaseValue))
                    {
                        AddLog($"📡 发现新标签: EPC={epc}, 天线={antennaNumber}, RSSI={rssiDbm}dBm, 频点={freqPoint}, 相位=0x{phaseValue}");
                    }
                    else
                    {
                        AddLog($"📡 发现新标签: EPC={epc}, 天线={antennaNumber}, RSSI={rssiDbm}dBm, 频点={freqPoint}");
                    }
                }

                // 添加详细的解析日志
                string phaseInfo = actualHasPhase ? $", 相位={phaseValue}" : "";
                string commandPhaseStatus = _currentCommandHasPhase ? "启用" : "禁用";
                string actualPhaseStatus = actualHasPhase ? "包含" : "不包含";
                AddLog($"📊 解析详情: PC={pc}, EPC长度={epcLengthBytes}字节, FreqAnt=0x{freqAnt:X2}{phaseInfo}");
                AddLog($"📊 相位状态: 命令{commandPhaseStatus}相位, 响应{actualPhaseStatus}相位数据");
            }
            catch (Exception ex)
            {
                AddLog($"❌ 解析标签数据异常: {ex.Message}");
                AddLog($"📊 原始数据: {BitConverter.ToString(response)}");
                // 如果解析失败，不添加模拟数据，保持真实性
            }
        }

        private void AddLog(string message)
        {
            _addLogAction?.Invoke(message);
        }

        /// <summary>
        /// 显示标签过滤界面
        /// </summary>
        private void ShowTagFilterView()
        {
            try
            {
                AddLog("🔧 切换到标签过滤设置界面");
                IsTagFilterVisible = true;

                // 自动查询当前掩码，显示在界面上
                ExecuteQueryFilter();
            }
            catch (Exception ex)
            {
                AddLog($"❌ 切换到标签过滤界面失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 返回到标签数据展示界面
        /// </summary>
        private void ExecuteReturnToDataView()
        {
            try
            {
                AddLog("🔙 返回到标签数据展示界面");
                IsTagFilterVisible = false;
            }
            catch (Exception ex)
            {
                AddLog($"❌ 返回数据界面失败: {ex.Message}");
            }
        }

        #region 标签过滤方法

        /// <summary>
        /// 初始化标签过滤选项
        /// </summary>
        private void InitializeTagFilterOptions()
        {
            try
            {
                // 掩码ID选项
                MaskIdOptions.Clear();
                for (int i = 1; i <= 5; i++)
                {
                    MaskIdOptions.Add($"Mask No.{i}");
                }

                // Session ID选项
                SessionIdOptions.Clear();
                SessionIdOptions.Add("S0");
                SessionIdOptions.Add("S1");
                SessionIdOptions.Add("S2");
                SessionIdOptions.Add("S3");
                SessionIdOptions.Add("SL");

                // 过滤行为选项
                ActionOptions.Clear();
                for (int i = 0; i <= 7; i++)
                {
                    ActionOptions.Add($"{i:D2}");
                }

                // 存储区选项
                MemBankOptions.Clear();
                MemBankOptions.Add("保留区");
                MemBankOptions.Add("EPC");
                MemBankOptions.Add("TID");
                MemBankOptions.Add("USER");

                // 清除掩码ID选项
                ClearMaskIdOptions.Clear();
                ClearMaskIdOptions.Add("Mask ALL");
                for (int i = 1; i <= 5; i++)
                {
                    ClearMaskIdOptions.Add($"Mask No.{i}");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 初始化标签过滤选项失败: {ex.Message}");
            }
        }

        private bool ValidateHexInput(string input)
        {
            // 允许十六进制字符和空格
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[0-9A-Fa-f\s]*$");
        }

        private string CalculateMaskLength(string hexValue)
        {
            if (string.IsNullOrWhiteSpace(hexValue))
                return "0";

            // 移除空格和非十六进制字符
            string cleanHex = System.Text.RegularExpressions.Regex.Replace(
                hexValue.ToUpper(), @"[^0-9A-F]", "");

            // 每个十六进制字符代表4位
            int bitLength = cleanHex.Length * 4;
            return bitLength.ToString();
        }

        private TagMask CreateTagMaskFromInput()
        {
            var mask = new TagMask();

            // 解析掩码ID
            if (SelectedMaskId.StartsWith("Mask No."))
            {
                mask.MaskId = int.Parse(SelectedMaskId.Substring(8));
            }

            // 解析Target
            mask.Target = SelectedSessionId switch
            {
                "S0" => MaskTarget.InventoriedS0,
                "S1" => MaskTarget.InventoriedS1,
                "S2" => MaskTarget.InventoriedS2,
                "S3" => MaskTarget.InventoriedS3,
                "SL" => MaskTarget.SL,
                _ => MaskTarget.InventoriedS0
            };

            // 解析Action
            if (byte.TryParse(SelectedAction, out byte actionValue))
            {
                mask.Action = (MaskAction)actionValue;
            }

            // 解析MemBank
            mask.MemBank = SelectedMemBank switch
            {
                "保留区" => MemoryBank.Reserved,
                "EPC" => MemoryBank.EPC,
                "TID" => MemoryBank.TID,
                "USER" => MemoryBank.USER,
                _ => MemoryBank.EPC
            };

            // 解析起始地址
            if (uint.TryParse(StartAddress, out uint startAddr))
            {
                mask.StartingAddress = startAddr;
            }

            // 设置掩码值
            mask.MaskValueHex = MaskValue;

            return mask;
        }



        private void ExecuteSetFilter()
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(MaskValue))
                {
                    AddLog("⚠️ 请输入过滤值");
                    return;
                }

                if (string.IsNullOrWhiteSpace(StartAddress))
                {
                    AddLog("⚠️ 请输入起始地址");
                    return;
                }

                if (!uint.TryParse(StartAddress, out _))
                {
                    AddLog("⚠️ 起始地址格式不正确，请输入数字");
                    return;
                }

                // 验证过滤值是否为有效的十六进制
                if (!ValidateHexInput(MaskValue) || MaskValue.Trim().Length == 0)
                {
                    AddLog("⚠️ 过滤值格式不正确，请输入有效的十六进制值");
                    return;
                }

                var mask = CreateTagMaskFromInput();
                var response = _readerManager.SetTagMask(mask);

                if (response != null)
                {
                    AddLog("✅ 标签掩码设置成功");
                    // 自动刷新列表
                    ExecuteQueryFilter();
                }
                else
                {
                    AddLog("❌ 标签掩码设置失败");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 设置标签掩码异常: {ex.Message}");
            }
        }

        private void ExecuteClearFilter()
        {
            try
            {
                int maskId = 0;
                if (SelectedClearMaskId.StartsWith("Mask No."))
                {
                    maskId = int.Parse(SelectedClearMaskId.Substring(8));
                }

                var response = _readerManager.ClearTagMask(maskId);

                if (response != null)
                {
                    AddLog("✅ 标签掩码清除成功");
                    // 自动刷新列表
                    ExecuteQueryFilter();
                }
                else
                {
                    AddLog("❌ 标签掩码清除失败");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 清除标签掩码异常: {ex.Message}");
            }
        }

        private void ExecuteQueryFilter()
        {
            try
            {
                var masks = _readerManager.QueryTagMasks();

                MaskList.Clear(); // 先清空列表

                if (masks != null && masks.Count > 0)
                {
                    foreach (var mask in masks)
                    {
                        MaskList.Add(TagMaskInfo.FromTagMask(mask));
                    }

                    AddLog($"✅ 查询标签掩码成功，共 {masks.Count} 个");
                }
                else
                {
                    AddLog("📋 当前无设置的标签掩码");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 查询标签掩码异常: {ex.Message}");
            }
        }

        #endregion



        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 处理标签数据接收事件（批量更新模式）
        /// </summary>
        private void OnTagDataReceived(object? sender, TagDataEventArgs e)
        {
            lock (_pendingTagUpdates)
            {
                _pendingTagUpdates.Add(e);

                // 检查是否超过最大待处理数量
                if (_pendingTagUpdates.Count > _performanceOptimizer.MaxPendingUpdates)
                {
                    // 强制处理一批数据
                    BatchUpdateUI(null, EventArgs.Empty);
                }

                // 启动定时器（如果还没启动）
                if (!_updateTimer.IsEnabled)
                {
                    // 动态调整定时器间隔
                    _updateTimer.Interval = TimeSpan.FromMilliseconds(_performanceOptimizer.BatchUpdateInterval);
                    _updateTimer.Start();
                }
            }
        }

        /// <summary>
        /// 高性能批量更新UI（定时器触发）
        /// </summary>
        private void BatchUpdateUI(object? sender, EventArgs e)
        {
            List<TagDataEventArgs> updatesToProcess;

            lock (_pendingTagUpdates)
            {
                if (_pendingTagUpdates.Count == 0)
                {
                    _updateTimer.Stop();
                    return;
                }

                updatesToProcess = new List<TagDataEventArgs>(_pendingTagUpdates);
                _pendingTagUpdates.Clear();
            }

            // 在后台线程预处理数据，减少UI线程工作量
            Task.Run(() =>
            {
                try
                {
                    _batchUpdateCache.Clear();
                    var newTags = new List<TagData>();

                    // 预处理：分组和去重
                    var groupedUpdates = updatesToProcess
                        .GroupBy(t => t.EPC)
                        .ToDictionary(g => g.Key, g => g.OrderByDescending(t => t.Timestamp).First());

                    foreach (var kvp in groupedUpdates)
                    {
                        var epc = kvp.Key;
                        var latestData = kvp.Value;

                        if (_epcDictionary.TryGetValue(epc, out TagData? existingTag))
                        {
                            // 更新现有标签
                            var updateCount = updatesToProcess.Count(u => u.EPC == epc);
                            existingTag.Count += updateCount;
                            existingTag.LastSeen = latestData.Timestamp.ToString("HH:mm:ss.fff");
                            existingTag.Rssi = latestData.RSSI.ToString();
                            _batchUpdateCache.Add(existingTag);
                        }
                        else
                        {
                            // 新标签
                            var newTag = new TagData
                            {
                                Index = _epcDictionary.Count + newTags.Count + 1,
                                Epc = latestData.EPC,
                                PC = $"0x{latestData.PC:X4}",
                                Rssi = latestData.RSSI.ToString(),
                                Antenna = (latestData.Antenna + 1).ToString(),
                                FreqPoint = latestData.Frequency.ToString(),
                                Phase = latestData.Phase?.ToString() ?? "",
                                Count = updatesToProcess.Count(u => u.EPC == epc),
                                LastSeen = latestData.Timestamp.ToString("HH:mm:ss.fff"),
                                RawFreqAnt = (byte)((latestData.Frequency << 2) | latestData.Antenna)
                            };

                            _epcDictionary[epc] = newTag;
                            newTags.Add(newTag);
                        }
                    }

                    // 在UI线程中快速更新界面
                    System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
                    {
                        try
                        {
                            // 批量添加新标签
                            foreach (var newTag in newTags)
                            {
                                TagDataCollection.Add(newTag);
                            }

                            // 触发现有标签的属性更新通知
                            foreach (var updatedTag in _batchUpdateCache)
                            {
                                // 这里可以触发特定属性的更新通知，但ObservableCollection会自动处理
                            }

                            // 检查是否需要继续定时器
                            lock (_pendingTagUpdates)
                            {
                                if (_pendingTagUpdates.Count == 0)
                                {
                                    _updateTimer.Stop();
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            AddLog($"❌ UI更新异常: {ex.Message}");
                        }
                    });
                }
                catch (Exception ex)
                {
                    AddLog($"❌ 批量处理异常: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 处理单个标签数据
        /// </summary>
        private void ProcessSingleTagData(TagDataEventArgs e)
        {
            // 检查EPC是否已存在
            if (_epcDictionary.TryGetValue(e.EPC, out TagData? existingTag))
            {
                // EPC已存在，累加读取次数
                existingTag.Count++;
                existingTag.LastSeen = e.Timestamp.ToString("HH:mm:ss.fff");

                // 更新RSSI（取最新值）
                existingTag.Rssi = e.RSSI.ToString();

                // 标签更新日志只在串口日志开启时显示
                if (ShowSerialLogs)
                {
                    AddLog($"🔄 更新标签: EPC={e.EPC}, 读取次数={existingTag.Count}, 天线={e.Antenna + 1}");
                }
            }
            else
            {
                // 新标签，添加到集合
                var newTag = new TagData
                {
                    Index = TagDataCollection.Count + 1,
                    Epc = e.EPC,
                    PC = $"0x{e.PC:X4}",
                    Rssi = e.RSSI.ToString(),
                    Antenna = (e.Antenna + 1).ToString(), // 显示为1-8
                    FreqPoint = e.Frequency.ToString(),
                    Phase = e.Phase?.ToString() ?? "",
                    Count = 1,
                    LastSeen = e.Timestamp.ToString("HH:mm:ss.fff"),
                    RawFreqAnt = (byte)((e.Frequency << 2) | e.Antenna)
                };

                _epcDictionary[e.EPC] = newTag;
                TagDataCollection.Add(newTag);

                // 新标签日志只在串口日志开启时显示
                if (ShowSerialLogs)
                {
                    string phaseInfo = e.Phase.HasValue ? $", 相位={e.Phase.Value}" : "";
                    AddLog($"🆕 新标签: EPC={e.EPC}, 天线={e.Antenna + 1}, RSSI={e.RSSI}{phaseInfo}");
                }
            }
        }

        /// <summary>
        /// 更新盘点计时器显示
        /// </summary>
        private void UpdateInventoryDuration(object? sender, EventArgs e)
        {
            if (_isReceiving)
            {
                var currentDuration = DateTime.Now - _inventoryStartTime;
                InventoryDuration = currentDuration.ToString(@"mm\:ss\.fff");

                // 同时更新发现标签数量
                DiscoveredTagCount = TagDataCollection.Count.ToString();
            }
        }

        /// <summary>
        /// 更新性能状态
        /// </summary>
        private void UpdatePerformanceStatus(object? sender, EventArgs e)
        {
            try
            {
                _performanceOptimizer.OptimizeIfNeeded();
                PerformanceStatus = _performanceOptimizer.GetPerformanceReport();

                // 始终显示性能监控（不仅仅在盘点过程中）
                ShowPerformanceMonitor = true;
            }
            catch (Exception ex)
            {
                PerformanceStatus = $"性能监控异常: {ex.Message}";
                ShowPerformanceMonitor = true; // 即使出错也显示错误信息
            }
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        private void ResetStatistics()
        {
            InventoryDuration = "00:00.000";
            TotalReadCount = "-";
            ReadRate = "-";
            DiscoveredTagCount = "0";
        }

        /// <summary>
        /// 解析命令完成消息中的统计信息
        /// </summary>
        private void ParseCommandCompletionMessage(string message)
        {
            try
            {
                // 解析消息格式：
                // 0x8A: "命令完成，读取1234次，耗时5678ms"
                // 0x8B: "命令完成，天线1读取1234次，速率156"

                if (message.Contains("读取") && message.Contains("次"))
                {
                    // 提取读取次数
                    var readMatch = System.Text.RegularExpressions.Regex.Match(message, @"读取(\d+)次");
                    if (readMatch.Success)
                    {
                        TotalReadCount = readMatch.Groups[1].Value;
                    }
                }

                if (message.Contains("速率"))
                {
                    // 提取速率信息（0x8B命令）
                    var rateMatch = System.Text.RegularExpressions.Regex.Match(message, @"速率(\d+)");
                    if (rateMatch.Success)
                    {
                        ReadRate = rateMatch.Groups[1].Value + "/s";
                    }
                }
                else if (TotalReadCount != "-" && InventoryDuration != "00:00.000")
                {
                    // 计算速率（0x8A命令或其他情况）
                    if (int.TryParse(TotalReadCount, out int totalReads))
                    {
                        var duration = DateTime.Now - _inventoryStartTime;
                        if (duration.TotalSeconds > 0)
                        {
                            var rate = (int)(totalReads / duration.TotalSeconds);
                            ReadRate = rate + "/s";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 解析统计信息异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理命令完成事件
        /// </summary>
        private void OnCommandCompleted(object? sender, CommandCompletedEventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                // 停止批量更新定时器
                _updateTimer.Stop();

                // 处理剩余的待更新数据
                lock (_pendingTagUpdates)
                {
                    if (_pendingTagUpdates.Count > 0)
                    {
                        foreach (var tagData in _pendingTagUpdates)
                        {
                            ProcessSingleTagData(tagData);
                        }
                        _pendingTagUpdates.Clear();
                    }
                }

                // 自动停止盘点
                StopInventory();

                if (e.IsSuccess)
                {
                    CommandStatus = "命令完成";
                    CommandStatusColor = Brushes.Green;
                    AddLog($"✅ 盘存结束");

                    // 解析并更新统计信息
                    ParseCommandCompletionMessage(e.Message);
                    DiscoveredTagCount = TagDataCollection.Count.ToString();

                    // 显示统计信息（保留原有逻辑）
                    InventoryStatistics = $"📊 {e.Message} | 发现标签: {TagDataCollection.Count}个";
                    ShowStatistics = true;
                }
                else
                {
                    CommandStatus = $"命令失败: {e.Message}";
                    CommandStatusColor = Brushes.Red;
                    AddLog($"❌ 盘存失败: {e.Message}");

                    // 隐藏统计信息
                    ShowStatistics = false;
                }
            });
        }

        /// <summary>
        /// 处理接收超时事件
        /// </summary>
        private void OnReceiveTimeout(object? sender, ReceiveTimeoutEventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                // 超时自动停止盘点
                StopInventory();

                CommandStatus = "接收超时";
                CommandStatusColor = Brushes.Orange;
                AddLog($"⏰ {e.Message}");
            });
        }


    }
}
