<Page x:Class="RFID_UI.Views.Pages.ReaderSettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      xmlns:local="clr-namespace:RFID_UI.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="1000"
      Title="读写器设置">

    <Grid Margin="20">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="35*"/>
            <ColumnDefinition Width="65*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧控制面板 -->
        <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
            <StackPanel MinWidth="280" MaxWidth="450">
            
            <!-- 串口连接 -->
            <ui:Card Margin="2,2,2,2">
                <StackPanel Margin="20">
                    <ui:TextBlock Text="串口连接" FontSize="14" Margin="2,2,2,2"/>

                    <Grid Margin="2,2,2,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ComboBox x:Name="ComPortComboBox"
                                 Grid.Column="0"
                                 ItemsSource="{Binding AvailablePorts}"
                                 SelectedItem="{Binding SelectedPort}"
                                 FontSize="14"
                                 Margin="2,2,2,2"/>

                        <ui:Button Grid.Column="1"
                                  Content="刷新"
                                  Icon="{ui:SymbolIcon Symbol=ArrowClockwise24}"
                                  Command="{Binding RefreshPortsCommand}"
                                  FontSize="14"/>
                    </Grid>

                    <ui:TextBlock Text="{Binding ConnectionStatus}"
                                 FontSize="14"
                                 Foreground="{Binding ConnectionStatusColor}"
                                 Margin="2,2,2,2"/>

                    <StackPanel Orientation="Horizontal">
                        <ui:Button Content="连接"
                                  Icon="{ui:SymbolIcon Symbol=PlugConnected24}"
                                  Command="{Binding ConnectCommand}"
                                  Appearance="Primary"
                                  FontSize="14"
                                  Margin="2,2,2,2"/>
                        <ui:Button Content="断开"
                                  Icon="{ui:SymbolIcon Symbol=PlugDisconnected24}"
                                  Command="{Binding DisconnectCommand}"
                                  FontSize="14"/>
                    </StackPanel>
                </StackPanel>
            </ui:Card>

            <!-- 读写器属性设置 -->
            <ui:Card Margin="2,2,2,2">
                <StackPanel Margin="20">
                    <ui:TextBlock Text="读写器属性设置" FontSize="14" Margin="2,2,2,2"/>

                    <!-- 读写器地址 -->
                    <Grid Margin="2,2,2,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ui:TextBlock Grid.Column="0" Text="读写器地址:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                        <ui:TextBox Grid.Column="1"
                                   Text="{Binding ReaderAddress}"
                                   FontSize="14"
                                   Margin="2,2,2,2"/>
                        <ui:Button Grid.Column="2" Content="设置" Command="{Binding SetAddressCommand}" FontSize="14"/>
                    </Grid>
                    

                    <!-- 功率设置 (统一设置所有天线) -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ui:TextBlock Grid.Column="0" Text="功率(dBm):" VerticalAlignment="Center" Margin="2,2,2,2"/>
                        <ui:TextBox Grid.Column="1"
                                   Text="{Binding PowerLevel}"
                                   FontSize="14"
                                   Margin="2,2,2,2"/>
                        <ui:Button Grid.Column="2" Content="设置" Command="{Binding SetPowerCommand}" FontSize="14" Margin="2,2,2,2"/>
                        <ui:Button Grid.Column="3" Content="读取" Command="{Binding GetPowerCommand}" FontSize="14"/>
                    </Grid>
                    
                    <!-- 蜂鸣器设置 -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ui:TextBlock Grid.Column="0" Text="蜂鸣器:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                        <ComboBox Grid.Column="1"
                                 ItemsSource="{Binding BuzzerOptions}"
                                 SelectedItem="{Binding SelectedBuzzer}"
                                 FontSize="14"
                                 Margin="2,2,2,2"/>
                        <ui:Button Grid.Column="2" Content="设置" Command="{Binding SetBuzzerCommand}" FontSize="14"/>
                    </Grid>
                    
                    <!-- 操作按钮 -->
                    <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                        <ui:Button Content="复位读写器"
                                  Icon="{ui:SymbolIcon Symbol=ArrowReset24}"
                                  Command="{Binding ResetCommand}"
                                  Appearance="Caution"
                                  FontSize="14"
                                  Margin="2,2,2,2"/>
                        <ui:Button Content="获取固件版本号"
                                  Icon="{ui:SymbolIcon Symbol=Info24}"
                                  Command="{Binding GetVersionCommand}"
                                  FontSize="14"/>
                    </StackPanel>
                </StackPanel>
            </ui:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 右侧设备信息面板 -->
        <ui:Card Grid.Column="1" Margin="20,0,0,0">
            <StackPanel Margin="20">
                <ui:TextBlock Text="设备信息" FontSize="14" Margin="2,2,2,2"/>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <ui:InfoBar Grid.Row="0" 
                               Title="连接状态"
                               Message="{Binding DetailedConnectionStatus}"
                               Severity="{Binding ConnectionSeverity}"
                               IsOpen="True"
                               Margin="2,2,2,2"/>
                    
                    <StackPanel Grid.Row="1">
                        <ui:TextBlock Text="设备版本信息:" FontWeight="SemiBold" Margin="0,0,0,10"/>
                        <ui:TextBlock Text="{Binding DeviceVersion}" FontFamily="Consolas"/>
                    </StackPanel>

                    <StackPanel Grid.Row="2" Margin="0,15,0,0">
                        <ui:TextBlock Text="当前配置:" FontWeight="SemiBold"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <ui:TextBlock Grid.Row="0" Grid.Column="0" Text="地址:" Margin="2,2,2,2"/>
                            <ui:TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentAddress}" FontFamily="Consolas"/>

                            <ui:TextBlock Grid.Row="1" Grid.Column="0" Text="天线:" Margin="2,2,2,2"/>
                            <ui:TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding CurrentAntenna}" FontFamily="Consolas"/>

                            <ui:TextBlock Grid.Row="2" Grid.Column="0" Text="功率:" Margin="2,2,2,2"/>
                            <ui:TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CurrentPower}" FontFamily="Consolas"/>

                            <ui:TextBlock Grid.Row="3" Grid.Column="0" Text="蜂鸣器:" Margin="2,2,2,2"/>
                            <ui:TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding CurrentBuzzer}" FontFamily="Consolas"/>
                        </Grid>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </ui:Card>
    </Grid>
</Page>
