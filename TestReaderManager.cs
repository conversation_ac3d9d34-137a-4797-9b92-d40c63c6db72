using System;
using System.Text;

namespace RFID_UI
{
    /// <summary>
    /// 简单的测试类，用于验证ReaderManager的核心功能
    /// </summary>
    public class TestReaderManager
    {
        public static void RunBasicTests()
        {
            Console.WriteLine("=== RFID Reader Manager 基础功能测试 ===");
            
            // 测试1: 校验和计算测试
            TestChecksumCalculation();
            
            // 测试2: 数据包构建测试
            TestPacketBuilding();
            
            // 测试3: 错误码解析测试
            TestErrorCodeParsing();
            
            // 测试4: 响应成功判断测试
            TestResponseSuccessCheck();
            
            Console.WriteLine("=== 测试完成 ===");
        }
        
        private static void TestChecksumCalculation()
        {
            Console.WriteLine("\n--- 测试1: 校验和计算 ---");
            
            // 创建一个测试用的ReaderManager实例
            var reader = new ReaderManager("COM1"); // 不需要真实连接
            
            // 使用反射访问私有方法进行测试
            var method = typeof(ReaderManager).GetMethod("CalculateChecksum", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (method != null)
            {
                // 测试已知的校验和计算
                byte[] testData = { 0xA0, 0x03, 0x01, 0x70 }; // Reset命令
                object? result = method.Invoke(reader, new object[] { testData });
                byte checksum = result != null ? (byte)result : (byte)0;
                
                Console.WriteLine($"测试数据: {BitConverter.ToString(testData)}");
                Console.WriteLine($"计算校验和: 0x{checksum:X2}");
                
                // 验证校验和算法：8位取模求补码
                byte expectedSum = 0;
                foreach (byte b in testData)
                {
                    expectedSum += b;
                }
                byte expectedChecksum = (byte)(~expectedSum + 1);
                
                Console.WriteLine($"期望校验和: 0x{expectedChecksum:X2}");
                Console.WriteLine($"校验和测试: {(checksum == expectedChecksum ? "✅ 通过" : "❌ 失败")}");
            }
            else
            {
                Console.WriteLine("❌ 无法访问CalculateChecksum方法");
            }
        }
        
        private static void TestPacketBuilding()
        {
            Console.WriteLine("\n--- 测试2: 数据包构建 ---");
            
            var reader = new ReaderManager("COM1");
            
            // 使用反射访问私有方法
            var method = typeof(ReaderManager).GetMethod("BuildPacket", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (method != null)
            {
                // 测试复位命令数据包构建
                byte[] packet = (byte[])method.Invoke(reader, new object[] { (byte)0x01, (byte)0x70, null })!;
                
                Console.WriteLine($"复位命令数据包: {BitConverter.ToString(packet)}");
                
                // 验证数据包格式: Head(0xA0) + Len + Address + Cmd + Check
                bool isValid = packet.Length == 5 && 
                              packet[0] == 0xA0 && 
                              packet[1] == 0x03 && 
                              packet[2] == 0x01 && 
                              packet[3] == 0x70;
                
                Console.WriteLine($"数据包格式验证: {(isValid ? "✅ 通过" : "❌ 失败")}");
                
                // 测试带数据的命令
                byte[] data = { 0x01 }; // 蜂鸣器模式数据
                byte[] packet2 = (byte[])method.Invoke(reader, new object[] { (byte)0x01, (byte)0x7A, data })!;
                
                Console.WriteLine($"蜂鸣器命令数据包: {BitConverter.ToString(packet2)}");
                
                bool isValid2 = packet2.Length == 6 && 
                               packet2[0] == 0xA0 && 
                               packet2[1] == 0x04 && 
                               packet2[2] == 0x01 && 
                               packet2[3] == 0x7A && 
                               packet2[4] == 0x01;
                
                Console.WriteLine($"带数据命令验证: {(isValid2 ? "✅ 通过" : "❌ 失败")}");
            }
            else
            {
                Console.WriteLine("❌ 无法访问BuildPacket方法");
            }
        }
        
        private static void TestErrorCodeParsing()
        {
            Console.WriteLine("\n--- 测试3: 错误码解析 ---");
            
            var reader = new ReaderManager("COM1");
            
            // 测试已知错误码
            var testCases = new[]
            {
                (0x10, "Command Success"),
                (0x11, "Command Failed"),
                (0x80, "No Tag Response"),
                (0xFF, "Unknown Error Code: 0xFF")
            };
            
            foreach (var (errorCode, expected) in testCases)
            {
                string result = reader.ParseErrorCode((byte)errorCode);
                bool isCorrect = result == expected;
                Console.WriteLine($"错误码 0x{errorCode:X2}: {result} {(isCorrect ? "✅" : "❌")}");
            }
        }
        
        private static void TestResponseSuccessCheck()
        {
            Console.WriteLine("\n--- 测试4: 响应成功判断 ---");
            
            var reader = new ReaderManager("COM1");
            
            // 测试成功响应
            byte[] successResponse = { 0xA0, 0x04, 0x01, 0x70, 0x10, 0x7E }; // 假设的成功响应
            bool isSuccess1 = reader.IsResponseSuccess(successResponse);
            Console.WriteLine($"成功响应测试: {(isSuccess1 ? "✅ 通过" : "❌ 失败")}");
            
            // 测试失败响应
            byte[] failResponse = { 0xA0, 0x04, 0x01, 0x70, 0x11, 0x7D }; // 假设的失败响应
            bool isSuccess2 = reader.IsResponseSuccess(failResponse);
            Console.WriteLine($"失败响应测试: {(!isSuccess2 ? "✅ 通过" : "❌ 失败")}");
            
            // 测试null响应
            bool isSuccess3 = reader.IsResponseSuccess(null);
            Console.WriteLine($"空响应测试: {(!isSuccess3 ? "✅ 通过" : "❌ 失败")}");
            
            // 测试长响应（非错误格式）
            byte[] longResponse = { 0xA0, 0x06, 0x01, 0x72, 0x01, 0x02, 0x03, 0x04 }; // 假设的长响应
            bool isSuccess4 = reader.IsResponseSuccess(longResponse);
            Console.WriteLine($"长响应测试: {(isSuccess4 ? "✅ 通过" : "❌ 失败")}");
        }
    }
}
