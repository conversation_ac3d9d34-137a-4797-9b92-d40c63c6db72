**超高频读写器串行接口通讯协议 V3.8**



<a name="_toc118691636"></a>**超高频读写器串行接口通讯协议**

**UHF RFID Reader Serial Interface Protocol**

**V3.8**


**目录：**

[*1 通信协议结构	4******](#_toc523831742)***

[**1.1 RS232参数设置	4****](#_toc523831743)

[**1.2 数据包格式定义	4****](#_toc523831744)

[***1.2.1 上位机指令数据包格式定义	4******](#_toc523831745)

[***1.2.2***	***读写器返回数据包定义	4******](#_toc523831746)

[***2指令集定义	5******](#_toc523831747)

[**2.1 系统设置指令	7****](#_toc523831748)

[***2.1.1 cmd_reset	7******](#_toc523831749)

[***2.1.2 cmd_set_uart_baudrate	7******](#_toc523831750)

[***2.1.3 cmd_get_firmware_version	8******](#_toc523831751)

[***2.1.4 cmd_set_reader_address	9******](#_toc523831752)

[***2.1.5 cmd_set_work_antenna	10******](#_toc523831753)

[***2.1.6 cmd_get_work_antenna	11******](#_toc523831754)

[***2.1.7 cmd_set_output_power	12******](#_toc523831755)

[***2.1.8 cmd_get_output_power	13******](#_toc523831756)

[***2.1.9 cmd_set_frequency_region	13******](#_toc523831757)

[***2.1.10 cmd_get_frequency_region	15******](#_toc523831758)

[***2.1.11 cmd_set_beeper_mode	16******](#_toc523831759)

[***2.1.12 cmd_get_reader_temperature	17******](#_toc523831760)

[***2.1.13 cmd_read_gpio_value	18******](#_toc523831761)

[***2.1.14 cmd_write_gpio_value	18******](#_toc523831762)

[***2.1.15 cmd_set_ant_connection_detector	19******](#_toc523831763)

[***2.1.16 cmd_get_ant_connection_detector	19******](#_toc523831764)

[***2.1.17 cmd_set_temporary_output_power	20******](#_toc523831765)

[***2.1.18 cmd_set_reader_identifier	21******](#_toc523831766)

[***2.1.19 cmd_get_reader_identifier	22******](#_toc523831767)

[***2.1.20 cmd_set_rf_link_profile	23******](#_toc523831768)

[***2.1.21 cmd_get_rf_link_profile	24******](#_toc523831769)

[***2.1.22 cmd_get_rf_port_return_loss	25******](#_toc523831770)

[**2.2 18000-6C标签操作命令	26****](#_toc523831771)

<a name="_hlt523831801"></a>[***2.2.1 盘存指令合集	26******](#_toc523831772)

[********** cmd_inventory	26******](#_toc523831773)

[********** cmd_real_time_inventory	27******](#_toc523831774)

[***2.2.1.3cmd_customized_session_target_inventory	28******](#_toc523831775)

[********** cmd_fast_switch_ant_inventory	30******](#_toc523831776)

[***2.2.2 cmd_read	2******](#_toc523831777)

[***2.2.3 cmd_write	4******](#_toc523831778)

[***2.2.4 cmd_lock	5******](#_toc523831779)

[***2.2.5 cmd_kill	6******](#_toc523831780)

[***2.2.6 cmd_set_access_epc_match	7******](#_toc523831781)

[***2.2.7 cmd_get_access_epc_match	8******](#_toc523831782)

[***2.2.8 cmd_set_impinj_fast_tid	8******](#_toc523831783)

[***2.2.9 cmd_set_and_save_impinj_fast_tid	9******](#_toc523831784)

[***2.2.10 cmd_get_impinj_fast_tid	10******](#_toc523831785)

[**2.3 ISO 18000-6B 标签操作命令	10****](#_toc523831786)

[***2.3.1 cmd_iso18000_6b_inventory	10******](#_toc523831787)

[***2.3.2 cmd_iso18000_6b_read	11******](#_toc523831788)

[***2.3.3 cmd_iso18000_6b_write	12******](#_toc523831789)

[***2.3.4 cmd_iso18000_6b_lock	13******](#_toc523831790)

[***2.3.5 cmd_iso18000_6b_query_lock	14******](#_toc523831791)

[**2.4 缓存操作命令	15****](#_toc523831792)

[***2.4.1 cmd_get_inventory_buffer	15******](#_toc523831793)

[***2.4.2 cmd_get_and_reset_inventory_buffer	16******](#_toc523831794)

[***2.4.3 cmd_get_inventory_buffer_tag_count	16******](#_toc523831795)

[***2.4.4 cmd_reset_inventory_buffer	17******](#_toc523831796)

[***3错误代码表	17******](#_toc523831797)

[***4频率参数对应表	19******](#_toc523831798)

[***5 RSSI参数对应表	20******](#_toc523831799)

[***6 校验和计算方法(C语言描述)	21******](#_toc523831800)



















# <a name="_toc118691637"></a><a name="_toc328402766"></a><a name="_toc328402814"></a><a name="_toc328405840"></a><a name="_toc328407220"></a><a name="_toc328407263"></a><a name="_toc328407310"></a><a name="_toc328407735"></a><a name="_toc328407780"></a><a name="_toc328407825"></a><a name="_toc328407870"></a><a name="_toc523831742"></a>**1 通信协议结构**
此通信协议是上位机通过串行通信接口操作读写器的通信规范。

命令和响应数据由连续的字节流组成数据包，长度可变，并采用校验和方法进行检错。

## <a name="_toc118691638"></a><a name="_toc328402767"></a><a name="_toc328402815"></a><a name="_toc328405841"></a><a name="_toc328407221"></a><a name="_toc328407264"></a><a name="_toc328407311"></a><a name="_toc328407736"></a><a name="_toc328407781"></a><a name="_toc328407826"></a><a name="_toc328407871"></a><a name="_toc523831743"></a>**1.1 RS232参数设置**
物理接口符合RS-232规范要求。

1位起始位、8位数据位、1位停止位、无奇偶校验。

通信波特率设计为38400bps、115200bps可选。默认波特率为115200bps。

## <a name="_toc118691639"></a><a name="_toc328402768"></a><a name="_toc328402816"></a><a name="_toc328405842"></a><a name="_toc328407222"></a><a name="_toc328407265"></a><a name="_toc328407312"></a><a name="_toc328407737"></a><a name="_toc328407782"></a><a name="_toc328407827"></a><a name="_toc328407872"></a><a name="_toc523831744"></a>**1.2 数据包格式定义**
<a name="_toc328405843"></a><a name="_toc328407223"></a><a name="_toc328407266"></a><a name="_toc328407313"></a><a name="_toc328407738"></a><a name="_toc328407783"></a><a name="_toc328407828"></a><a name="_toc328407873"></a><a name="_toc523831745"></a>**1.2.1 上位机指令数据包格式定义**

<table><tr><th colspan="1"><b>Head</b></th><th colspan="1"><b>Len</b> </th><th colspan="1"><b>Address</b></th><th colspan="1"><b>Cmd</b> </th><th colspan="2"><b>Data</b></th><th colspan="1"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">1 Byte</td><td colspan="1">1 Byte</td><td colspan="1">1 Byte</td><td colspan="2">N Bytes</td><td colspan="1">1 Byte</td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="2" rowspan="6">参数说明</td><td colspan="3">Head</td><td colspan="2">数据包头，每包数据均以0xA0开始。</td></tr>
<tr><td colspan="3">Len</td><td colspan="2">数据包从Len后面开始的字节数，不包含Len本身。</td></tr>
<tr><td colspan="3">Address</td><td colspan="2">读写器地址。供RS-485接口串联时使用。一般地址从0～254(0xFE)，255（0xFF）为公用地址。读写器接收自身地址和公用地址的命令。</td></tr>
<tr><td colspan="3">Cmd</td><td colspan="2">命令码。</td></tr>
<tr><td colspan="3">Data</td><td colspan="2">命令参数。</td></tr>
<tr><td colspan="3">Check</td><td colspan="2">校验和，除校验和本身外所有字节的校验和。</td></tr>
</table>

2. <a name="_toc328405844"></a><a name="_toc328407224"></a><a name="_toc328407267"></a><a name="_toc328407314"></a><a name="_toc328407739"></a><a name="_toc328407784"></a><a name="_toc328407829"></a><a name="_toc328407874"></a><a name="_toc523831746"></a>**读写器返回数据包定义**

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b> </th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1" valign="top">0xA0</td><td colspan="1" valign="top">1 Byte</td><td colspan="1" valign="top">1 Byte</td><td colspan="1" valign="top">1 Byte</td><td colspan="1" valign="top">N Bytes</td><td colspan="1" valign="top">1 Byte</td></tr>
<tr><td colspan="6" valign="top"></td></tr>
<tr><td colspan="2" rowspan="6" valign="top"><p></p><p></p><p>参数说明</p></td><td colspan="1" valign="top">Head</td><td colspan="3" valign="top">数据包头，每包数据均以0xA0开始。</td></tr>
<tr><td colspan="1" valign="top">Len</td><td colspan="3" valign="top">数据包从Len后面开始的字节数，不包含Len本身。</td></tr>
<tr><td colspan="1" valign="top">Address</td><td colspan="3" valign="top">读写器自身的地址。</td></tr>
<tr><td colspan="1" valign="top">Cmd</td><td colspan="3">命令码。</td></tr>
<tr><td colspan="1" valign="top">Data</td><td colspan="3" valign="top">读写器返回的数据。</td></tr>
<tr><td colspan="1" valign="top">Check</td><td colspan="3" valign="top">校验和，除校验和本身外所有字节的校验和。</td></tr>
</table>



# <a name="_toc118691640"></a><a name="_toc328402769"></a><a name="_toc328402817"></a><a name="_toc328405845"></a><a name="_toc328407225"></a><a name="_toc328407268"></a><a name="_toc328407315"></a><a name="_toc328407740"></a><a name="_toc328407785"></a><a name="_toc328407830"></a><a name="_toc328407875"></a><a name="_toc523831747"></a>**2指令集定义**
**指令集一览表**

|**序号**|**命令码**|**名     称**|**描    述**|
| :-: | :-: | :-: | :-: |
|**读写器操作命令**||||
|1|**0x70**|**cmd\_reset**|复位读写器|
|2|**0x71**|**cmd\_set\_uart\_baudrate**|设置串口通讯波特率|
|3|**0x72**|**cmd\_get\_firmware\_version**|读取读写器固件版本|
|4|**0x73**|**cmd\_set\_reader\_address**|设置读写器地址|
|5|**0x74**|**cmd\_set\_work\_antenna**|设置读写器工作天线|
|6|**0x75**|**cmd\_get\_work\_antenna**|查询当前天线工作天线|
|7|**0x76**|**cmd\_set\_output\_power**|设置读写器射频输出功率|
|8|**0x77**|**cmd\_get\_output\_power**|查询读写器当前输出功率|
|9|**0x78**|**cmd\_set\_frequency\_region**|设置读写器工作频率范围|
|10|**0x79**|**cmd\_get\_frequency\_region**|查询读写器工作频率范围|
|11|**0x7A**|**cmd\_set\_beeper\_mode**|设置蜂鸣器状态|
|12|**0x7B**|**cmd\_get\_reader\_temperature**|查询当前设备的工作温度|
|13|**0x60**|**cmd\_read\_gpio\_value**|读取GPIO电平|
|14|**0x61**|**cmd\_write\_gpio\_value**|设置GPIO电平|
|15|**0x62**|**cmd\_set\_ant\_connection\_detector**|设置天线连接检测器状态|
|16|**0x63**|**cmd\_get\_ant\_connection\_detector**|读取天线连接检测器状态|
|17|**0x66**|**cmd\_set\_temporary\_output\_power**|设置读写器临时射频输出功率|
|18|**0x67**|**cmd\_set\_reader\_identifier**|设置读写器识别码|
|19|**0x68**|**cmd\_get\_reader\_identifier**|读取读写器识别码|
|20|**0x69**|**cmd\_set\_rf\_link\_profile**|设置射频链路的通讯速率|
|21|**0x6A**|**cmd\_get\_rf\_link\_profile**|读取射频链路的通讯速率|
|22|**0x7E**|**cmd\_get\_rf\_port\_return\_loss**|测量天线端口的回波损耗|
|**18000-6C 命令** ||||
|23|**0x80**|**cmd\_inventory**|盘存标签|
|24|**0x81**|**cmd\_read**|读标签|
|25|**0x82**|**cmd\_write**|写标签|
|26|**0x83**|**cmd\_lock**|锁定标签 |
|27|**0x84**|**cmd\_kill**|灭活标签|
|28|**0x85**|**cmd\_set\_access\_epc\_match**|匹配ACCESS操作的EPC号|
|29|**0x86**|**cmd\_get\_access\_epc\_match**|查询匹配的EPC状态|
|30|**0x89**|**cmd\_real\_time\_inventory**|盘存标签(实时上传标签数据)|
|31|**0x8A**|**cmd\_fast\_switch\_ant\_inventory**|快速轮询多个天线盘存标签|
|32|**0x8B**|**cmd\_customized\_session\_target\_inventory**|自定义session和target盘存|
|33|**0x8C**|**cmd\_set\_impinj\_fast\_tid**|<p>设置Monza标签快速读TID </p><p>(设置不被保存至内部FLASH)</p>|
|34|**0x8D**|**cmd\_set\_and\_save\_impinj\_fast\_tid**|<p>设置Monza标签快速读TID</p><p>(设置被保存至内部FLASH)</p>|
|35|**0x8E**|**cmd\_get\_impinj\_fast\_tid**|查询当前的快速TID设置|
|**ISO18000-6B 命令**||||
|36|**0xB0**|**cmd\_iso18000\_6b\_inventory**|盘存18000-6B标签|
|37|**0xB1**|**cmd\_iso18000\_6b\_read**|读18000-6B标签|
|38|**0xB2**|**cmd\_iso18000\_6b\_write**|写18000-6B标签|
|39|**0xB3**|**cmd\_iso18000\_6b\_lock**|锁定18000-6B标签|
|40|**0xB4**|**cmd\_iso18000\_6b\_query\_lock**|查询18000-6B标签|
|**缓存操作命令**||||
|41|**0x90**|**cmd\_get\_inventory\_buffer**|提取标签数据保留缓存备份|
|42|**0x91**|**cmd\_get\_and\_reset\_inventory\_buffer**|提取标签数据并删除缓存|
|43|**0x92**|**cmd\_get\_inventory\_buffer\_tag\_count**|查询缓存中已读标签个数|
|44|**0x93**|**cmd\_reset\_inventory\_buffer**|清空标签数据缓存|



## <a name="_toc328402770"></a><a name="_toc328402818"></a><a name="_toc328405846"></a><a name="_toc328407226"></a><a name="_toc328407269"></a><a name="_toc328407316"></a><a name="_toc328407741"></a><a name="_toc328407786"></a><a name="_toc328407831"></a><a name="_toc328407876"></a><a name="_toc523831748"></a>**2.1 系统设置指令**
<a name="_toc328405847"></a><a name="_toc328407227"></a><a name="_toc328407270"></a><a name="_toc328407317"></a><a name="_toc328407742"></a><a name="_toc328407787"></a><a name="_toc328407832"></a><a name="_toc328407877"></a><a name="_toc523831749"></a>**2.1.1 cmd\_reset**
**

**
`	`上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x70||


**◆操作成功：**无数据返回，读写器重启，蜂鸣器响一声。

**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x70|||
|||||||
|参数说明|ErrorCode |错误代码||||
**


<a name="_toc328405848"></a><a name="_toc328407228"></a><a name="_toc328407271"></a><a name="_toc328407318"></a><a name="_toc328407743"></a><a name="_toc328407788"></a><a name="_toc328407833"></a><a name="_toc328407878"></a><a name="_toc523831750"></a>**2.1.2 cmd\_set\_uart\_baudrate**
**

**
`	`上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>BaudRate</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x71</td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="6"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="1" rowspan="2">BaudRate</td><td colspan="1">0x03</td><td colspan="3">38400 bps</td></tr>
<tr><td colspan="1">0x04</td><td colspan="3">115200 bps</td></tr>
</table>



**◆操作成功：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x71|CommandSuccess ||

读写器成功收到此命令帧后，用先前波特率返回应答数据包，然后重新启动读写器。

`	`新的波特率保存在内部FLASH中，断电不丢失。



**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x71|||
|||||||
|参数说明|ErrorCode|错误代码||||


**


<a name="_toc328405849"></a><a name="_toc328407229"></a><a name="_toc328407272"></a><a name="_toc328407319"></a><a name="_toc328407744"></a><a name="_toc328407789"></a><a name="_toc328407834"></a><a name="_toc328407879"></a><a name="_toc523831751"></a>**2.1.3 cmd\_get\_firmware\_version**
**


`	`上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x72||

读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Major</b></th><th colspan="1" valign="top"><b>Minor</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x05</td><td colspan="1"></td><td colspan="1">0x72</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="1">Major</td><td colspan="5">固件主版本号。</td></tr>
<tr><td colspan="1">Minor</td><td colspan="5">固件次版本号。</td></tr>
</table>

**                                                                               















<a name="_toc328405850"></a><a name="_toc328407230"></a><a name="_toc328407273"></a><a name="_toc328407320"></a><a name="_toc328407745"></a><a name="_toc328407790"></a><a name="_toc328407835"></a><a name="_toc328407880"></a><a name="_toc523831752"></a>**2.1.4 cmd\_set\_reader\_address**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Address**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x73|||
|||||||
|参数说明|Address|读写器地址，取值范围0 – 254。||||

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x73|CommandSuccess ||
新的读写器地址立即生效，并被写入FLASH保存，断电不丢失。

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x73|||
|||||||
|参数说明|ErrorCode|错误代码||||

**                                                                               











<a name="_toc328405851"></a><a name="_toc328407231"></a><a name="_toc328407274"></a><a name="_toc328407321"></a><a name="_toc328407746"></a><a name="_toc328407791"></a><a name="_toc328407836"></a><a name="_toc328407881"></a><a name="_toc523831753"></a>**2.1.5 cmd\_set\_work\_antenna**
**


`	`上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>AntennaID</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x74</td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="6"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1" rowspan="4">AntennaID</td><td colspan="1" rowspan="4">天线号</td><td colspan="1">0x00</td><td colspan="2">天线 1</td></tr>
<tr><td colspan="1">0x01</td><td colspan="2">天线 2</td></tr>
<tr><td colspan="1">0x02</td><td colspan="2">天线 3</td></tr>
<tr><td colspan="1">0x03</td><td colspan="2">天线 4</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x74|CommandSuccess ||

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x74|||
|||||||
|参数说明|ErrorCode|错误代码||||

**                                                                               









<a name="_toc328405852"></a><a name="_toc328407232"></a><a name="_toc328407275"></a><a name="_toc328407322"></a><a name="_toc328407747"></a><a name="_toc328407792"></a><a name="_toc328407837"></a><a name="_toc328407882"></a><a name="_toc523831754"></a>**2.1.6 cmd\_get\_work\_antenna**
**

**
`	`上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x75||

`	`读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>AntennaID</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x75</td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="6"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="2" rowspan="4"><p>AntennaID</p><p>(天线号)</p></td><td colspan="1">0x00</td><td colspan="2">天线 1</td></tr>
<tr><td colspan="1">0x01</td><td colspan="2">天线 2</td></tr>
<tr><td colspan="1">0x02</td><td colspan="2">天线 3</td></tr>
<tr><td colspan="1">0x03</td><td colspan="2">天线 4</td></tr>
</table>

**                                                                               
















<a name="_toc328405853"></a><a name="_toc328407233"></a><a name="_toc328407276"></a><a name="_toc328407323"></a><a name="_toc328407748"></a><a name="_toc328407793"></a><a name="_toc328407838"></a><a name="_toc328407883"></a><a name="_toc523831755"></a>**2.1.7 cmd\_set\_output\_power**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**RfPower**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x76|||
|||||||
|参数说明|RfPower|RF输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。||||

或者：**	

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Power1</b></th><th colspan="1" valign="top"><b>Power2</b></th><th colspan="1" valign="top"><b>Power3</b></th><th colspan="1" valign="top"><b>Power4</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x07</td><td colspan="1"></td><td colspan="1">0x76</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="9" valign="top"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1">Power1</td><td colspan="7" valign="top">天线1输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。</td></tr>
<tr><td colspan="1">Power2</td><td colspan="7" valign="top">天线2输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。</td></tr>
<tr><td colspan="1">Power3</td><td colspan="7" valign="top">天线3输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。</td></tr>
<tr><td colspan="1">Power4</td><td colspan="7" valign="top">天线4输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x76|CommandSuccess ||

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x76|||
|||||||
|参数说明|ErrorCode|错误代码||||

操作成功后输出功率值将被保存在内部的Flash中，断电后不丢失。

**注意：**

**★此命令耗时将超过100mS。**

**★如果需要动态改变射频输出功率，请使用cmd\_set\_temporary\_output\_power**

**命令，否则将会影响Flash的使用寿命。**
**


<a name="_toc328405854"></a><a name="_toc328407234"></a><a name="_toc328407277"></a><a name="_toc328407324"></a><a name="_toc328407749"></a><a name="_toc328407794"></a><a name="_toc328407839"></a><a name="_toc328407884"></a><a name="_toc523831756"></a>**2.1.8 cmd\_get\_output\_power**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x77||
**	
**
`	`如果所有天线的功率设置相同，读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**OutputPower**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x77|||
|||||||
|参数说明|OutputPower|读写器当前的射频输出功率。||||

否则返回：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Power1</b></th><th colspan="1" valign="top"><b>Power2</b></th><th colspan="1" valign="top"><b>Power3</b></th><th colspan="1" valign="top"><b>Power4</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x07</td><td colspan="1"></td><td colspan="1">0x77</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="9" valign="top"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1">Power1</td><td colspan="7" valign="top">天线1输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。</td></tr>
<tr><td colspan="1">Power2</td><td colspan="7" valign="top">天线2输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。</td></tr>
<tr><td colspan="1">Power3</td><td colspan="7" valign="top">天线3输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。</td></tr>
<tr><td colspan="1">Power4</td><td colspan="7" valign="top">天线4输出功率，取值范围 0-33(0x00 – 0x21), 单位dBm。</td></tr>
</table>

**                                                                               



<a name="_toc328405855"></a><a name="_toc328407235"></a><a name="_toc328407278"></a><a name="_toc328407325"></a><a name="_toc328407750"></a><a name="_toc328407795"></a><a name="_toc328407840"></a><a name="_toc328407885"></a><a name="_toc523831757"></a>**2.1.9 cmd\_set\_frequency\_region**
**


上位机指令数据包：

射频频谱的定义有两种方法。

方法一：使用系统默认的频点（参见频率参数对应表），上位机发送：



<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>Region</b></th><th colspan="1" valign="top"><b>StartFreq</b></th><th colspan="1" valign="top"><b>EndFreq</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x06</td><td colspan="1"></td><td colspan="1">0x78</td><td colspan="2" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="1" rowspan="5">参数说明</td><td colspan="1" rowspan="3" valign="top"><p></p><p>Region</p></td><td colspan="1" rowspan="3">射频规范</td><td colspan="2">0x01</td><td colspan="4">FCC</td></tr>
<tr><td colspan="2">0x02</td><td colspan="4">ETSI</td></tr>
<tr><td colspan="2">0x03</td><td colspan="4">CHN</td></tr>
<tr><td colspan="1" valign="top">StartFreq</td><td colspan="1">频率起始点</td><td colspan="2"></td><td colspan="4" rowspan="2">可以在射频规范的频率范围内再设置跳频的范围。参数所对应的频率请参见频率参数对应表。参数的设置规则为：1，起始频率与结束频率不能超过射频规范的范围。2，起始频率必须低于结束频率。3，起始频率等于结束频率则定频发射。</td></tr>
<tr><td colspan="1" valign="top"><p></p><p></p><p>EndFreq</p></td><td colspan="1"><p>频率结束点</p><p></p></td><td colspan="2"><p></p><p></p><p></p></td></tr>
</table>


方法二：用户自定义频谱，上位机发送以下命令：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Region</b></th><th colspan="1" valign="top"><b>FreqSpace</b></th><th colspan="1" valign="top"><b>RreqQuantity</b></th><th colspan="1" valign="top"><b>StartFreq</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x09</td><td colspan="1"></td><td colspan="1">0x78</td><td colspan="1" valign="top">0x04</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top">3bytes</td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1" valign="top">Region</td><td colspan="1">射频规范</td><td colspan="6">此值固定为0x04。</td></tr>
<tr><td colspan="1" valign="top">FreqSpace</td><td colspan="1">频点间隔</td><td colspan="6">频点间隔 = FreqSpace x 10KHz。</td></tr>
<tr><td colspan="1" valign="top">FreqQuantity</td><td colspan="1">频点数量</td><td colspan="6">包含起始频率的频点数量，1为以起始频率定频发射。此参数必须大于0。</td></tr>
<tr><td colspan="1" valign="top">StartFreq</td><td colspan="1">起始频率</td><td colspan="6">单位为KHz。16进制数高位在前。例如915000KHz则发送 0D F6 38。</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x78|CommandSuccess ||

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x78|||
|||||||
|参数说明|ErrorCode|错误代码||||

**                                                                               

<a name="_toc328405856"></a><a name="_toc328407236"></a><a name="_toc328407279"></a><a name="_toc328407326"></a><a name="_toc328407751"></a><a name="_toc328407796"></a><a name="_toc328407841"></a><a name="_toc328407886"></a><a name="_toc523831758"></a>**2.1.10 cmd\_get\_frequency\_region**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x79||
**	

如果使用的是系统默认频点点，则读写器返回以下数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>Region</b></th><th colspan="1" valign="top"><b>StartFreq</b></th><th colspan="1" valign="top"><b>EndFreq</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x06</td><td colspan="1"></td><td colspan="1">0x79</td><td colspan="2" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="1" rowspan="5">参数说明</td><td colspan="1" rowspan="3" valign="top"><p></p><p>Region</p></td><td colspan="1" rowspan="3">射频规范</td><td colspan="2">0x01</td><td colspan="4">FCC</td></tr>
<tr><td colspan="2">0x02</td><td colspan="4">ETSI</td></tr>
<tr><td colspan="2">0x03</td><td colspan="4">CHN</td></tr>
<tr><td colspan="1" valign="top">StartFreq</td><td colspan="1">频率起始点</td><td colspan="2"></td><td colspan="4">跳频频率范围的低点。</td></tr>
<tr><td colspan="1" valign="top">EndFreq</td><td colspan="1">频率结束点</td><td colspan="2"></td><td colspan="4">跳频频率范围的高点。</td></tr>
</table>



如果使用的是自定义频点，则读写器返回以下数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Region</b></th><th colspan="1" valign="top"><b>FreqSpace</b></th><th colspan="1" valign="top"><b>RreqQuantity</b></th><th colspan="1" valign="top"><b>StartFreq</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x09</td><td colspan="1"></td><td colspan="1">0x79</td><td colspan="1" valign="top">0x04</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top">3bytes</td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1" valign="top">Region</td><td colspan="1">射频规范</td><td colspan="6">此值固定为0x04。</td></tr>
<tr><td colspan="1" valign="top">FreqSpace</td><td colspan="1">频点间隔</td><td colspan="6">频点间隔 = FreqSpace x 10KHz。</td></tr>
<tr><td colspan="1" valign="top">FreqQuantity</td><td colspan="1">频点数量</td><td colspan="6">包含起始频率的频点数量，1为以起始频率定频发射。此参数必须大于0。</td></tr>
<tr><td colspan="1" valign="top">StartFreq</td><td colspan="1">起始频率</td><td colspan="6">单位为KHz。16进制数高位在前。例如915000KHz则返回 0D F6 38。</td></tr>
</table>

**                                                                               







<a name="_toc328407752"></a><a name="_toc328407797"></a><a name="_toc328407842"></a><a name="_toc328407887"></a><a name="_toc523831759"></a>**2.1.11 cmd\_set\_beeper\_mode**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Mode</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x7A</td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="6"></td></tr>
<tr><td colspan="1" rowspan="3">参数说明</td><td colspan="2" rowspan="3" valign="top"><p></p><p>Mode</p><p>(操作标签时蜂鸣器状态)</p></td><td colspan="1">0x00</td><td colspan="2">安静</td></tr>
<tr><td colspan="1">0x01</td><td colspan="2">每次盘存后鸣响</td></tr>
<tr><td colspan="1">0x02</td><td colspan="2">每读到一张标签鸣响</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x7A|CommandSuccess ||

操作成功后此配置将保存至内部FLASH, 断电后不丢失。

**注意：**

**★读到一张标签后蜂鸣器鸣响，会占用大量处理器时间，若此选项打开，将会明显影响到读多标签（防冲突算法）的性能，此选项应作为测试功能选用。**

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x7A|||
|||||||
|参数说明|ErrorCode|错误代码||||

**                                                                               





<a name="_toc328405857"></a><a name="_toc328407237"></a><a name="_toc328407280"></a><a name="_toc328407327"></a><a name="_toc328407753"></a><a name="_toc328407798"></a><a name="_toc328407843"></a><a name="_toc328407888"></a><a name="_toc523831760"></a>**2.1.12 cmd\_get\_reader\_temperature**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x7B||
||||||

**◆操作成功：**

`	`读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>PlusMinus</b></th><th colspan="1" valign="top"><b>Temp</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x05</td><td colspan="1"></td><td colspan="1">0x7B</td><td colspan="2"></td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="1" rowspan="3">参数说明</td><td colspan="2" rowspan="2">PlusMinus</td><td colspan="2">0x00</td><td colspan="3">零下</td></tr>
<tr><td colspan="2">0x01</td><td colspan="3">零上</td></tr>
<tr><td colspan="2">Temp</td><td colspan="5">摄氏度</td></tr>
</table>

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x7B|||
|||||||
|参数说明|ErrorCode|错误代码||||

**                                                                               









<a name="_toc328405860"></a><a name="_toc328407240"></a><a name="_toc328407283"></a><a name="_toc328407330"></a><a name="_toc328407756"></a><a name="_toc328407801"></a><a name="_toc328407846"></a><a name="_toc328407891"></a><a name="_toc523831761"></a>**2.1.13 cmd\_read\_gpio\_value**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x60||

`	`读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>Gpio1</b></th><th colspan="1" valign="top"><b>Gpio2</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x05</td><td colspan="1"></td><td colspan="1">0x60</td><td colspan="2">  </td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="2" rowspan="4">参数说明</td><td colspan="1" rowspan="2">Gpio1</td><td colspan="2">0x00</td><td colspan="3">Gpio1的电平为低</td></tr>
<tr><td colspan="2">0x01</td><td colspan="3">Gpio1的电平为高</td></tr>
<tr><td colspan="1" rowspan="2">Gpio2</td><td colspan="2">0x00</td><td colspan="3">Gpio2的电平为低</td></tr>
<tr><td colspan="2">0x01</td><td colspan="3">Gpio2的电平为高</td></tr>
</table>

**                                                                               


<a name="_toc328407757"></a><a name="_toc328407802"></a><a name="_toc328407847"></a><a name="_toc328407892"></a><a name="_toc523831762"></a>**2.1.14 cmd\_write\_gpio\_value**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>ChooseGpio</b></th><th colspan="1" valign="top"><b>GpioValue</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x05</td><td colspan="1"></td><td colspan="1">0x61</td><td colspan="2">  </td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="2" rowspan="4">参数说明</td><td colspan="1" rowspan="2">ChooseGpio</td><td colspan="2">0x03</td><td colspan="3">设置GPIO 3</td></tr>
<tr><td colspan="2">0x04</td><td colspan="3">设置GPIO 4</td></tr>
<tr><td colspan="1" rowspan="2">GpioValue</td><td colspan="2">0x00</td><td colspan="3">设置为低电平</td></tr>
<tr><td colspan="2">0x01</td><td colspan="3">设置为高电平</td></tr>
</table>

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x61|||
|||||||
|参数说明|ErrorCode |错误代码||||
**


<a name="_toc328405861"></a><a name="_toc328407241"></a><a name="_toc328407284"></a><a name="_toc328407331"></a><a name="_toc328407758"></a><a name="_toc328407803"></a><a name="_toc328407848"></a><a name="_toc328407893"></a><a name="_toc523831763"></a>**2.1.15 cmd\_set\_ant\_connection\_detector**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>DetectorSensitivity</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x62</td><td colspan="1">  </td><td colspan="1"></td></tr>
<tr><td colspan="6"></td></tr>
<tr><td colspan="2" rowspan="2">参数说明</td><td colspan="1" rowspan="2">DetectorSensitivity</td><td colspan="1">0x00</td><td colspan="2">关闭天线连接检测。</td></tr>
<tr><td colspan="1"></td><td colspan="2">` `天线连接检测的灵敏度(端口回波损耗值)，单位dB。值越大，对端口的阻抗匹配要求越高。</td></tr>
</table>

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x62|||
|||||||
|参数说明|ErrorCode |错误代码||||
**


<a name="_toc328405862"></a><a name="_toc328407242"></a><a name="_toc328407285"></a><a name="_toc328407332"></a><a name="_toc328407759"></a><a name="_toc328407804"></a><a name="_toc328407849"></a><a name="_toc328407894"></a><a name="_toc523831764"></a>**2.1.16 cmd\_get\_ant\_connection\_detector**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x63||

`	`读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>DetectorSensitivity</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x63</td><td colspan="2">  </td><td colspan="1"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="2" rowspan="2">参数说明</td><td colspan="1" rowspan="2">DetectorSensitivity</td><td colspan="2">0x00</td><td colspan="2">天线连接检测已关闭。</td></tr>
<tr><td colspan="2"> </td><td colspan="2">天线连接检测的灵敏度(端口回波损耗值)。</td></tr>
</table>
**


<a name="_toc523831765"></a>**2.1.17 cmd\_set\_temporary\_output\_power**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**RfPower**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x66|||
|||||||
|参数说明|RfPower|RF输出功率，取值范围 20-33(0x14 – 0x21), 单位dBm。||||

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x66|CommandSuccess ||

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x66|||
|||||||
|参数说明|ErrorCode |错误代码||||

操作成功后输出功率值将不会被保存在内部的Flash中，重新启动或断电后输出功率将恢复至内部Flash中保存的输出功率值。此命令的操作速度非常快，并且不写Flash，从而不影响Flash的使用寿命，适合需要反复切换射频输出功率的应用。

**                                                                               







<a name="_toc523831766"></a>**2.1.18 cmd\_set\_reader\_identifier**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Identifier**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x0F||0x67|12 Bytes||
|||||||
|参数说明|Identifier|12字节的读写器识别字符。||||

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x67|CommandSuccess ||

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x67|||
|||||||
|参数说明|ErrorCode |错误代码||||

操作成功后12字节的读写器识别字符串将会保存在内部的Flash中，断电后不丢失。

**                                                                               








<a name="_toc523831767"></a>**2.1.19 cmd\_get\_reader\_identifier**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x68| |

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Identifier**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x0F||0x68|12 Bytes||
|||||||
|参数说明|Identifier|12字节的读写器识别字符。||||

**                                                                               















<a name="_toc523831768"></a>**2.1.20 cmd\_set\_rf\_link\_profile**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>ProfileID</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x69</td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="6"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1" rowspan="4">ProfileID</td><td colspan="1">0xD0</td><td colspan="3">Profile 0：Tari 25uS，FM0 40KHz。</td></tr>
<tr><td colspan="1">0xD1</td><td colspan="3"><p>Profile 1：Tari 25uS，Miller 4 250KHz。</p><p>此设置为推荐设置，并为系统默认设置。</p></td></tr>
<tr><td colspan="1">0xD2</td><td colspan="3">Profile 2：Tari 25uS，Miller 4 300KHz。</td></tr>
<tr><td colspan="1">0xdD3</td><td colspan="3">Profile 3：Tari 6.25uS，FM0 400KHz。</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x69|CommandSuccess ||

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x69|||
|||||||
|参数说明|ErrorCode |错误代码||||

操作成功后读写器会重新启动，配置保存在内部的Flash中，断电后不丢失。

**                                                                               






<a name="_toc523831769"></a>**2.1.21 cmd\_get\_rf\_link\_profile**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x6A||

**◆操作成功：**

`	`读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>ProfileID</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x6A</td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="6"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1" rowspan="4">ProfileID</td><td colspan="1">0xD0</td><td colspan="3">Profile 0：Tari 25uS，FM0 40KHz。</td></tr>
<tr><td colspan="1">0xD1</td><td colspan="3"><p>Profile 1：Tari 25uS，Miller 4 250KHz。</p><p>此设置为推荐设置，并为系统默认设置。</p></td></tr>
<tr><td colspan="1">0xD2</td><td colspan="3">Profile 2：Tari 25uS，Miller 4 300KHz。</td></tr>
<tr><td colspan="1">0xdD3</td><td colspan="3">Profile 3：Tari 6.25uS，FM0 400KHz。</td></tr>
</table>

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x69|||
|||||||
|参数说明|ErrorCode |错误代码||||


**









<a name="_toc523831770"></a>**2.1.22 cmd\_get\_rf\_port\_return\_loss**

上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**FreqParameter**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x7E|||
|||||||
|参数说明|FreqParameter|<p>频率参数参见频率参数对应表。</p><p>系统将获取此频点当前工作天线端口的回波损耗值。</p>||||

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ReturnLoss**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x7E|||
|||||||
|参数说明|ReturnLoss|回波损耗值，单位是dB。VSWR = (10 <sup>RL/20</sup> + 1)/ (10 <sup>RL/20</sup> - 1)。||||

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x7E|EE||
|||||||
|参数说明|ErrorCode |错误代码||||

**                                                                                










## <a name="_toc328402771"></a><a name="_toc328402819"></a><a name="_toc328405863"></a><a name="_toc328407243"></a><a name="_toc328407286"></a><a name="_toc328407333"></a><a name="_toc328407760"></a><a name="_toc328407805"></a><a name="_toc328407850"></a><a name="_toc328407895"></a><a name="_toc523831771"></a>**2.2 18000-6C标签操作命令**
<a name="_toc328405864"></a><a name="_toc328407244"></a><a name="_toc328407287"></a><a name="_toc328407334"></a><a name="_toc328407761"></a><a name="_toc328407806"></a><a name="_toc328407851"></a><a name="_toc328407896"></a><a name="_toc523831772"></a>**2.2.1 盘存指令合集**

<a name="_toc523831773"></a>******* cmd\_inventory
**


缓存模式：读写器收到此命令后，进行多标签识别操作。标签数据存入读写器缓存区，使用提取缓存指令可获得标签数据，详见：2.4缓存操作命令。

上位机指令数据包：	

|**Head**|**Len**|**Address**|**Cmd** |**Repeat**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x80|||
|||||||
|参数说明|Repeat|<p>盘存过程重复的次数。</p><p>Repeat = 0xFF则此轮盘存时间为最短时间。如果射频区域内只有一张标签，则此轮的盘存约耗时为30-50mS。一般在四通道机器上快速轮询多个天线时使用此参数值。</p>||||

**注意：**

**★将参数设置成255（0xFF）时，将启动专为读少量标签设计的算法。对于少量标的应**

**用来说，效率更高，反应更灵敏，但此参数不适合同时读取大量标签的应用。**

**◆操作成功：**

`	`读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>AntID</b></th><th colspan="1" valign="top"><b>TagCount</b></th><th colspan="1" valign="top"><b>ReadRate</b></th><th colspan="1" valign="top"><b>TotalRead</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x0C</td><td colspan="1"></td><td colspan="1">0x80</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">2 Bytes</td><td colspan="1" valign="top">2Bytes</td><td colspan="1" valign="top">4Bytes</td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="2" rowspan="4">参数说明</td><td colspan="2">AntID</td><td colspan="5" valign="top">此次盘存使用的天线号。</td></tr>
<tr><td colspan="2">TagCount</td><td colspan="5" valign="top">识别标签的总数量，根据EPC号来区分标签，相同EPC号的标签将被视为同一张标签。若未清空缓存，标签数量为多次盘存操作的数量累加。</td></tr>
<tr><td colspan="2">ReadRate</td><td colspan="5" valign="top">此次执行命令的标签识别速度(成功读取标签的次数/秒)。不区分是否多次读取同一张标签。</td></tr>
<tr><td colspan="2">TotalRead</td><td colspan="5" valign="top">此次执行命令的标签的总读取标签次数，不区分是否多次读取同一张标签。</td></tr>
</table>

**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x80|||
|||||||
|参数说明|ErrorCode |错误代码||||
**


<a name="_toc328405871"></a><a name="_toc328407251"></a><a name="_toc328407294"></a><a name="_toc328407341"></a><a name="_toc328407768"></a><a name="_toc328407813"></a><a name="_toc328407858"></a><a name="_toc328407903"></a><a name="_toc523831774"></a>******* cmd\_real\_time\_inventory
**


实时模式（Auto）：读写器收到此命令后，进行多标签识别操作。标签数据实时上传，不存入读写器缓存区。此命令一轮盘存耗时较长，适用于大批量标签读取。

上位机指令数据包：	

|**Head**|**Len**|**Address**|**Cmd** |**Repeat**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x89|||
|||||||
|参数说明|Repeat|<p>盘存过程重复的次数。</p><p>Repeat = 0xFF则此轮盘存时间为最短时间。如果射频区域内只有一张标签，则此轮的盘存约耗时为30-50mS。一般在四通道机器上快速轮询多个天线时使用此参数值。</p>||||

**注意：**

**★由于硬件为双CPU架构，主CPU负责轮询标签，副CPU负责数据管理。轮询标签和发送数据并行，互不占用对方的时间，因此串口的数据传输不影响读写器工作的效率。**

`	`如有标签应答，返回如下数据包(多条)：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>FreqAnt</b></th><th colspan="1" valign="top"><b>PC</b> </th><th colspan="1" valign="top"><b>EPC</b></th><th colspan="1" valign="top"><b>RSSI</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x89</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">2 bytes</td><td colspan="1" valign="top">N bytes</td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="2" rowspan="4">参数说明</td><td colspan="2">FreqAnt</td><td colspan="5" valign="top">此字节高6位是读取标签的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">PC</td><td colspan="5" valign="top">标签的PC ,固定两个字节</td></tr>
<tr><td colspan="2">EPC</td><td colspan="5" valign="top">标签的EPC号，长度可变化。</td></tr>
<tr><td colspan="2">RSSI</td><td colspan="5" valign="top">标签的实时RSSI。</td></tr>
</table>

命令完成，读写器返回如下数据包：

**◆操作成功：**

读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>AntID</b> </th><th colspan="1" valign="top"><b>ReadRate</b></th><th colspan="1" valign="top"><b>TotalRead</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x0A</td><td colspan="1"></td><td colspan="1">` `0x89</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">2bytes</td><td colspan="1" valign="top">4 bytes</td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="1" rowspan="3">参数说明</td><td colspan="1">AntID</td><td colspan="6">此次盘存使用的天线号。</td></tr>
<tr><td colspan="1">ReadRate</td><td colspan="6">此轮命令标签识别速率。</td></tr>
<tr><td colspan="1">TotalRead</td><td colspan="6">标签应答的总记录数。</td></tr>
</table>

**◆操作失败：**

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x89|||
|||||||
|参数说明|ErrorCode |错误代码||||
**


<a name="_toc523831775"></a>**2.2.1.3cmd\_customized\_session\_target\_inventory**
**


**推荐使用的盘存指令**

实时模式（Session）：读写器收到此命令后，按照指定的session和inventoried flag进行多标签识别操作。标签数据实时上传，不存入读写器缓存区。此命令一轮盘存耗时短，普通盘存推荐使用此命令S1模式。

关于S0~S1模式，详见：EPC RFID Protocols\_Class1\_Gen2\_V1.1.0->******* Sessions and inventoried flags

上位机指令数据包：	

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Session</b></th><th colspan="1" valign="top"><b>Target</b></th><th colspan="1" valign="top"><b>Repeat</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x06</td><td colspan="1"></td><td colspan="1">0x8B</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="1" rowspan="3">参数说明</td><td colspan="1">Session</td><td colspan="6">指定盘存的session。00为S0，01为S1，02为S2，03为S3。</td></tr>
<tr><td colspan="1">Target</td><td colspan="6">指定盘存的Inventoried Flag，00为A，01为B。</td></tr>
<tr><td colspan="1">Repeat</td><td colspan="6">盘存过程重复的次数。 </td></tr>
</table>

或者（此命令仅适用于固件版本为V8.2以上的读写器）：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="2" valign="top"><b>Address</b></th><th colspan="2" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Session</b></th><th colspan="1" valign="top"><b>Target</b></th><th colspan="1" valign="top"><b>SL</b></th><th colspan="1" valign="top"><b>Repeat</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x07</td><td colspan="2"></td><td colspan="2">0x8B</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="12" valign="top"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="2">Session</td><td colspan="2" valign="top"></td><td colspan="7">指定盘存的session。00为S0，01为S1，02为S2，03为S3。</td></tr>
<tr><td colspan="2">Target</td><td colspan="2" valign="top"></td><td colspan="7">指定盘存的Inventoried Flag，00为A，01为B。</td></tr>
<tr><td colspan="2">SL</td><td colspan="2" valign="top"></td><td colspan="7">Select Flag；范围：00,01,02,03.</td></tr>
<tr><td colspan="2">Repeat</td><td colspan="2" valign="top"></td><td colspan="7">盘存过程重复的次数。 </td></tr>
</table>

或者（此命令仅适用于固件版本为V8.2以上的读写器）：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="2" valign="top"><b>Address</b></th><th colspan="2" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>Session</b></th><th colspan="1" valign="top"><b>Target</b></th><th colspan="1" valign="top"><b>SL</b></th><th colspan="1" valign="top"><b>Phase</b></th><th colspan="1" valign="top"><b>Repeat</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x08</td><td colspan="2"></td><td colspan="2">0x8B</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="12" valign="top"></td></tr>
<tr><td colspan="1" rowspan="5">参数说明</td><td colspan="2">Session</td><td colspan="2" valign="top"></td><td colspan="7">指定盘存的session。00为S0，01为S1，02为S2，03为S3。</td></tr>
<tr><td colspan="2">Target</td><td colspan="2" valign="top"></td><td colspan="7">指定盘存的Inventoried Flag，00为A，01为B。</td></tr>
<tr><td colspan="2">SL</td><td colspan="2" valign="top"></td><td colspan="7">Select Flag；范围：00,01,02,03.</td></tr>
<tr><td colspan="2">Phase</td><td colspan="2" valign="top"></td><td colspan="7">相位值；00为关闭此功能，01为打开此功能。</td></tr>
<tr><td colspan="2">Repeat</td><td colspan="2" valign="top"></td><td colspan="7">盘存过程重复的次数。 </td></tr>
</table>

**注意：**

**★由于硬件为双CPU架构，主CPU负责轮询标签，副CPU负责数据管理。轮询标签和发送数据并行，互不占用对方的时间，因此串口的数据传输不影响读写器工作的效率。**

`	`如有标签应答，返回如下数据包(多条)：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>FreqAnt</b></th><th colspan="1" valign="top"><b>PC</b> </th><th colspan="1" valign="top"><b>EPC</b></th><th colspan="1" valign="top"><b>RSSI</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x8B</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">2 bytes</td><td colspan="1" valign="top">N bytes</td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="2" rowspan="4">参数说明</td><td colspan="2">FreqAnt</td><td colspan="5" valign="top">此字节高6位是读取标签的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">PC</td><td colspan="5" valign="top">标签的PC ,固定两个字节</td></tr>
<tr><td colspan="2">EPC</td><td colspan="5" valign="top">标签的EPC号，长度可变化。</td></tr>
<tr><td colspan="2">RSSI</td><td colspan="5" valign="top">标签的实时RSSI。</td></tr>
</table>

如果打开了相位值，则返回如下数据包(多条)：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>FreqAnt</b></th><th colspan="2" valign="top"><b>PC</b> </th><th colspan="1" valign="top"><b>EPC</b></th><th colspan="1" valign="top"><b>RSSI</b></th><th colspan="1" valign="top"><b>Phase</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x8B</td><td colspan="1" valign="top"></td><td colspan="2" valign="top">2 bytes</td><td colspan="1" valign="top">N bytes</td><td colspan="1"></td><td colspan="1" valign="top">2 bytes</td><td colspan="1"></td></tr>
<tr><td colspan="11" valign="top"></td></tr>
<tr><td colspan="2" rowspan="5">参数说明</td><td colspan="2">FreqAnt</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">此字节高6位是读取标签的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">PC</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">标签的PC ,固定两个字节。</td></tr>
<tr><td colspan="2">EPC</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">标签的EPC号，长度可变化。</td></tr>
<tr><td colspan="2">RSSI</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">标签的实时RSSI。</td></tr>
<tr><td colspan="2">Phase</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">相位值。</td></tr>
</table>

命令完成，读写器返回如下数据包：

**◆操作成功：**

读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>AntID</b> </th><th colspan="1" valign="top"><b>ReadRate</b></th><th colspan="1" valign="top"><b>TotalRead</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x0A</td><td colspan="1"></td><td colspan="1">` `0x8B</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">2bytes</td><td colspan="1" valign="top">4 bytes</td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="1" rowspan="3">参数说明</td><td colspan="1">AntID</td><td colspan="6">此次盘存使用的天线号。</td></tr>
<tr><td colspan="1">ReadRate</td><td colspan="6">此轮命令标签识别速率。</td></tr>
<tr><td colspan="1">TotalRead</td><td colspan="6">标签应答的总记录数。</td></tr>
</table>

**◆操作失败：**

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x8B|||
|||||||
|参数说明|ErrorCode |错误代码||||
**




<a name="_toc523831776"></a>******* cmd\_fast\_switch\_ant\_inventory
**


上位机指令数据包：	

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>A</b></th><th colspan="1" valign="top"><b>Stay</b></th><th colspan="1" valign="top"><b>B</b></th><th colspan="1" valign="top"><b>Stay</b></th><th colspan="1" valign="top"><b>C</b></th><th colspan="1" valign="top"><b>Stay</b></th><th colspan="1" valign="top"><b>D</b></th><th colspan="1" valign="top"><b>Stay</b></th><th colspan="1" valign="top"><b>Interval</b></th><th colspan="1" valign="top"><b>Repeat</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x0D</td><td colspan="1"></td><td colspan="1">0x8A</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="16"></td></tr>
<tr><td colspan="2" rowspan="7">参数说明</td><td colspan="2">A</td><td colspan="12">首先轮询的天线 (00 – 03) ，天线号大于三则表示不轮询。</td></tr>
<tr><td colspan="2">Stay</td><td colspan="12">天线重复轮询的次数。每个天线可单独配置。</td></tr>
<tr><td colspan="2">B</td><td colspan="12">第二个轮询的天线 (00 – 03) ，天线号大于三则表示不轮询。</td></tr>
<tr><td colspan="2">C</td><td colspan="12">第三个轮询的天线 (00 – 03) ，天线号大于三则表示不轮询。</td></tr>
<tr><td colspan="2">D</td><td colspan="12">第四个轮询的天线 (00 – 03) ，天线号大于三则表示不轮询。</td></tr>
<tr><td colspan="2">Interval</td><td colspan="12">天线间的休息时间。单位是mS。休息时无射频输出，可降低功耗。</td></tr>
<tr><td colspan="2">Repeat</td><td colspan="12">重复以上天线切换顺序次数。</td></tr>
</table>





























21                    
![](data:image/png;base64,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 "英文-03")超高频读写器串行接口通讯协议 V3.6

或者（此命令仅适用于固件版本为V8.2以上的读写器）：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>A</b></th><th colspan="1" valign="top"><b>Stay</b></th><th colspan="1" valign="top"><b>B</b></th><th colspan="1" valign="top"><b>Stay</b></th><th colspan="1" valign="top"><b>C</b></th><th colspan="1" valign="top"><b>Stay</b></th><th colspan="1" valign="top"><b>…</b></th><th colspan="1" valign="top"><b>H</b></th><th colspan="1" valign="top"><b>Stay</b></th><th colspan="1" valign="top"><b>Interval</b></th><th colspan="1" valign="top"><b>Reserve0</b></th><th colspan="1" valign="top"><b>Session</b></th><th colspan="1" valign="top"><b>Target</b></th><th colspan="1" valign="top"><b>Reserve1</b></th><th colspan="1" valign="top"><b>Reserve2</b></th><th colspan="1" valign="top"><b>Reserve3</b></th><th colspan="1" valign="top"><b>Phase</b></th><th colspan="1" valign="top"><b>Repeat</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x20</td><td colspan="1"></td><td colspan="1">0x8A</td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1">5 bytes</td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"> </td><td colspan="1"> </td><td colspan="1"></td></tr>
<tr><td colspan="23"></td></tr>
<tr><td colspan="2" rowspan="15">参数说明</td><td colspan="2">A</td><td colspan="19">首先轮询的天线 (00 – 07) ，天线号大于7则表示不轮询。</td></tr>
<tr><td colspan="2">Stay</td><td colspan="19">天线重复轮询的次数。每个天线可单独配置。</td></tr>
<tr><td colspan="2">B</td><td colspan="19">第二个轮询的天线 (00 – 07) ，天线号大于7则表示不轮询。</td></tr>
<tr><td colspan="2">C</td><td colspan="19">第三个轮询的天线 (00 – 07) ，天线号大于7则表示不轮询。</td></tr>
<tr><td colspan="2">…</td><td colspan="19">…</td></tr>
<tr><td colspan="2">H</td><td colspan="19">第八个轮询的天线 (00 – 07) ，天线号大于7则表示不轮询。</td></tr>
<tr><td colspan="2">Interval</td><td colspan="19">天线间的休息时间。单位是mS。休息时无射频输出，可降低功耗。</td></tr>
<tr><td colspan="2">Reserve0</td><td colspan="19">预留字节，5 bytes，默认全部为： 0x00。</td></tr>
<tr><td colspan="2">Session</td><td colspan="19">指定盘存的session，00为S0，01为S1，02为S2，03为S3。</td></tr>
<tr><td colspan="2">Target</td><td colspan="19">指定盘存的Inventoried Flag，00为A，01为B。</td></tr>
<tr><td colspan="2">Reserve1</td><td colspan="19">预留字节，默认为： 0x00。</td></tr>
<tr><td colspan="2">Reserve2</td><td colspan="19">预留字节，默认为： 0x00。</td></tr>
<tr><td colspan="2">Reserve3</td><td colspan="19">预留字节，默认为： 0x00。</td></tr>
<tr><td colspan="2">Phase</td><td colspan="19">00为关闭相位值，01为打开相位值。</td></tr>
<tr><td colspan="2">Repeat</td><td colspan="19">重复以上天线切换顺序次数。</td></tr>
</table>

读写器收到此命令后，进行多标签识别操作，实时上传标签的数据，并且同时也会存入读写器缓存区。读写器会依次按照A->H的顺序自动切换天线。如果在射频区域内没有标签，或者只有一两张标签在射频区域内，则每个天线平均耗时30mS左右。如果标签数量比较多，则耗时时间会相应增加。此命令非常适合需要高速切换多个天线识别标签的应用。

超高频读写器串行接口通讯协议 V3.6

**注意：**

**★由于硬件为双CPU架构，主CPU负责轮询标签，副CPU负责数据管理。轮询标签和发送数据并行，互不占用对方的时间，因此串口的数据传输不影响读写器工作的效率。**

`	`如有标签应答，返回如下数据包(多条)：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>FreqAnt</b></th><th colspan="1" valign="top"><b>PC</b> </th><th colspan="1" valign="top"><b>EPC</b></th><th colspan="1" valign="top"><b>RSSI</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x8A</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">2 bytes</td><td colspan="1" valign="top">N bytes</td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="2" rowspan="4">参数说明</td><td colspan="2">FreqAnt</td><td colspan="5" valign="top">此字节高6位是读取标签的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">PC</td><td colspan="5" valign="top">标签的PC ,固定两个字节。</td></tr>
<tr><td colspan="2">EPC</td><td colspan="5" valign="top">标签的EPC号，长度可变化。</td></tr>
<tr><td colspan="2">RSSI</td><td colspan="5" valign="top"><p>标签的实时RSSI。</p><p>此字节高位为0时取天线号1/2/3/4；高位为1时取天线号5/6/7/8。（注：高位仅判断天线取值，不计入RSSI值）</p></td></tr>
</table>

如果打开了相位值，则返回如下数据包(多条)：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>FreqAnt</b></th><th colspan="2" valign="top"><b>PC</b> </th><th colspan="1" valign="top"><b>EPC</b></th><th colspan="1" valign="top"><b>RSSI</b></th><th colspan="1" valign="top"><b>Phase</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x8A</td><td colspan="1" valign="top"></td><td colspan="2" valign="top">2 bytes</td><td colspan="1" valign="top">N bytes</td><td colspan="1"></td><td colspan="1" valign="top">2 bytes</td><td colspan="1"></td></tr>
<tr><td colspan="11" valign="top"></td></tr>
<tr><td colspan="2" rowspan="5">参数说明</td><td colspan="2">FreqAnt</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">此字节高6位是读取标签的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">PC</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">标签的PC ,固定两个字节。</td></tr>
<tr><td colspan="2">EPC</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">标签的EPC号，长度可变化。</td></tr>
<tr><td colspan="2">RSSI</td><td colspan="2" valign="top"></td><td colspan="5" valign="top"><p>标签的实时RSSI。</p><p>此字节高位为0时取天线号1/2/3/4；高位为1时取天线号5/6/7/8。（注：高位仅判断天线取值，不计入RSSI值）</p></td></tr>
<tr><td colspan="2">Phase</td><td colspan="2" valign="top"></td><td colspan="5" valign="top">相位值</td></tr>
</table>

如果打开了天线连接检测，并检测到端口未连接天线则返回：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>AntID</b> </th><th colspan="1" valign="top"><b>ErrorCode</b> </th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x05</td><td colspan="1"></td><td colspan="1">` `0x8A</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">0x22</td><td colspan="1"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="1">AntID</td><td colspan="5">未连接的天线号(00 – 03)。</td></tr>
<tr><td colspan="1">ErrorCode </td><td colspan="5">0x22 , 天线未连接错误。</td></tr>
</table>

命令完成，读写器返回如下数据包：

**◆操作成功：**

读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>TotalRead</b></th><th colspan="1" valign="top"><b>CommandDuration</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x0A</td><td colspan="1"></td><td colspan="1">` `0x8A</td><td colspan="1" valign="top">3 bytes</td><td colspan="1" valign="top">4 bytes</td><td colspan="1"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="3">TotalRead</td><td colspan="3">总共上传的标签数据记录数，3字节，高位在前。</td></tr>
<tr><td colspan="3">CommandDuration</td><td colspan="3">命令总共消耗的时间，单位是毫秒，4字节，高位在前。</td></tr>
</table>

**◆操作失败：**

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| - | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x8A|||
|||||||
|参数说明|ErrorCode |错误代码||||

**                                                                               



<a name="_toc328405865"></a><a name="_toc328407245"></a><a name="_toc328407288"></a><a name="_toc328407335"></a><a name="_toc328407762"></a><a name="_toc328407807"></a><a name="_toc328407852"></a><a name="_toc328407897"></a><a name="_toc523831777"></a>**2.2.2 cmd\_read**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>MemBank</b></th><th colspan="1" valign="top"><b>WordAdd</b></th><th colspan="1" valign="top"><b>WordCnt</b></th><th colspan="1" valign="top"><b>PassWord</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">` `0x0A</td><td colspan="1"></td><td colspan="1">0x81</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1">4 Bytes</td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="1" rowspan="7">参数说明</td><td colspan="4" rowspan="4"><p>MemBank</p><p>(标签存储区域)</p></td><td colspan="1" valign="top">0x00</td><td colspan="3" valign="top">RESERVED</td></tr>
<tr><td colspan="1" valign="top">0x01</td><td colspan="3" valign="top">EPC</td></tr>
<tr><td colspan="1" valign="top">0X02</td><td colspan="3" valign="top">TID</td></tr>
<tr><td colspan="1" valign="top">0X03</td><td colspan="3" valign="top">USER</td></tr>
<tr><td colspan="4">WordAdd (读取数据首地址)</td><td colspan="4" valign="top">取值范围请参考标签规格。</td></tr>
<tr><td colspan="4"><p>WordCnt</p><p>(读取数据长度)</p></td><td colspan="4" valign="top"><p>字长，WORD(16 bits)长度。</p><p>取值范围请参考标签规格书。</p></td></tr>
<tr><td colspan="4">PassWord</td><td colspan="4" valign="top">标签访问密码，4字节。</td></tr>
</table>

或者（此命令适合R2000芯片方案快速大批量读取普通标签TID及User区数据）：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Add</b></th><th colspan="2" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><p><b>Res</b></p><p><b>Add</b></p></th><th colspan="1" valign="top"><p><b>Res</b></p><p><b>Len</b></p></th><th colspan="1" valign="top"><p><b>Tid</b></p><p><b>Add</b></p></th><th colspan="1" valign="top"><p><b>Tid</b></p><p><b>Len</b></p></th><th colspan="1" valign="top"><p><b>User</b></p><p><b>Add</b></p></th><th colspan="2" valign="top"><p><b>User</b></p><p><b>Len</b></p></th><th colspan="1" valign="top"><p><b>Pass</b></p><p><b>word</b></p></th><th colspan="1" valign="top"><b>Session</b></th><th colspan="1" valign="top"><b>Target</b></th><th colspan="1" valign="top"><p><b>Read</b></p><p><b>Mode</b></p></th><th colspan="1" valign="top"><p><b>Time</b></p><p><b>out</b></p></th><th colspan="1" valign="top"><b>CC</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x11</td><td colspan="1"></td><td colspan="2">0x81</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td><td colspan="2"></td><td colspan="1">4bytes</td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="18"></td></tr>
<tr><td colspan="4" rowspan="17">参数说明 </td><td colspan="3">ResAdd</td><td colspan="11" valign="top">密码区起始地址</td></tr>
<tr><td colspan="3">ResLen</td><td colspan="11" valign="top">密码区读取长度</td></tr>
<tr><td colspan="3">TidAdd</td><td colspan="11" valign="top">TID区起始地址</td></tr>
<tr><td colspan="3">TidLen</td><td colspan="11" valign="top">TID区读取长度</td></tr>
<tr><td colspan="3">UserAdd</td><td colspan="11" valign="top">用户区起始地址</td></tr>
<tr><td colspan="3">UserLen</td><td colspan="11" valign="top">用户区读取长度</td></tr>
<tr><td colspan="3">Password</td><td colspan="11" valign="top">访问密码, 4 字节</td></tr>
<tr><td colspan="3" rowspan="4">Session</td><td colspan="4" valign="top">00</td><td colspan="7" valign="top">Inventory session S0</td></tr>
<tr><td colspan="4" valign="top">01</td><td colspan="7" valign="top">Inventory session S1</td></tr>
<tr><td colspan="4" valign="top">02</td><td colspan="7" valign="top">Inventory session S2</td></tr>
<tr><td colspan="4" valign="top">03</td><td colspan="7" valign="top">Inventory session S3</td></tr>
<tr><td colspan="3" rowspan="2">Target</td><td colspan="4" valign="top">00</td><td colspan="7" valign="top">目标A</td></tr>
<tr><td colspan="4" valign="top">01</td><td colspan="7" valign="top">目标B</td></tr>
<tr><td colspan="3" rowspan="2">Read Mode</td><td colspan="4" valign="top">00</td><td colspan="7" valign="top"><p>单标签模式，无Session控制；</p><p>最快的速度。 目标和Session的值将被忽略</p></td></tr>
<tr><td colspan="4" valign="top">01</td><td colspan="7" valign="top">单标签模式，有Session控制</td></tr>
<tr><td colspan="3"></td><td colspan="4" valign="top">02</td><td colspan="7" valign="top">多标签模式，有Session控制</td></tr>
<tr><td colspan="3">Timeout</td><td colspan="11" valign="top">标签响应超时控制， 单位：ms，默认值：5ms</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包：此数据可能返回多条。数量等于读取的标签数量(无重复数据)。 

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>TagCount</b> </th><th colspan="1" valign="top"><b>DataLen</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>ReadLen</b></th><th colspan="1" valign="top"><b>AntID</b></th><th colspan="1" valign="top"><b>ReadCount</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x81</td><td colspan="1" valign="top">2 Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">N Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="11"></td></tr>
<tr><td colspan="2" rowspan="6">参数说明</td><td colspan="2">TagCount</td><td colspan="7" valign="top">成功操作的标签总数。16 bits。</td></tr>
<tr><td colspan="2">DataLen</td><td colspan="7" valign="top">所操作标签的有效数据长度。（PC+CRC+EPC+读取的标签数据）。 单位是字节。</td></tr>
<tr><td colspan="2">Data</td><td colspan="7" valign="top"><p>所操作标签的有效数据。</p><p>PC (2字节) + EPC (根据标签规格) + CRC (2字节)  + 读取的数据。</p><p>(PC(2字节)  + EPC + CRC(2字节)  即EPC存储区域中的全部内容。)</p></td></tr>
<tr><td colspan="2">ReadLen</td><td colspan="7" valign="top">Read操作的数据长度。单位是字节。</td></tr>
<tr><td colspan="2">AntID</td><td colspan="7" valign="top">高6位是第一次读取的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">ReadCount</td><td colspan="7" valign="top">该标签被成功操作的次数。</td></tr>
</table>

**注意：**

**★相同EPC的标签，若读取的数据不相同，则被视为不同的标签。**

**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x81|||
|||||||
|参数说明|ErrorCode |错误代码||||
**




<a name="_toc328405866"></a><a name="_toc328407246"></a><a name="_toc328407289"></a><a name="_toc328407336"></a><a name="_toc328407763"></a><a name="_toc328407808"></a><a name="_toc328407853"></a><a name="_toc328407898"></a><a name="_toc523831778"></a>**2.2.3 cmd\_write**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>PassWord</b> </th><th colspan="1" valign="top"><b>MemBank</b></th><th colspan="1" valign="top"><b>WordAdd</b></th><th colspan="1" valign="top"><b>WordCnt</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x82 </td><td colspan="1" valign="top">4 Bytes </td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"> </td><td colspan="1" valign="top">WordCnt *2</td><td colspan="1"></td></tr>
<tr><td colspan="10"></td></tr>
<tr><td colspan="2" rowspan="7">参数说明</td><td colspan="4">PassWord</td><td colspan="4" valign="top">标签访问密码，4字节。</td></tr>
<tr><td colspan="4" rowspan="4"><p>MenBank</p><p>(标签存储区域)</p></td><td colspan="2" valign="top">0x00</td><td colspan="2" valign="top">RESERVED</td></tr>
<tr><td colspan="2" valign="top">0x01</td><td colspan="2" valign="top">EPC</td></tr>
<tr><td colspan="2" valign="top">0x02</td><td colspan="2" valign="top">TID</td></tr>
<tr><td colspan="2" valign="top">0x03</td><td colspan="2" valign="top">USER</td></tr>
<tr><td colspan="4"><p>WordAdd</p><p>(数据首地址)</p></td><td colspan="4" valign="top"><p>WORD(16 bits)地址。</p><p>写入EPC存储区域一般从02开始，该区域前四个字节存放PC+CRC。</p></td></tr>
<tr><td colspan="4">WordCnt (写入的字长度)</td><td colspan="4" valign="top">WORD(16 bits)长度，数值请参考标签规格。</td></tr>
</table>

或者使用Block Write（**默认并推荐**）:

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>PassWord</b> </th><th colspan="1" valign="top"><b>MemBank</b></th><th colspan="1" valign="top"><b>WordAdd</b></th><th colspan="1" valign="top"><b>WordCnt</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x94 </td><td colspan="1" valign="top">4 Bytes </td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"> </td><td colspan="1" valign="top">WordCnt *2</td><td colspan="1"></td></tr>
<tr><td colspan="10"></td></tr>
<tr><td colspan="2" rowspan="7">参数说明</td><td colspan="4">PassWord</td><td colspan="4" valign="top">标签访问密码，4字节。</td></tr>
<tr><td colspan="4" rowspan="4"><p>MenBank</p><p>(标签存储区域)</p></td><td colspan="2" valign="top">0x00</td><td colspan="2" valign="top">RESERVED</td></tr>
<tr><td colspan="2" valign="top">0x01</td><td colspan="2" valign="top">EPC</td></tr>
<tr><td colspan="2" valign="top">0x02</td><td colspan="2" valign="top">TID</td></tr>
<tr><td colspan="2" valign="top">0x03</td><td colspan="2" valign="top">USER</td></tr>
<tr><td colspan="4"><p>WordAdd</p><p>(数据首地址)</p></td><td colspan="4" valign="top"><p>WORD(16 bits)地址。</p><p>写入EPC存储区域一般从02开始，该区域前四个字节存放PC+CRC。</p></td></tr>
<tr><td colspan="4">WordCnt (写入的字长度)</td><td colspan="4" valign="top">WORD(16 bits)长度，数值请参考标签规格。</td></tr>
</table>

**★两种指令写标签方式不同，相互独立。**

**★推荐使用Block Write指令，效率更高。**

**◆操作成功：**

读写器返回数据包：此数据可能返回多条。数量等于写入的标签数量(无重复数据)。

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>TagCount</b> </th><th colspan="1" valign="top"><b>DataLen</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>ErrCode</b></th><th colspan="1" valign="top"><b>AntID</b></th><th colspan="1" valign="top"><b>WriteCount</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x82</td><td colspan="1" valign="top">2 Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">N Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="11"></td></tr>
<tr><td colspan="2" rowspan="6">参数说明</td><td colspan="2">TagCount</td><td colspan="7" valign="top">成功操作的标签总数。16 bits。</td></tr>
<tr><td colspan="2">DataLen</td><td colspan="7" valign="top">所操作标签的有效数据长度。（PC+CRC+EPC）。 单位是字节。</td></tr>
<tr><td colspan="2">Data</td><td colspan="7" valign="top"><p>所操作标签有效数据。</p><p>PC(2字节)  + EPC (根据标签规格)  + CRC (2字节) </p><p>( PC(2字节) + EPC + CRC (2字节)  即EPC存储区域中的全部内容。)</p></td></tr>
<tr><td colspan="2">ErrCode</td><td colspan="7" valign="top">所操作标签的操作结果，即错误代码。</td></tr>
<tr><td colspan="2">AntID</td><td colspan="7" valign="top">高6位是第一次读取的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">` `WriteCount</td><td colspan="7" valign="top">该标签被操作的次数。</td></tr>
</table>

或者Block Write：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>TagCount</b> </th><th colspan="1" valign="top"><b>DataLen</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>ErrCode</b></th><th colspan="1" valign="top"><b>AntID</b></th><th colspan="1" valign="top"><b>WriteCount</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x94</td><td colspan="1" valign="top">2 Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">N Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="11"></td></tr>
<tr><td colspan="2" rowspan="6">参数说明</td><td colspan="2">TagCount</td><td colspan="7" valign="top">成功操作的标签总数。16 bits。</td></tr>
<tr><td colspan="2">DataLen</td><td colspan="7" valign="top">所操作标签的有效数据长度。（PC+CRC+EPC）。 单位是字节。</td></tr>
<tr><td colspan="2">Data</td><td colspan="7" valign="top"><p>所操作标签有效数据。</p><p>PC(2字节)  + EPC (根据标签规格)  + CRC (2字节) </p><p>( PC(2字节) + EPC + CRC (2字节)  即EPC存储区域中的全部内容。)</p></td></tr>
<tr><td colspan="2">ErrCode</td><td colspan="7" valign="top">所操作标签的操作结果，即错误代码。</td></tr>
<tr><td colspan="2">AntID</td><td colspan="7" valign="top">高6位是第一次读取的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">` `WriteCount</td><td colspan="7" valign="top">该标签被操作的次数。</td></tr>
</table>

**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||<p>` `0x82</p><p>（或者0x94）</p>|||
|||||||
|参数说明|ErrorCode |错误代码||||
**



<a name="_toc328405867"></a><a name="_toc328407247"></a><a name="_toc328407290"></a><a name="_toc328407337"></a><a name="_toc328407764"></a><a name="_toc328407809"></a><a name="_toc328407854"></a><a name="_toc328407899"></a><a name="_toc523831779"></a>**2.2.4 cmd\_lock**
**


`	`上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>PassWord</b></th><th colspan="1" valign="top"><b>Menbank</b></th><th colspan="1" valign="top"><b>LockType</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x09</td><td colspan="1"></td><td colspan="1">0x83</td><td colspan="1" valign="top">4 Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="1" rowspan="10">参数说明</td><td colspan="4">PassWord</td><td colspan="3">标签访问密码，4字节。</td></tr>
<tr><td colspan="4" rowspan="5"><p>Menbank</p><p>(操作的数据区域)</p></td><td colspan="1" valign="top">0x01</td><td colspan="2" valign="top">User Memory</td></tr>
<tr><td colspan="1" valign="top">0x02</td><td colspan="2" valign="top">TID Memory</td></tr>
<tr><td colspan="1" valign="top">0x03</td><td colspan="2" valign="top">EPC Memory</td></tr>
<tr><td colspan="1" valign="top">0x04</td><td colspan="2" valign="top">Access Password</td></tr>
<tr><td colspan="1" valign="top">0x05</td><td colspan="2" valign="top">Kill Password</td></tr>
<tr><td colspan="4" rowspan="4"><p>LockType</p><p>(锁操作类型)</p></td><td colspan="1" valign="top">0x00</td><td colspan="2" valign="top">开放</td></tr>
<tr><td colspan="1" valign="top">0x01</td><td colspan="2" valign="top">锁定</td></tr>
<tr><td colspan="1" valign="top">0x02</td><td colspan="2" valign="top">永久开放</td></tr>
<tr><td colspan="1" valign="top">0x03</td><td colspan="2" valign="top">永久锁定</td></tr>
</table>

**◆操作成功：**

读写器返回数据包：此数据可能返回多条。数量等于锁定的标签数量(无重复数据)。

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>TagCount</b> </th><th colspan="1" valign="top"><b>DataLen</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>ErrCode</b></th><th colspan="1" valign="top"><b>AntID</b></th><th colspan="1" valign="top"><b>LockCount</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x83</td><td colspan="1" valign="top">2 Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">N Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="11"></td></tr>
<tr><td colspan="2" rowspan="6">参数说明</td><td colspan="2">TagCount</td><td colspan="7" valign="top">成功操作的标签总数。16 bits。</td></tr>
<tr><td colspan="2">DataLen</td><td colspan="7" valign="top">所操作标签的有效数据长度。（PC+CRC+EPC）。 单位是字节。</td></tr>
<tr><td colspan="2">Data</td><td colspan="7" valign="top"><p>所操作标签有效数据。</p><p>PC(2字节)  + EPC (根据标签规格 + CRC (2字节)) </p><p>( PC (2字节) + EPC  + CRC (2字节) 即EPC存储区域中的全部内容。)</p></td></tr>
<tr><td colspan="2">ErrCode</td><td colspan="7" valign="top">所操作标签的操作结果，即错误代码。</td></tr>
<tr><td colspan="2">AntID</td><td colspan="7" valign="top">高6位是第一次读取的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">LockCount</td><td colspan="7" valign="top">该标签被操作的次数。</td></tr>
</table>

**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x83|||
|||||||
|参数说明|ErrorCode |错误代码||||
**



<a name="_toc328405868"></a><a name="_toc328407248"></a><a name="_toc328407291"></a><a name="_toc328407338"></a><a name="_toc328407765"></a><a name="_toc328407810"></a><a name="_toc328407855"></a><a name="_toc328407900"></a><a name="_toc523831780"></a>**2.2.5 cmd\_kill**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**PassWord**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x07||0x84|4 Bytes||
|||||||
|参数说明|PassWord|标签销毁密码||||

**◆操作成功：**

读写器返回数据包：

此数据可能返回多条。数量等于销毁的标签数量(无重复数据)。

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>TagCount</b> </th><th colspan="1" valign="top"><b>DataLen</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>ErrCode</b></th><th colspan="1" valign="top"><b>AntID</b></th><th colspan="1" valign="top"><b>KillCount</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x84</td><td colspan="1" valign="top">2 Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">N Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="11"></td></tr>
<tr><td colspan="2" rowspan="6">参数说明</td><td colspan="2">TagCount</td><td colspan="7" valign="top">成功操作的标签总数。16 bits。</td></tr>
<tr><td colspan="2">DataLen</td><td colspan="7" valign="top">所操作标签的有效数据长度。（PC+CRC+EPC）。 单位是字节。</td></tr>
<tr><td colspan="2">Data </td><td colspan="7" valign="top"><p>所操作标签有效数据。</p><p>PC(2字节)  + EPC (根据标签规格 + CRC (2字节)) </p><p>( PC(2字节)  + EPC + CRC (2字节) 即EPC存储区域中的全部内容。)</p></td></tr>
<tr><td colspan="2">ErrCode</td><td colspan="7" valign="top">所操作标签的操作结果，即错误代码。</td></tr>
<tr><td colspan="2">AntID</td><td colspan="7" valign="top">高6位是第一次读取的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">KillCount</td><td colspan="7" valign="top">销毁标签操作只能为1。</td></tr>
</table>



**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x84|||
|||||||
|参数说明|ErrorCode |错误代码||||

**                                                                               


<a name="_toc328405869"></a><a name="_toc328407249"></a><a name="_toc328407292"></a><a name="_toc328407339"></a><a name="_toc328407766"></a><a name="_toc328407811"></a><a name="_toc328407856"></a><a name="_toc328407901"></a><a name="_toc523831781"></a>**2.2.6 cmd\_set\_access\_epc\_match**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>Mode</b></th><th colspan="1" valign="top"><b>EpcLen</b></th><th colspan="1" valign="top"><b>Epc</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x85</td><td colspan="2"></td><td colspan="1" valign="top"></td><td colspan="1">EpcLen个字节</td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="2" rowspan="2">Mode</td><td colspan="2">0x00</td><td colspan="4">EPC匹配一直有效，直到下一次刷新。</td></tr>
<tr><td colspan="2">0x01</td><td colspan="4">清除EPC匹配。</td></tr>
<tr><td colspan="2">EpcLen</td><td colspan="6">EPC长度。</td></tr>
<tr><td colspan="2">Epc</td><td colspan="6">EPC号，由EpcLen个字节组成。</td></tr>
</table>

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x85|||
|||||||
|参数说明|ErrorCode |错误代码||||

**                                                                               


<a name="_toc328405870"></a><a name="_toc328407250"></a><a name="_toc328407293"></a><a name="_toc328407340"></a><a name="_toc328407767"></a><a name="_toc328407812"></a><a name="_toc328407857"></a><a name="_toc328407902"></a><a name="_toc523831782"></a>**2.2.7 cmd\_get\_access\_epc\_match**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|||0x86||
||||||

读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>Status</b></th><th colspan="2" valign="top"><b>EpcLen</b></th><th colspan="1" valign="top"><b>EPC</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">` `0x86</td><td colspan="1" valign="top"></td><td colspan="2"></td><td colspan="1"></td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1" rowspan="2">Status</td><td colspan="4">0x00</td><td colspan="3">有匹配</td></tr>
<tr><td colspan="4">0x01</td><td colspan="3">无匹配</td></tr>
<tr><td colspan="1">EpcLen</td><td colspan="7">匹配的EPC号长度，无匹配时不返回此数据。</td></tr>
<tr><td colspan="1">EPC</td><td colspan="7">匹配的EPC号，无匹配时不返回此数据。</td></tr>
</table>
**


<a name="_toc523831783"></a>**2.2.8 cmd\_set\_impinj\_fast\_tid**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="2" valign="top"><b>Len</b></th><th colspan="2" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>FastTID</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="2">0x04</td><td colspan="2"></td><td colspan="1">0x8C</td><td colspan="2"></td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="2" rowspan="2">参数说明</td><td colspan="2" rowspan="2">FastTID</td><td colspan="3">` `除0x8D外的其他值</td><td colspan="2">关闭FastTID</td></tr>
<tr><td colspan="3">0x8D</td><td colspan="2">打开FastTID</td></tr>
</table>

**注意：**

**★此功能仅对 Impinj Monza 标签的部分型号有效。**

**★此功能在识别EPC的同时识别TID，因此大大提高了读TID的效率。**

**★打开此功能后，特定型号的标签会在盘存的过程中将TID打包到EPC中。因此，标签的PC会被修改，原来的PC+EPC变为：修改后的PC + EPC + (EPC的CRC) + TID。**

**★如果在识别TID的过程中出现错误，则上传原来的PC+EPC。**

**★如不需要此功能请将其关闭，避免不必要的时间消耗。**

**★此命令不保存至内部的Flash中，重启后将回复至Flash保存的值。**

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x8C|CommandSuccess ||

**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x8C|||
|||||||
|参数说明|ErrorCode |错误代码||||
**



<a name="_toc523831784"></a>**2.2.9 cmd\_set\_and\_save\_impinj\_fast\_tid**
**


请参考 cmd\_set\_impinj\_fast\_tid命令。

此命令将配置保存至内部的Flash中，断电不丢失。

上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="2" valign="top"><b>Len</b></th><th colspan="2" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>FastTID</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="2">0x04</td><td colspan="2"></td><td colspan="1">0x8D</td><td colspan="2"></td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="2" rowspan="2">参数说明</td><td colspan="2" rowspan="2">FastTID</td><td colspan="3">` `除0x8D外的其他值</td><td colspan="2">关闭FastTID</td></tr>
<tr><td colspan="3">0x8D</td><td colspan="2">打开FastTID</td></tr>
</table>

**注意：**

**★此功能仅对 Impinj Monza 标签的部分型号有效。**

**★此功能在识别EPC的同时识别TID，因此大大提高了读TID的效率。**

**★打开此功能后，特定型号的标签会在盘存的过程中将TID打包到EPC中。因此，标签的PC会被修改，原来的PC+EPC变为：修改后的PC + EPC + (EPC的CRC) + TID。**

**★如果在识别TID的过程中出现错误，则上传原来的PC+EPC。**

**★如不需要此功能请将其关闭，避免不必要的时间消耗。**

**★此命令不保存至内部的Flash中，重启后将回复至Flash保存的值。**

**◆操作成功：**

`	`读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x8D|CommandSuccess ||
**◆操作失败：**

读写器返回数据包：	

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x8D|||
|||||||
|参数说明|ErrorCode |错误代码||||
**


<a name="_toc523831785"></a>**2.2.10 cmd\_get\_impinj\_fast\_tid**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x8E||
**	读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="2" valign="top"><b>FastTID</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x04</td><td colspan="1"></td><td colspan="1">0x8E</td><td colspan="2" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="1" rowspan="2">FastTID</td><td colspan="3">0x8D</td><td colspan="2">FastTID打开</td></tr>
<tr><td colspan="3">0x00</td><td colspan="2">FastTID关闭</td></tr>
</table>
**



## <a name="_toc523831786"></a>**2.3 ISO 18000-6B 标签操作命令**
<a name="_toc523831787"></a>**2.3.1 cmd\_iso18000\_6b\_inventory**
**


上位机指令数据包：	

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0xB0||

读写器收到此命令后，进行ISO 18000 -6B多标签识别操作。标签数据不存入读写器缓存区。

`	`如有标签应答，返回如下数据包(多条)：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>AntID</b></th><th colspan="1" valign="top"><b>UID</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x0C</td><td colspan="1"></td><td colspan="1">0xB0</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">8 bytes</td><td colspan="1" valign="top"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="2" rowspan="2">参数说明</td><td colspan="2">AntID</td><td colspan="3" valign="top">工作天线号。</td></tr>
<tr><td colspan="2">UID</td><td colspan="3" valign="top">ISO 18000-6B 标签的8字节UID。</td></tr>
</table>

命令完成，读写器返回如下数据包：

**◆操作成功：**

读写器返回数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>AntID</b> </th><th colspan="1" valign="top"><b>TagFound</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x05</td><td colspan="1"></td><td colspan="1">` `0xB0</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="1">AntID</td><td colspan="5">此次盘存使用的天线号。</td></tr>
<tr><td colspan="1">TagFound</td><td colspan="5">盘存到的标签数量。</td></tr>
</table>

**◆操作失败：**

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0xB0|||
|||||||
|参数说明|ErrorCode |错误代码||||
**









<a name="_toc523831788"></a>**2.3.2 cmd\_iso18000\_6b\_read**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>UID</b></th><th colspan="1" valign="top"><b>StartAddress</b></th><th colspan="1" valign="top"><b>Length</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">` `0x0D</td><td colspan="1"></td><td colspan="1">0xB1</td><td colspan="1" valign="top">8 bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="1" rowspan="3">参数说明</td><td colspan="2">UID</td><td colspan="5">被操作标签的UID。</td></tr>
<tr><td colspan="2">StartAddress</td><td colspan="5">要读取的数据首地址。</td></tr>
<tr><td colspan="2">Length</td><td colspan="5">要读取的数据长度。</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包： 

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>AntID</b> </th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"> </td><td colspan="1"></td><td colspan="1">` `0xB1</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">N bytes</td><td colspan="1"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="1">AntID</td><td colspan="5">此次读操作使用的天线号。</td></tr>
<tr><td colspan="1">Data</td><td colspan="5">读出的数据。</td></tr>
</table>



**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0xB1|||
|||||||
|参数说明|ErrorCode |错误代码||||

**                                                                               












<a name="_toc523831789"></a>**2.3.3 cmd\_iso18000\_6b\_write**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>UID</b></th><th colspan="1" valign="top"><b>StartAddress</b></th><th colspan="1" valign="top"><b>Length</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0xB2</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"> </td><td colspan="1">N bytes</td><td colspan="1"></td></tr>
<tr><td colspan="9"></td></tr>
<tr><td colspan="2" rowspan="4">参数说明</td><td colspan="2">UID</td><td colspan="5" valign="top">被操作标签的UID。</td></tr>
<tr><td colspan="2">StartAddress</td><td colspan="5" valign="top">写入数据的首地址。</td></tr>
<tr><td colspan="2">Length</td><td colspan="5" valign="top">写入数据的长度。</td></tr>
<tr><td colspan="2">Data</td><td colspan="5" valign="top">写入的数据。</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包： 

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="1" valign="top"><b>AntID</b> </th><th colspan="1" valign="top"><b>WrittenCount</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">0x05</td><td colspan="1"></td><td colspan="1">0xB2</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"> </td><td colspan="1"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="1">AntID</td><td colspan="5">此次写操作使用的天线号。</td></tr>
<tr><td colspan="1">WrittenCount</td><td colspan="5">成功写入的字节数。</td></tr>
</table>

**注意：**

**★可以一次性写入多个字节。一旦写入某个字节出现错误，此命令不会继续写入后面的数据。同时命令返回已经成功写入的字节数。**

**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0xB2|||
|||||||
|参数说明|ErrorCode |错误代码||||


**                                                                               








<a name="_toc523831790"></a>**2.3.4 cmd\_iso18000\_6b\_lock**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>UID</b></th><th colspan="1" valign="top"><b>LockAddress</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">` `0x0C</td><td colspan="1"></td><td colspan="1">0xB3</td><td colspan="1" valign="top">8 bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="2">UID</td><td colspan="4">被操作标签的UID。</td></tr>
<tr><td colspan="2">LockAddress</td><td colspan="4">被锁定的地址。</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包： 

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="2" valign="top"><b>AntID</b> </th><th colspan="1" valign="top"><b>Status</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">` `0x05</td><td colspan="1"></td><td colspan="1">` `0xB3</td><td colspan="2" valign="top"></td><td colspan="1" valign="top"> </td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="1" rowspan="4">参数说明</td><td colspan="1">AntID</td><td colspan="6">此次盘存使用的天线号。</td></tr>
<tr><td colspan="1" rowspan="2">Status</td><td colspan="3">0x00</td><td colspan="3">该字节成功锁定。</td></tr>
<tr><td colspan="3">0xFE</td><td colspan="3">该字节已是锁定状态。</td></tr>
<tr><td colspan="1"></td><td colspan="3">0xFF</td><td colspan="3">该字节无法锁定。</td></tr>
</table>



**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0xB3|||
|||||||
|参数说明|ErrorCode |错误代码||||

**                                                                              











<a name="_toc523831791"></a>**2.3.5 cmd\_iso18000\_6b\_query\_lock**
**


上位机指令数据包：

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>UID</b></th><th colspan="1" valign="top"><b>QueryAddress</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">` `0x0C</td><td colspan="1"></td><td colspan="1">0xB4</td><td colspan="1" valign="top">8 bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td></tr>
<tr><td colspan="7"></td></tr>
<tr><td colspan="1" rowspan="2">参数说明</td><td colspan="2">UID</td><td colspan="4">被操作标签的UID。</td></tr>
<tr><td colspan="2">QueryAddress</td><td colspan="4">要查询的地址。</td></tr>
</table>

**◆操作成功：**

`	`读写器返回数据包： 

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b></th><th colspan="2" valign="top"><b>AntID</b> </th><th colspan="1" valign="top"><b>Status</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1">` `0x05</td><td colspan="1"></td><td colspan="1">` `0xB4</td><td colspan="2" valign="top"></td><td colspan="1" valign="top"> </td><td colspan="1"></td></tr>
<tr><td colspan="8"></td></tr>
<tr><td colspan="1" rowspan="3">参数说明</td><td colspan="1">AntID</td><td colspan="6">此次盘存使用的天线号。</td></tr>
<tr><td colspan="1" rowspan="2">Status</td><td colspan="3">0x00</td><td colspan="3">该字节未锁定。</td></tr>
<tr><td colspan="3">0xFE</td><td colspan="3">该字节已是锁定状态。</td></tr>
</table>



**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0xB4|||
|||||||
|参数说明|ErrorCode |错误代码||||

**                                                                               








## <a name="_toc328402772"></a><a name="_toc328402820"></a><a name="_toc328405872"></a><a name="_toc328407252"></a><a name="_toc328407295"></a><a name="_toc328407342"></a><a name="_toc328407769"></a><a name="_toc328407814"></a><a name="_toc328407859"></a><a name="_toc328407904"></a><a name="_toc523831792"></a>**2.4 缓存操作命令**
<a name="_toc328405873"></a><a name="_toc328407253"></a><a name="_toc328407296"></a><a name="_toc328407343"></a><a name="_toc328407770"></a><a name="_toc328407815"></a><a name="_toc328407860"></a><a name="_toc328407905"></a><a name="_toc523831793"></a>**2.4.1 cmd\_get\_inventory\_buffer**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x90||

**◆操作成功：**

读写器返回数据包：

此数据可能返回多条。数量等于缓存中的标签数量(无重复数据)。

<table><tr><th colspan="1" valign="top"><b>Head</b></th><th colspan="1" valign="top"><b>Len</b></th><th colspan="1" valign="top"><b>Address</b></th><th colspan="1" valign="top"><b>Cmd</b> </th><th colspan="1" valign="top"><b>TagCount</b> </th><th colspan="1" valign="top"><b>DataLen</b></th><th colspan="1" valign="top"><b>Data</b></th><th colspan="1" valign="top"><b>RSSI</b></th><th colspan="1"><b>FreqAnt</b></th><th colspan="1" valign="top"><b>InvCount</b></th><th colspan="1" valign="top"><b>Check</b></th></tr>
<tr><td colspan="1">0xA0</td><td colspan="1"></td><td colspan="1"></td><td colspan="1">0x90</td><td colspan="1" valign="top">2 Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">N Bytes</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1"></td></tr>
<tr><td colspan="11"></td></tr>
<tr><td colspan="2" rowspan="6">参数说明</td><td colspan="2">TagCount</td><td colspan="7" valign="top">成功操作的标签总数。16 bits。</td></tr>
<tr><td colspan="2">DataLen</td><td colspan="7" valign="top">所操作标签的有效数据长度。（PC+CRC+EPC）。 单位是字节。</td></tr>
<tr><td colspan="2">Data</td><td colspan="7" valign="top"><p>所操作标签有效数据。</p><p>PC(2字节) + EPC (根据标签规格 + CRC (2字节)) </p><p>( PC(2字节) + EPC  +  CRC (2字节) 即EPC存储区域中的全部内容。)</p></td></tr>
<tr><td colspan="2">RSSI</td><td colspan="7" valign="top">第一次读到该标签时的信号强度。此参数对应的dBm值请参见RSSI参数对应表。</td></tr>
<tr><td colspan="2">FreqAnt</td><td colspan="7" valign="top">高6位是第一次读取的频点参数，低2位是天线号。</td></tr>
<tr><td colspan="2">InvCount</td><td colspan="7" valign="top">该标签成功读取的次数，如果该值为0xFF，则说明成功读取次数 >= 255次。</td></tr>
</table>



**注意：**

**★命令完成后，缓存中的数据并不丢失，可以多次提取。**

**★若再次运行cmd\_inventory 命令，则盘存到的标签将累计存入缓存。**

**★若再次运行其他的18000-6C命令，缓存中的数据将被清空。**



**◆操作失败：**

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd**|**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||` `0x90|||
|||||||
|参数说明|ErrorCode |错误代码||||
**



<a name="_toc328407254"></a><a name="_toc328407297"></a><a name="_toc328407344"></a><a name="_toc328407771"></a><a name="_toc328407816"></a><a name="_toc328407861"></a><a name="_toc328407906"></a><a name="_toc523831794"></a>**2.4.2 cmd\_get\_and\_reset\_inventory\_buffer**
**


数据格式请参考cmd\_get\_inventory\_buffer命令。

命令成功完成后，缓存中的数据将被全部清空。
**


<a name="_toc328407255"></a><a name="_toc328407298"></a><a name="_toc328407345"></a><a name="_toc328407772"></a><a name="_toc328407817"></a><a name="_toc328407862"></a><a name="_toc328407907"></a><a name="_toc523831795"></a>**2.4.3 cmd\_get\_inventory\_buffer\_tag\_count**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x92||

**◆操作成功：**

读写器返回数据包：



|**Head**|**Len**|**Address**|**Cmd**|**TagCount**|**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x05||` `0x92|2 Bytes||
|||||||
|参数说明|TagCount|缓存中标签数据数量。无重复数据。||||

**                                                                               

<a name="_toc328405874"></a><a name="_toc328407256"></a><a name="_toc328407299"></a><a name="_toc328407346"></a><a name="_toc328407773"></a><a name="_toc328407818"></a><a name="_toc328407863"></a><a name="_toc328407908"></a><a name="_toc523831796"></a>**2.4.4 cmd\_reset\_inventory\_buffer**
**


上位机指令数据包：

|**Head**|**Len**|**Address**|**Cmd** |**Check**|
| :-: | :-: | :-: | :-: | :-: |
|0xA0|0x03||0x93||

读写器返回数据包：

|**Head**|**Len**|**Address**|**Cmd** |**ErrorCode** |**Check**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|0xA0|0x04||0x93|CommandSuccess ||

**                                                                               
# <a name="_toc328402773"></a><a name="_toc328402821"></a><a name="_toc328405877"></a><a name="_toc328407259"></a><a name="_toc328407302"></a><a name="_toc328407349"></a><a name="_toc328407776"></a><a name="_toc328407821"></a><a name="_toc328407866"></a><a name="_toc328407911"></a><a name="_toc523831797"></a>**3错误代码表**

|**序号**|**值**|**名 称**|**描    叙**|
| :-: | :-: | :-: | :-: |
|1|0x10|command\_success|命令成功完成|
|2|0x11|command\_fail|命令执行失败|
|3|0x20|mcu\_reset\_error|CPU复位错误|
|4|0x21|cw\_on\_error|打开CW错误|
|5|0x22|antenna\_missing\_error|天线未连接|
|6|0x23|write\_flash\_error|写Flash错误|
|7|0x24|read\_flash\_error|读Flash错误|
|8|0x25|set\_output\_power\_error|设置发射功率错误|
|9|0x31|tag\_inventory\_error|盘存标签错误|
|10|0x32|tag\_read\_error|读标签错误|
|11|0x33|tag\_write\_error|写标签错误|
|12|0x34|tag\_lock\_error|锁定标签错误|
|13|0x35|tag\_kill\_error|灭活标签错误|
|14|0x36|no\_tag\_error|无可操作标签错误|
|15|0x37|inventory\_ok\_but\_access\_fail|成功盘存但访问失败|
|16|0x38|buffer\_is\_empty\_error|缓存为空|
|17|0x3C|nxp\_custom\_command\_fail|NXP芯片自定义指令失败|
|18|0x40|access\_or\_password\_error|访问标签错误或访问密码错误|
|19|0x41|parameter\_invalid|无效的参数|
|20|0x42|parameter\_invalid\_wordCnt\_too\_long|wordCnt参数超过规定长度|
|21|0x43|parameter\_invalid\_membank\_out\_of\_range|MemBank参数超出范围|
|22|0x44|parameter\_invalid\_lock\_region\_out\_of\_range|Lock数据区参数超出范围|
|23|0x45|parameter\_invalid\_lock\_action\_out\_of\_range|LockType参数超出范围|
|24|0x46|parameter\_reader\_address\_invalid|读写器地址无效|
|25|0x47|parameter\_invalid\_antenna\_id\_out\_of\_range|Antenna\_id 超出范围|
|26|0x48|parameter\_invalid\_output\_power\_out\_of\_range|输出功率参数超出范围|
|27|0x49|parameter\_invalid\_frequency\_region\_out\_of\_range|射频规范区域参数超出范围|
|28|0x4A|parameter\_invalid\_baudrate\_out\_of\_range|波特率参数超出范围|
|29|0x4B|parameter\_beeper\_mode\_out\_of\_range|蜂鸣器设置参数超出范围|
|30|0x4C|parameter\_epc\_match\_len\_too\_long|EPC匹配长度越界|
|31|0x4D|parameter\_epc\_match\_len\_error|EPC匹配长度错误|
|32|0x4E|parameter\_invalid\_epc\_match\_mode|EPC匹配参数超出范围|
|33|0x4F|parameter\_invalid\_frequency\_range|频率范围设置参数错误|
|34|0x50|fail\_to\_get\_RN16\_from\_tag|无法接收标签的RN16|
|35|0x51|parameter\_invalid\_drm\_mode|DRM设置参数错误|
|36|0x52|pll\_lock\_fail|PLL不能锁定|
|37|0x53|rf\_chip\_fail\_to\_response |射频芯片无响应|
|38|0x54|fail\_to\_achieve\_desired\_output\_power|输出达不到指定的输出功率|
|39|0x55|copyright\_authentication\_fail|版权认证未通过|
|40|0x56|spectrum\_regulation\_error|频谱规范设置错误|
|41|0x57|output\_power\_too\_low|输出功率过低|
|42|0xEE|fail\_to\_get\_rf\_port\_return\_loss|测量回波损耗失败|















# <a name="_toc328402774"></a><a name="_toc328402822"></a><a name="_toc328405878"></a><a name="_toc328407260"></a><a name="_toc328407303"></a><a name="_toc328407350"></a><a name="_toc328407777"></a><a name="_toc328407822"></a><a name="_toc328407867"></a><a name="_toc328407912"></a><a name="_toc523831798"></a>**4频率参数对应表**

|**频率参数**|**对应频点**|**频率参数**|**对应频点**|
| :-: | :-: | :-: | :-: |
|0(0x00)|865\.00 MHz|30(0x1E)|913\.50 MHz|
|1(0x01)|865\.50 MHz|31(0x1F)|914\.00 MHz|
|2(0x02)|866\.00 MHz|32(0x20)|914\.50 MHz|
|3(0x03)|866\.50 MHz|33(0x21)|915\.00 MHz|
|4(0x04)|867\.00 MHz|34(0x22)|915\.50 MHz|
|5(0x05)|867\.50 MHz|35(0x23)|916\.00 MHz|
|6(0x06)|868\.00 MHz|36(0x24)|916\.50 MHz|
|7(0x07)|902\.00 MHz|37(0x25)|917\.00 MHz|
|8(0x08)|902\.50 MHz|38(0x26)|917\.50 MHz|
|9(0x09)|903\.00 MHz|39(0x27)|918\.00 MHz|
|10(0x0A)|903\.50 MHz|40(0x28)|918\.50 MHz|
|11(0x0B)|904\.00 MHz|41(0x29)|919\.00 MHz|
|12(0x0C)|904\.50 MHz|42(0x2A)|919\.50 MHz|
|13(0x0D)|905\.00 MHz|43(0x2B)|920\.00 MHz|
|14(0x0E)|905\.50 MHz|44(0x2C)|920\.50 MHz|
|15(0x0F)|906\.00 MHz|45(0x2D)|921\.00 MHz|
|16(0x10)|906\.50 MHz|46(0x2E)|921\.50 MHz|
|17(0x11)|907\.00 MHz|47(0x2F)|922\.00 MHz|
|18(0x12)|907\.50 MHz|48(0x30)|922\.50 MHz|
|19(0x13)|908\.00 MHz|49(0x31)|923\.00 MHz|
|20(0x14)|908\.50 MHz|50(0x32)|923\.50 MHz|
|21(0x15)|909\.00 MHz|51(0x33)|924\.00 MHz|
|22(0x16)|909\.50 MHz|52(0x34)|924\.50 MHz|
|23(0x17)|910\.00 MHz|53(0x35)|925\.00 MHz|
|24(0x18)|910\.50 MHz|54(0x36)|925\.50 MHz|
|25(0x19)|911\.00 MHz|55(0x37)|926\.00 MHz|
|26(0x1A)|911\.50 MHz|56(0x38)|926\.50 MHz|
|27(0x1B)|912\.00 MHz|57(0x39)|927\.00 MHz|
|28(0x1C)|912\.50 MHz|58(0x3A)|927\.50 MHz|
|29(0x1D)|913\.00 MHz|59(0x3B)|928\.00 MHz|









# <a name="_toc328402775"></a><a name="_toc328402823"></a><a name="_toc328405879"></a><a name="_toc328407261"></a><a name="_toc328407304"></a><a name="_toc328407351"></a><a name="_toc328407778"></a><a name="_toc328407823"></a><a name="_toc328407868"></a><a name="_toc328407913"></a><a name="_toc523831799"></a>**5 RSSI参数对应表**

|**RSSI参数**|**对应信号强度（对数）**|**RSSI参数**|**对应信号强度（对数）**|
| :-: | :-: | :-: | :-: |
|98(0x62)|-31dBm|64(0x40)|-65dBm|
|97(0x61)|-32dBm|63(0x3F)|-66dBm|
|96(0x60)|-33dBm|62(0x3E)|-67dBm|
|95(0x5F)|-34dBm|61(0x3D)|-68dBm|
|94(0x5E)|-35dBm|60(0x3C)|-69dBm|
|93(0x5D)|-36dBm|59(0x3B)|-70dBm|
|92(0x5C)|-37dBm|58(0x3A)|-71dBm|
|91(0x5B)|-38dBm|57(0x39)|-72dBm|
|90(0x5A)|-39dBm|56(0x38)|-73dBm|
|89(0x59)|-40dBm|55(0x37)|-74dBm|
|88(0x58)|-41dBm|54(0x36)|-75dBm|
|87(0x57)|-42dBm|53(0x35)|-76dBm|
|86(0x56)|-43dBm|52(0x34)|-77dBm|
|85(0x55)|-44dBm|51(0x33)|-78dBm|
|84(0x54)|-45dBm|50(0x32)|-79dBm|
|83(0x53)|-46dBm|49(0x31)|-80dBm|
|82(0x52)|-47dBm|48(0x30)|-81dBm|
|81(0x51)|-48dBm|47(0x2F)|-82dBm|
|80(0x50)|-49dBm|46(0x2E)|-83dBm|
|79(0x4F)|-50dBm|45(0x2D)|-84dBm|
|78(0x4E)|-51dBm|44(0x2C)|-85dBm|
|77(0x4D)|-52dBm|43(0x2B)|-86dBm|
|76(0x4C)|-53dBm|42(0x2A)|-87dBm|
|75(0x4B)|-54dBm|41(0x29)|-88dBm|
|74(0x4A)|-55dBm|40(0x28)|-89dBm|
|73(0x49)|-56dBm|39(0x27)|-90dBm|
|72(0x48)|-57dBm|38(0x26)|-91dBm|
|71(0x47)|-58dBm|37(0x25)|-92dBm|
|70(0x46)|-59dBm|36(0x24)|-93dBm|
|69(0x45)|-60dBm|35(0x23)|-94dBm|
|68(0x44)|-61dBm|34(0x22)|-95dBm|
|67(0x43)|-62dBm|33(0x21)|-96dBm|
|66(0x42)|-63dBm|32(0x20)|-97dBm|
|65(0x41)|-64dBm|31(0x1F)|-98dBm|






# <a name="_toc328402776"></a><a name="_toc328402824"></a><a name="_toc328405880"></a><a name="_toc328407262"></a><a name="_toc328407305"></a><a name="_toc328407352"></a><a name="_toc328407779"></a><a name="_toc328407824"></a><a name="_toc328407869"></a><a name="_toc328407914"></a><a name="_toc523831800"></a>**6 校验和计算方法(C语言描述)**


unsigned char  CheckSum(unsigned char \*uBuff, unsigned char uBuffLen)

{

`	`unsigned char i,uSum=0;

`	`for(i=0;i<uBuffLen;i++)

`	`{

`		`uSum = uSum + uBuff[i];

`	`}

`	`uSum = (~uSum) + 1;

`	`return uSum;

}


标签过滤（cmd_tag_select）指令总结
一、设置掩码（Set mask(s)）
主机数据包（Host packet）结构：
字段	描述
Head	固定为0xA0
Len	命令长度，取决于掩码长度：
- 若MaskBitLen % 8 = 0，则Len = 11 + MaskBitLen/8
- 否则Len = 11 + MaskBitLen/8 + 1
Add	读写器地址
Cmd	固定为0x98
Function	功能码：
0x01=设置标签掩码1
0x02=设置标签掩码2
0x03=设置标签掩码3
0x04=设置标签掩码4
0x05=设置标签掩码5
Target	目标对象：
0x00=已盘点的S0
0x01=已盘点的S1
0x02=已盘点的S2
0x03=已盘点的S3
0x04=SL
Action	操作类型：
参考操作类型表
MemBank	存储区：
0x00=保留
0x01=EPC存储区
0x02=TID存储区
0x03=USER存储区
StartingMaskAdd	默认值32
MaskBitLen	掩码位长度
Mask	掩码内容
Truncate	默认为0
CC	校验码
操作类型表

Action	 	Tag Matches Mask 	Tag Doesn’t Match Mask
	0x00	Assert SL or inventoried ->A	Deassert SL or inventoried ->B
	0x01	Assert SL or inventoried ->A	Do nothing
	0x02	Do nothing	Deassert SL or inventoried->B
	0x03	Negate SL or(A->B,B->A)	Do nothing
	0x04	Deassert SL or inventoried ->B	Assert SL or inventoried->A
	0x05	Deaaert SL or inventoried ->B	Do nothing
	0x06	Do nothing	Assert SL or inventoried ->A
	0x07	Do nothing	Negate SL or (A->B, B->A)

响应数据包（Response packet）结构：
字段	描述
Head	固定为0xA0
Len	固定为0x04
Address	（未明确说明，可能为地址字段）
Cmd	固定为0x98
ErrorCode	错误码，固定为0x10
Check	校验码

二、清除掩码（Clear mask(s)）
主机数据包（Host packet）结构：
字段	描述
Head	固定为0xA0
Len	固定为0x04
Address	读写器地址
Cmd	固定为0x98
Function	功能码：
0x00=清除所有掩码
0x01~0x05=清除对应编号（1~5）的掩码
Check	校验码
响应数据包（Response packet）结构：
与“设置掩码”响应结构一致：
 Head=0xA0，Len=0x04，Cmd=0x98，ErrorCode=0x10，Check=校验码。
三、查询当前掩码（Query current mask(s)）
主机数据包（Host packet）结构：
字段	描述
Head	固定为0xA0
Len	固定为0x04
Address	读写器地址
Cmd	固定为0x98
Function	固定为0x20
Check	校验码
响应数据包（Response packet）结构：
若存在掩码：
返回多个数据包（数量等于掩码总数），单包结构如下：
字段	描述
Head	固定为0xA0
Len	取决于掩码长度：Len = 11 + MaskBitLen/8
Add	读写器地址
Cmd	固定为0x98
Mask ID	0x01=标签掩码1
0x02=标签掩码2
0x03=标签掩码3
0x04=标签掩码4
0x05=标签掩码5
Mask quantity	已设置的掩码总数
Target	同“设置掩码”描述
Action	同“设置掩码”描述
MemBank	同“设置掩码”描述
StartingMaskAdd	同“设置掩码”描述
MaskBitLen	同“设置掩码”描述
Mask	同“设置掩码”描述
Truncate	同“设置掩码”描述
CC	校验码
若无掩码：
返回固定结构：
字段	描述
Head	固定为0xA0
Len	固定为0x0B
Add	读写器地址
Cmd	固定为0x98
Mask quantity	固定为0x00（无掩码）
CC	校验码

注：以上表格中未标注“（未明确说明）”的字段，描述均基于用户提供的参数说明翻译或直接保留原字段名。

