<UserControl x:Class="RFID_UI.Views.UserControls.FastAntennaControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:local="clr-namespace:RFID_UI.Views.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="500" d:DesignWidth="600"
             d:DataContext="{d:DesignInstance local:FastAntennaViewModel}">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="0">

            <!-- Session选择 -->
            <Grid Margin="2,2,2,2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>

                <ui:TextBlock Grid.Column="0" Text="Session:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                <ComboBox Grid.Column="1"
                         ItemsSource="{Binding SessionOptions}"
                         SelectedItem="{Binding SelectedSession}"
                         FontSize="14"/>
            </Grid>

            <!-- Target选择 -->
            <Grid Margin="2,2,2,2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <ui:TextBlock Grid.Column="0" Text="Target:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <RadioButton Content="A"
                                IsChecked="{Binding IsTargetASelected}"
                                FontSize="14" Margin="2,2,2,2"/>
                    <RadioButton Content="B"
                                IsChecked="{Binding IsTargetBSelected}"
                                FontSize="14"/>
                </StackPanel>
            </Grid>

            <!-- 相位检测 -->
            <CheckBox Content="启用相位检测"
                     IsChecked="{Binding EnablePhase}"
                     FontSize="14" Margin="2,2,2,2"/>

            <!-- 临时功率设置 -->
            <StackPanel Margin="2,2,2,2">
                <CheckBox Content="启用临时功率"
                         IsChecked="{Binding EnableTempPower}"
                         FontSize="14" Margin="2,2,2,2"/>

                <Grid Margin="2,2,2,2" IsEnabled="{Binding EnableTempPower}">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="70"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ui:TextBlock Grid.Column="0" Text="功率值:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                    <ui:TextBox Grid.Column="1"
                               Text="{Binding TempPowerValue}"
                               FontSize="14" Margin="2,2,2,2"
                               ToolTip="临时功率范围: 20-33 dBm"/>
                    <ui:TextBlock Grid.Column="2" Text="dBm (20-33)" VerticalAlignment="Center"/>
                </Grid>

                <!-- SL参数 - 只在启用临时功率时显示 -->
                <StackPanel Margin="2,2,2,2" Visibility="{Binding EnableTempPower, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <CheckBox Content="启用SL参数"
                             IsChecked="{Binding EnableSL}"
                             FontSize="14" Margin="2,2,2,2"/>

                    <Grid Margin="2,2,2,2" IsEnabled="{Binding EnableSL}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="120"/>
                        </Grid.ColumnDefinitions>

                        <ui:TextBlock Grid.Column="0" Text="SL值:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                        <ComboBox Grid.Column="1"
                                 ItemsSource="{Binding SLOptions}"
                                 SelectedItem="{Binding SelectedSL}"
                                 FontSize="14"/>
                    </Grid>
                </StackPanel>
            </StackPanel>

            <!-- 其他高级参数 -->
            <StackPanel Margin="2,2,2,2">
                <!-- 间隔时间 -->
                <Grid Margin="2,2,2,2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="70"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ui:TextBlock Grid.Column="0" Text="间隔时间:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                    <ui:TextBox Grid.Column="1"
                               Text="{Binding IntervalTime}"
                               FontSize="14" Margin="2,2,2,2"/>
                    <ui:TextBlock Grid.Column="2" Text="ms" VerticalAlignment="Center"/>
                </Grid>

                <!-- 重复次数 -->
                <Grid Margin="2,2,2,2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="70"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ui:TextBlock Grid.Column="0" Text="重复次数:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                    <ui:TextBox Grid.Column="1"
                               Text="{Binding RepeatCount}"
                               FontSize="14" Margin="2,2,2,2"/>
                    <ui:TextBlock Grid.Column="2" Text="次" VerticalAlignment="Center"/>
                </Grid>


            </StackPanel>

            <!-- 8天线配置 -->
            <Grid Margin="2,2,2,2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 天线配置标题 -->
                <ui:TextBlock Grid.Row="0" Text="天线配置" FontSize="14" Margin="2,2,2,2"/>

                <!-- 天线复选框和停留时间 -->
                <Grid Grid.Row="1" Margin="2,2,2,2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左列：天线A-D -->
                    <StackPanel Grid.Column="0" Margin="2,2,2,2">
                        <!-- 天线A (固定参数0) -->
                        <StackPanel Orientation="Horizontal" Margin="2,2,2,2">
                            <CheckBox IsChecked="{Binding AntennaAEnabled}"
                                      Margin="0,0,2,0"
                                      VerticalAlignment="Center"/>
                            <ui:TextBlock Text="天线A"
                                          FontSize="14"
                                          VerticalAlignment="Center"
                                          Margin="0,0,2,0"/>
                            <ui:TextBox Text="{Binding StayTimeA}"
                                        FontSize="14"
                                        Width="70"
                                        IsEnabled="{Binding AntennaAEnabled}"
                                        Margin="0,0,2,0"/>
                        </StackPanel>

                        <!-- 天线B (固定参数1) -->
                        <StackPanel Orientation="Horizontal" Margin="2,2,2,2">
                            <CheckBox IsChecked="{Binding AntennaBEnabled}"
                                      Margin="0,0,2,0"
                                      VerticalAlignment="Center"/>
                            <ui:TextBlock Text="天线B"
                                          FontSize="14"
                                          VerticalAlignment="Center"
                                          Margin="0,0,2,0"/>
                            <ui:TextBox Text="{Binding StayTimeB}"
                                        FontSize="14"
                                        Width="70"
                                        IsEnabled="{Binding AntennaBEnabled}"
                                        Margin="0,0,2,0"/>
                        </StackPanel>

                        <!-- 天线C (固定参数2) -->
                        <StackPanel Orientation="Horizontal" Margin="2,2,2,2">
                            <CheckBox IsChecked="{Binding AntennaCEnabled}"
                                      Margin="0,0,2,0"
                                      VerticalAlignment="Center"/>
                            <ui:TextBlock Text="天线C"
                                          FontSize="14"
                                          VerticalAlignment="Center"
                                          Margin="0,0,2,0"/>
                            <ui:TextBox Text="{Binding StayTimeC}"
                                        FontSize="14"
                                        Width="70"
                                        IsEnabled="{Binding AntennaCEnabled}"
                                        Margin="0,0,2,0"/>

                        </StackPanel>

                        <!-- 天线D (固定参数3) -->
                        <StackPanel Orientation="Horizontal" Margin="2,2,2,2">
                            <CheckBox IsChecked="{Binding AntennaDEnabled}"
                                      Margin="0,0,2,0"
                                      VerticalAlignment="Center"/>
                            <ui:TextBlock Text="天线D"
                                          FontSize="14"
                                          VerticalAlignment="Center"
                                          Margin="0,0,2,0"/>
                            <ui:TextBox Text="{Binding StayTimeD}"
                                        FontSize="14"
                                        Width="70"
                                        IsEnabled="{Binding AntennaDEnabled}"
                                        Margin="0,0,2,0"/>

                        </StackPanel>
                        </StackPanel>
                        
                    <!-- 右列：天线E-H -->
                    <StackPanel Grid.Column="1" Margin="5,0,0,0">
                        <!-- 天线E (固定参数4) -->
                        <StackPanel Orientation="Horizontal" Margin="2,2,2,2">
                            <CheckBox IsChecked="{Binding AntennaEEnabled}"
                                      Margin="0,0,2,0"
                                      VerticalAlignment="Center"/>
                            <ui:TextBlock Text="天线E"
                                          FontSize="14"
                                          VerticalAlignment="Center"
                                          Margin="0,0,2,0"/>
                            <ui:TextBox Text="{Binding StayTimeE}"
                                        FontSize="14"
                                        Width="70"
                                        IsEnabled="{Binding AntennaEEnabled}"
                                        Margin="0,0,2,0"/>
                        </StackPanel>

                        <!-- 天线F (固定参数5) -->
                        <StackPanel Orientation="Horizontal" Margin="2,2,2,2">
                            <CheckBox IsChecked="{Binding AntennaFEnabled}"
                                      Margin="0,0,2,0"
                                      VerticalAlignment="Center"/>
                            <ui:TextBlock Text="天线F"
                                          FontSize="14"
                                          VerticalAlignment="Center"
                                          Margin="0,0,2,0"/>
                            <ui:TextBox Text="{Binding StayTimeF}"
                                        FontSize="14"
                                        Width="70"
                                        IsEnabled="{Binding AntennaFEnabled}"
                                        Margin="0,0,2,0"/>
                        </StackPanel>

                        <!-- 天线G (固定参数6) -->
                        <StackPanel Orientation="Horizontal" Margin="2,2,2,2">
                            <CheckBox IsChecked="{Binding AntennaGEnabled}"
                                      Margin="0,0,2,0"
                                      VerticalAlignment="Center"/>
                            <ui:TextBlock Text="天线G"
                                          FontSize="14"
                                          VerticalAlignment="Center"
                                          Margin="0,0,2,0"/>
                            <ui:TextBox Text="{Binding StayTimeG}"
                                        FontSize="14"
                                        Width="70"
                                        IsEnabled="{Binding AntennaGEnabled}"
                                        Margin="0,0,2,0"/>
                        </StackPanel>

                        <!-- 天线H (固定参数7) -->
                        <StackPanel Orientation="Horizontal" Margin="2,2,2,2">
                            <CheckBox IsChecked="{Binding AntennaHEnabled}"
                                      Margin="0,0,2,0"
                                      VerticalAlignment="Center"/>
                            <ui:TextBlock Text="天线H"
                                          FontSize="14"
                                          VerticalAlignment="Center"
                                          Margin="0,0,2,0"/>
                            <ui:TextBox Text="{Binding StayTimeH}"
                                        FontSize="14"
                                        Width="70"
                                        IsEnabled="{Binding AntennaHEnabled}"
                                        Margin="0,0,2,0"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>

                <!-- 批量设置轮询次数 -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <ui:TextBlock Grid.Column="0" Text="轮询次数:" VerticalAlignment="Center" Margin="2,2,2,2"/>
                    <ui:TextBox Grid.Column="1"
                               Text="{Binding BatchStayTime}"
                               FontSize="14"
                               Width="70"
                               Margin="2,2,2,2"/>
                    <ui:Button Grid.Column="2"
                              Content="应用到所有天线"
                              Command="{Binding ApplyBatchStayTimeCommand}"
                              FontSize="14"/>
                </Grid>
            </Grid>
        </StackPanel>
    </ScrollViewer>
</UserControl>
