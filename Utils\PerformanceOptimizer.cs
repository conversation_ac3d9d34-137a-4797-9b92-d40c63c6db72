using System;
using System.Diagnostics;
using System.Threading;

namespace RFID_UI.Utils
{
    /// <summary>
    /// 性能优化器 - 自适应调整系统参数
    /// </summary>
    public class PerformanceOptimizer
    {
        private readonly PerformanceCounter? _cpuCounter;
        private readonly PerformanceCounter? _memoryCounter;
        private long _lastGCMemory = 0;
        private DateTime _lastOptimization = DateTime.Now;
        
        // 性能阈值
        private const double HIGH_CPU_THRESHOLD = 80.0;
        private const double HIGH_MEMORY_THRESHOLD = 500 * 1024 * 1024; // 500MB
        private const int OPTIMIZATION_INTERVAL_SECONDS = 10;

        // 自适应参数
        public int BatchUpdateInterval { get; private set; } = 100; // ms
        public int MaxPendingUpdates { get; private set; } = 1000;
        public bool EnableDetailedLogging { get; private set; } = true;
        public int CircularBufferSize { get; private set; } = 16384;

        public PerformanceOptimizer()
        {
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
                _lastGCMemory = GC.GetTotalMemory(false);
            }
            catch
            {
                // 性能计数器可能不可用，使用默认值
            }
        }

        /// <summary>
        /// 检查并优化性能参数
        /// </summary>
        public void OptimizeIfNeeded()
        {
            if (DateTime.Now - _lastOptimization < TimeSpan.FromSeconds(OPTIMIZATION_INTERVAL_SECONDS))
                return;

            try
            {
                var cpuUsage = GetCPUUsage();
                var memoryUsage = GetMemoryUsage();
                var gcMemory = GC.GetTotalMemory(false);
                var memoryGrowth = gcMemory - _lastGCMemory;

                // CPU优化
                if (cpuUsage > HIGH_CPU_THRESHOLD)
                {
                    // 降低更新频率
                    BatchUpdateInterval = Math.Min(BatchUpdateInterval + 50, 500);
                    EnableDetailedLogging = false;
                    Console.WriteLine($"🔧 CPU使用率高({cpuUsage:F1}%)，调整批量更新间隔到{BatchUpdateInterval}ms");
                }
                else if (cpuUsage < 30.0 && BatchUpdateInterval > 100)
                {
                    // 提高更新频率
                    BatchUpdateInterval = Math.Max(BatchUpdateInterval - 25, 50);
                    Console.WriteLine($"🔧 CPU使用率正常({cpuUsage:F1}%)，调整批量更新间隔到{BatchUpdateInterval}ms");
                }

                // 内存优化
                if (memoryGrowth > HIGH_MEMORY_THRESHOLD)
                {
                    // 减少缓存大小
                    MaxPendingUpdates = Math.Max(MaxPendingUpdates - 200, 100);
                    EnableDetailedLogging = false;
                    
                    // 强制垃圾回收
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                    
                    Console.WriteLine($"🔧 内存增长过快({memoryGrowth / 1024 / 1024:F1}MB)，减少缓存大小到{MaxPendingUpdates}");
                }
                else if (memoryGrowth < 50 * 1024 * 1024 && MaxPendingUpdates < 1000) // 50MB
                {
                    // 增加缓存大小
                    MaxPendingUpdates = Math.Min(MaxPendingUpdates + 100, 1000);
                    EnableDetailedLogging = true;
                }

                _lastGCMemory = gcMemory;
                _lastOptimization = DateTime.Now;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"性能优化异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取CPU使用率
        /// </summary>
        private double GetCPUUsage()
        {
            try
            {
                return _cpuCounter?.NextValue() ?? 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// 获取内存使用情况
        /// </summary>
        private double GetMemoryUsage()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                return process.WorkingSet64;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// 获取性能报告
        /// </summary>
        public string GetPerformanceReport()
        {
            try
            {
                var cpuUsage = GetCPUUsage();
                var memoryUsage = GetMemoryUsage() / 1024 / 1024; // MB
                var gcMemory = GC.GetTotalMemory(false) / 1024 / 1024; // MB

                return $"CPU: {cpuUsage:F1}%, 内存: {memoryUsage:F1}MB, GC内存: {gcMemory:F1}MB, " +
                       $"批量间隔: {BatchUpdateInterval}ms, 缓存大小: {MaxPendingUpdates}";
            }
            catch
            {
                return "性能数据不可用";
            }
        }

        /// <summary>
        /// 重置为默认参数
        /// </summary>
        public void ResetToDefaults()
        {
            BatchUpdateInterval = 100;
            MaxPendingUpdates = 1000;
            EnableDetailedLogging = true;
            CircularBufferSize = 16384;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _cpuCounter?.Dispose();
                _memoryCounter?.Dispose();
            }
            catch
            {
                // 忽略释放异常
            }
        }
    }
}
