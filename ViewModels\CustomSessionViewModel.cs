using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace RFID_UI.ViewModels
{
    public class CustomSessionViewModel : INotifyPropertyChanged
    {
        // Session选择
        private bool _isS0Selected = false;
        private bool _isS1Selected = true;  // 默认S1
        private bool _isS2Selected = false;
        private bool _isS3Selected = false;

        // Target选择
        private bool _isTargetASelected = true;  // 默认A
        private bool _isTargetBSelected = false;

        // 基础参数
        private int _repeatCount = 1;

        // 扩展参数
        private bool _useSL = false;
        private string _selectedSL = "00";
        private bool _enablePhase = false;

        public ObservableCollection<string> SLOptions { get; }
        public ObservableCollection<string> SessionOptions { get; }

        // Session下拉框选择
        private string _selectedSession = "S1";
        public string SelectedSession
        {
            get => _selectedSession;
            set
            {
                _selectedSession = value;
                // 同步更新RadioButton状态
                _isS0Selected = value == "S0";
                _isS1Selected = value == "S1";
                _isS2Selected = value == "S2";
                _isS3Selected = value == "S3";
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsS0Selected));
                OnPropertyChanged(nameof(IsS1Selected));
                OnPropertyChanged(nameof(IsS2Selected));
                OnPropertyChanged(nameof(IsS3Selected));
            }
        }

        // Session属性
        public bool IsS0Selected
        {
            get => _isS0Selected;
            set
            {
                _isS0Selected = value;
                if (value) { _isS1Selected = _isS2Selected = _isS3Selected = false; }
                OnPropertyChanged();
            }
        }

        public bool IsS1Selected
        {
            get => _isS1Selected;
            set
            {
                _isS1Selected = value;
                if (value) { _isS0Selected = _isS2Selected = _isS3Selected = false; }
                OnPropertyChanged();
            }
        }

        public bool IsS2Selected
        {
            get => _isS2Selected;
            set
            {
                _isS2Selected = value;
                if (value) { _isS0Selected = _isS1Selected = _isS3Selected = false; }
                OnPropertyChanged();
            }
        }

        public bool IsS3Selected
        {
            get => _isS3Selected;
            set
            {
                _isS3Selected = value;
                if (value) { _isS0Selected = _isS1Selected = _isS2Selected = false; }
                OnPropertyChanged();
            }
        }

        // Target属性
        public bool IsTargetASelected
        {
            get => _isTargetASelected;
            set
            {
                _isTargetASelected = value;
                if (value) { _isTargetBSelected = false; }
                OnPropertyChanged();
            }
        }

        public bool IsTargetBSelected
        {
            get => _isTargetBSelected;
            set
            {
                _isTargetBSelected = value;
                if (value) { _isTargetASelected = false; }
                OnPropertyChanged();
            }
        }

        // 其他参数属性
        public int RepeatCount
        {
            get => _repeatCount;
            set
            {
                _repeatCount = value;
                OnPropertyChanged();
            }
        }

        public bool UseSL
        {
            get => _useSL;
            set
            {
                _useSL = value;

                // 禁用SL时自动禁用相位（协议要求）
                if (!value && _enablePhase)
                {
                    EnablePhase = false;
                }

                OnPropertyChanged();
            }
        }

        public string SelectedSL
        {
            get => _selectedSL;
            set
            {
                _selectedSL = value;
                OnPropertyChanged();
            }
        }

        public bool EnablePhase
        {
            get => _enablePhase;
            set
            {
                _enablePhase = value;

                // 启用相位时自动启用SL（协议要求）
                if (value && !_useSL)
                {
                    UseSL = true;
                    // 如果SL值为空或无效，设置默认值
                    if (string.IsNullOrEmpty(_selectedSL) || _selectedSL == "请选择")
                    {
                        SelectedSL = "00"; // 设置默认SL值
                    }
                }

                OnPropertyChanged();
            }
        }





        public CustomSessionViewModel()
        {
            SLOptions = new ObservableCollection<string>
            {
                "00", "01", "02", "03"
            };

            SessionOptions = new ObservableCollection<string>
            {
                "S0", "S1", "S2", "S3"
            };
        }

        /// <summary>
        /// 获取Session值
        /// </summary>
        public byte GetSessionValue()
        {
            if (_isS0Selected) return 0;
            if (_isS1Selected) return 1;
            if (_isS2Selected) return 2;
            if (_isS3Selected) return 3;
            return 1; // 默认S1
        }

        /// <summary>
        /// 获取Target值
        /// </summary>
        public byte GetTargetValue()
        {
            return _isTargetASelected ? (byte)0 : (byte)1;
        }

        /// <summary>
        /// 获取SL值
        /// </summary>
        public byte GetSLValue()
        {
            return byte.TryParse(_selectedSL, out byte result) ? result : (byte)0;
        }

        /// <summary>
        /// 获取相位值
        /// </summary>
        public byte GetPhaseValue()
        {
            return _enablePhase ? (byte)1 : (byte)0;
        }

        /// <summary>
        /// 重置所有参数到默认值
        /// </summary>
        public void ResetToDefaults()
        {
            IsS0Selected = false;
            IsS1Selected = true;
            IsS2Selected = false;
            IsS3Selected = false;

            IsTargetASelected = true;
            IsTargetBSelected = false;

            RepeatCount = 1;
            UseSL = false;
            SelectedSL = "00";
            EnablePhase = false;
        }



        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
