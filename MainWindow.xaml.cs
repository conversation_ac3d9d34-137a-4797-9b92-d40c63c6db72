using System.Windows;
using Wpf.Ui;
using Wpf.Ui.Controls;

namespace RFID_UI
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : FluentWindow
    {
        public MainWindow()
        {
            InitializeComponent();
            
            // 设置默认的DataContext
            DataContext = new ViewModels.MainWindowViewModel();
            
            // 在窗口加载完成后初始化页面
            Loaded += MainWindow_Loaded;
            
            // 应用主题
            Loaded += (sender, args) =>
            {
                Wpf.Ui.Appearance.SystemThemeWatcher.Watch(
                    this,
                    Wpf.Ui.Controls.WindowBackdropType.Mica,
                    true
                );
            };
        }
        
        public MainWindow(Services.PageService pageService, ViewModels.MainWindowViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // 手动设置导航
            SetupNavigation(pageService);
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取MainWindowViewModel来访问日志功能
                var mainViewModel = DataContext as ViewModels.MainWindowViewModel;

                // 传递日志功能给ViewModels
                Action<string>? logAction = mainViewModel != null ? mainViewModel.AddLog : null;

                // 创建一个临时的AdvancedCommandsViewModel来获取ShowSerialLogs函数
                ViewModels.AdvancedCommandsViewModel? tempAdvancedViewModel = null;

                // 创建ReaderManager，使用延迟绑定的日志控制函数
                var readerManager = new ReaderManager("COM1", 115200, logAction, () => tempAdvancedViewModel?.ShowSerialLogs ?? false);
                var pageService = new Services.PageService(null!);

                // 创建最终的AdvancedCommandsViewModel
                var advancedCommandsViewModel = new ViewModels.AdvancedCommandsViewModel(readerManager, logAction);
                tempAdvancedViewModel = advancedCommandsViewModel; // 设置引用
                var readerSettingsViewModel = new ViewModels.ReaderSettingsViewModel(readerManager, logAction);
                var inventoryTestViewModel = new ViewModels.InventoryTestViewModel(readerManager, logAction);

                // 设置AdvancedCommandsViewModel引用到MainWindowViewModel
                mainViewModel?.SetAdvancedCommandsViewModel(advancedCommandsViewModel);

                var readerSettingsPage = new Views.Pages.ReaderSettingsPage(readerSettingsViewModel);
                var advancedCommandsPage = new Views.Pages.AdvancedCommandsPage(advancedCommandsViewModel);
                var inventoryTestPage = new Views.Pages.InventoryTestPage(inventoryTestViewModel);

                // 设置页面内容
                if (ReaderSettingsFrame != null)
                    ReaderSettingsFrame.Content = readerSettingsPage;

                if (AdvancedCommandsFrame != null)
                    AdvancedCommandsFrame.Content = advancedCommandsPage;

                if (InventoryTestFrame != null)
                    InventoryTestFrame.Content = inventoryTestPage;

                // 添加初始化日志
                mainViewModel?.AddLog("🚀 RFID UI 应用程序已启动");

                // 延迟订阅自动滚动事件，确保UI完全加载
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    if (mainViewModel != null)
                    {
                        mainViewModel.ScrollToEnd += () =>
                        {
                            // 在UI线程中执行滚动
                            Dispatcher.BeginInvoke(new Action(() =>
                            {
                                if (LogScrollViewer != null && LogScrollViewer.IsLoaded)
                                {
                                    LogScrollViewer.ScrollToEnd();
                                }
                            }), System.Windows.Threading.DispatcherPriority.Background);
                        };

                        // 立即滚动到底部（处理已有的日志）
                        if (LogScrollViewer != null && LogScrollViewer.IsLoaded)
                        {
                            LogScrollViewer.ScrollToEnd();
                        }
                    }
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (System.Exception ex)
            {
                System.Windows.MessageBox.Show($"页面初始化错误: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void LogTextBox_Loaded(object sender, RoutedEventArgs e)
        {
            // TextBox加载完成后，通过ScrollViewer滚动到底部
            if (LogScrollViewer != null)
            {
                LogScrollViewer.ScrollToEnd();
            }
        }

        private void LogScrollViewer_Loaded(object sender, RoutedEventArgs e)
        {
            // ScrollViewer加载完成后，立即滚动到底部
            if (sender is System.Windows.Controls.ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        }

        private void SetupNavigation(Services.PageService pageService)
        {
            // 暂时简化导航 - 直接显示第一个页面
            var readerSettingsPage = pageService.GetPage<Views.Pages.ReaderSettingsPage>();
            if (readerSettingsPage != null)
            {
                // 将页面添加到主内容区域
                var frame = new System.Windows.Controls.Frame();
                frame.Content = readerSettingsPage;
                
                // 这里需要找到合适的容器来放置内容
                // 暂时先不实现导航，让应用能够运行起来
            }
        }
    }
}
