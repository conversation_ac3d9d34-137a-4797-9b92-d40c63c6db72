# 超高频读写器串行接口通讯协议 V3.8 (UHF RFID Reader Serial Interface Protocol)

## 目录

- [1. 通信协议结构](#1-通信协议结构)
  - [1.1. RS232 参数设置](#11-rs232-参数设置)
  - [1.2. 数据包格式定义](#12-数据包格式定义)
- [2. 指令集定义](#2-指令集定义)
  - [2.1. 系统设置指令](#21-系统设置指令)
  - [2.2. 18000-6C 标签操作命令](#22-18000-6c-标签操作命令)
  - [2.3. ISO 18000-6B 标签操作命令](#23-iso-18000-6b-标签操作命令)
  - [2.4. 缓存操作命令](#24-缓存操作命令)
- [3. 错误代码表](#3-错误代码表)
- [4. 频率参数对应表](#4-频率参数对应表)
- [5. RSSI 参数对应表](#5-rssi-参数对应表)
- [6. 校验和计算方法(C语言描述)](#6-校验和计算方法c语言描述)

---

---

## 2. 指令集定义

### 2.1. 系统设置指令

#### 2.1.1. cmd_reset

- **功能**: 复位读写器。
- **上位机指令**: `A0 03 70 [Check]`
- **操作成功**: 无数据返回，读写器重启，蜂鸣器响一声。
- **操作失败**: `A0 04 70 [ErrorCode] [Check]`

#### 2.1.2. cmd_set_uart_baudrate

- **功能**: 设置串口通讯波特率。
- **上位机指令**: 

| Head | Len | Address | Cmd | BaudRate | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x71` | 1 Byte | |

  - **BaudRate**:
    - `0x03`: 38400 bps
    - `0x04`: 115200 bps

- **操作成功**: `A0 04 71 CommandSuccess [Check]`
  - 读写器成功收到此命令帧后，用先前波特率返回应答数据包，然后重新启动读写器。新的波特率保存在内部 FLASH 中，断电不丢失。
- **操作失败**: `A0 04 71 ErrorCode [Check]`

#### 2.1.3. cmd_get_firmware_version

- **功能**: 读取读写器固件版本。
- **上位机指令**: `A0 03 72 [Check]`
- **读写器返回**: 

| Head | Len | Address | Cmd | Major | Minor | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x05` | | `0x72` | 1 Byte | 1 Byte | |

  - **Major**: 固件主版本号。
  - **Minor**: 固件次版本号。

#### 2.1.4. cmd_set_reader_address

- **功能**: 设置读写器地址。
- **上位机指令**: 

| Head | Len | Address | Cmd | Address | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x73` | 1 Byte | |

  - **Address**: 读写器地址，取值范围 0-254。

- **操作成功**: `A0 04 73 CommandSuccess [Check]`
  - 新的读写器地址立即生效，并被写入 FLASH 保存，断电不丢失。
- **操作失败**: `A0 04 73 ErrorCode [Check]`

#### 2.1.5. cmd_set_work_antenna

- **功能**: 设置读写器工作天线。
- **上位机指令**: 

| Head | Len | Address | Cmd | AntennaID | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x74` | 1 Byte | |

  - **AntennaID** (天线号):
    - `0x00`: 天线 1
    - `0x01`: 天线 2
    - `0x02`: 天线 3
    - `0x03`: 天线 4

- **操作成功**: `A0 04 74 CommandSuccess [Check]`
- **操作失败**: `A0 04 74 ErrorCode [Check]`

#### 2.1.6. cmd_get_work_antenna

- **功能**: 查询当前天线工作天线。
- **上位机指令**: `A0 03 75 [Check]`
- **读写器返回**: 

| Head | Len | Address | Cmd | AntennaID | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x75` | 1 Byte | |

  - **AntennaID** (天线号):
    - `0x00`: 天线 1
    - `0x01`: 天线 2
    - `0x02`: 天线 3
    - `0x03`: 天线 4

#### 2.1.7. cmd_set_output_power

- **功能**: 设置读写器射频输出功率。
- **上位机指令**: 
  - **方式一 (设置所有天线为相同功率)**:

    | Head | Len | Address | Cmd | RfPower | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x04` | | `0x76` | 1 Byte | |

    - **RfPower**: RF 输出功率，取值范围 0-33 (0x00 - 0x21)，单位 dBm。

  - **方式二 (分别为每个天线设置功率)**:

    | Head | Len | Address | Cmd | Power1 | Power2 | Power3 | Power4 | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x07` | | `0x76` | 1 Byte | 1 Byte | 1 Byte | 1 Byte | |

    - **Power1-4**: 天线 1-4 输出功率，取值范围 0-33 (0x00 - 0x21)，单位 dBm。

- **操作成功**: `A0 04 76 CommandSuccess [Check]`
  - 操作成功后输出功率值将被保存在内部的 Flash 中，断电后不丢失。
  - **注意**: 此命令耗时将超过 100mS。如果需要动态改变射频输出功率，请使用 `cmd_set_temporary_output_power` 命令，否则将会影响 Flash 的使用寿命。
- **操作失败**: `A0 04 76 ErrorCode [Check]`

#### 2.1.8. cmd_get_output_power

- **功能**: 查询读写器当前输出功率。
- **上位机指令**: `A0 03 77 [Check]`
- **读写器返回**:
  - **如果所有天线的功率设置相同**:

    | Head | Len | Address | Cmd | OutputPower | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x04` | | `0x77` | 1 Byte | |

    - **OutputPower**: 读写器当前的射频输出功率。

  - **否则返回**:

    | Head | Len | Address | Cmd | Power1 | Power2 | Power3 | Power4 | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x07` | | `0x77` | 1 Byte | 1 Byte | 1 Byte | 1 Byte | |

    - **Power1-4**: 天线 1-4 输出功率，取值范围 0-33 (0x00 - 0x21)，单位 dBm。

#### 2.1.9. cmd_set_frequency_region

- **功能**: 设置读写器工作频率范围。
- **上位机指令**:
  - **方法一 (使用系统默认频点)**:

    | Head | Len | Address | Cmd | Region | StartFreq | EndFreq | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x06` | | `0x78` | 1 Byte | 1 Byte | 1 Byte | |

    - **Region** (射频规范):
      - `0x01`: FCC
      - `0x02`: ETSI
      - `0x03`: CHN
    - **StartFreq**: 频率起始点
    - **EndFreq**: 频率结束点
    - **规则**:
      1. 起始频率与结束频率不能超过射频规范的范围。
      2. 起始频率必须低于结束频率。
      3. 起始频率等于结束频率则定频发射。

  - **方法二 (用户自定义频谱)**:

    | Head | Len | Address | Cmd | Region | FreqSpace | RreqQuantity | StartFreq | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x09` | | `0x78` | `0x04` | 1 Byte | 1 Byte | 3 Bytes | |

    - **Region**: `0x04` (固定值)
    - **FreqSpace**: 频点间隔 = FreqSpace x 10KHz。
    - **RreqQuantity**: 频点数量 (包含起始频率)，必须大于 0。1 为定频发射。
    - **StartFreq**: 起始频率 (单位 KHz)，16 进制数高位在前。例如 915000KHz 则发送 `0D F6 38`。

- **操作成功**: `A0 04 78 CommandSuccess [Check]`
- **操作失败**: `A0 04 78 ErrorCode [Check]`

#### 2.1.10. cmd_get_frequency_region

- **功能**: 查询读写器工作频率范围。
- **上位机指令**: `A0 03 79 [Check]`
- **读写器返回**:
  - **如果使用的是系统默认频点**:

    | Head | Len | Address | Cmd | Region | StartFreq | EndFreq | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x06` | | `0x79` | 1 Byte | 1 Byte | 1 Byte | |

    - **Region** (射频规范):
      - `0x01`: FCC
      - `0x02`: ETSI
      - `0x03`: CHN
    - **StartFreq**: 跳频频率范围的低点。
    - **EndFreq**: 跳频频率范围的高点。

  - **如果使用的是自定义频点**:

    | Head | Len | Address | Cmd | Region | FreqSpace | RreqQuantity | StartFreq | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x09` | | `0x79` | `0x04` | 1 Byte | 1 Byte | 3 Bytes | |

    - **Region**: `0x04` (固定值)
    - **FreqSpace**: 频点间隔 = FreqSpace x 10KHz。
    - **RreqQuantity**: 频点数量 (包含起始频率)，必须大于 0。1 为定频发射。
    - **StartFreq**: 起始频率 (单位 KHz)，16 进制数高位在前。例如 915000KHz 则返回 `0D F6 38`。

#### 2.1.11. cmd_set_beeper_mode

- **功能**: 设置蜂鸣器状态。
- **上位机指令**: 

| Head | Len | Address | Cmd | Mode | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x7A` | 1 Byte | |

  - **Mode** (操作标签时蜂鸣器状态):
    - `0x00`: 安静
    - `0x01`: 每次盘存后鸣响
    - `0x02`: 每读到一张标签鸣响

- **操作成功**: `A0 04 7A CommandSuccess [Check]`
  - 操作成功后此配置将保存至内部 FLASH，断电后不丢失。
  - **注意**: 读到一张标签后蜂鸣器鸣响，会占用大量处理器时间，若此选项打开，将会明显影响到读多标签（防冲突算法）的性能，此选项应作为测试功能选用。
- **操作失败**: `A0 04 7A ErrorCode [Check]`

#### 2.1.12. cmd_get_reader_temperature

- **功能**: 查询当前设备的工作温度。
- **上位机指令**: `A0 03 7B [Check]`
- **操作成功**: 

| Head | Len | Address | Cmd | PlusMinus | Temp | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x05` | | `0x7B` | 1 Byte | 1 Byte | |

  - **PlusMinus**:
    - `0x00`: 零下
    - `0x01`: 零上
  - **Temp**: 摄氏度

- **操作失败**: `A0 04 7B ErrorCode [Check]`

#### 2.1.13. cmd_read_gpio_value

- **功能**: 读取 GPIO 电平。
- **上位机指令**: `A0 03 60 [Check]`
- **读写器返回**: 

| Head | Len | Address | Cmd | Gpio1 | Gpio2 | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x05` | | `0x60` | 1 Byte | 1 Byte | |

  - **Gpio1**:
    - `0x00`: Gpio1 的电平为低
    - `0x01`: Gpio1 的电平为高
  - **Gpio2**:
    - `0x00`: Gpio2 的电平为低
    - `0x01`: Gpio2 的电平为高

#### 2.1.14. cmd_write_gpio_value

- **功能**: 设置 GPIO 电平。
- **上位机指令**: 

| Head | Len | Address | Cmd | ChooseGpio | GpioValue | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x05` | | `0x61` | 1 Byte | 1 Byte | |

  - **ChooseGpio**:
    - `0x03`: 设置 GPIO 3
    - `0x04`: 设置 GPIO 4
  - **GpioValue**:
    - `0x00`: 设置为低电平
    - `0x01`: 设置为高电平

- **读写器返回**: `A0 04 61 ErrorCode [Check]`

#### 2.1.15. cmd_set_ant_connection_detector

- **功能**: 设置天线连接检测器状态。
- **上位机指令**: 

| Head | Len | Address | Cmd | DetectorSensitivity | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x62` | 1 Byte | |

  - **DetectorSensitivity**:
    - `0x00`: 关闭天线连接检测。
    - **其他值**: 天线连接检测的灵敏度 (端口回波损耗值)，单位 dB。值越大，对端口的阻抗匹配要求越高。

- **读写器返回**: `A0 04 62 ErrorCode [Check]`

#### 2.1.16. cmd_get_ant_connection_detector

- **功能**: 读取天线连接检测器状态。
- **上位机指令**: `A0 03 63 [Check]`
- **读写器返回**: 

| Head | Len | Address | Cmd | DetectorSensitivity | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x63` | 1 Byte | |

  - **DetectorSensitivity**:
    - `0x00`: 天线连接检测已关闭。
    - **其他值**: 天线连接检测的灵敏度 (端口回波损耗值)。

#### 2.1.17. cmd_set_temporary_output_power

- **功能**: 设置读写器临时射频输出功率。
- **上位机指令**: 

| Head | Len | Address | Cmd | RfPower | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x66` | 1 Byte | |

  - **RfPower**: RF 输出功率，取值范围 20-33 (0x14 - 0x21)，单位 dBm。

- **操作成功**: `A0 04 66 CommandSuccess [Check]`
  - 操作成功后输出功率值将不会被保存在内部的 Flash 中，重新启动或断电后输出功率将恢复至内部 Flash 中保存的输出功率值。此命令的操作速度非常快，并且不写 Flash，从而不影响 Flash 的使用寿命，适合需要反复切换射频输出功率的应用。
- **操作失败**: `A0 04 66 ErrorCode [Check]`

#### 2.1.18. cmd_set_reader_identifier

- **功能**: 设置读写器识别码。
- **上位机指令**: 

| Head | Len | Address | Cmd | Identifier | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x0F` | | `0x67` | 12 Bytes | |

  - **Identifier**: 12 字节的读写器识别字符。

- **操作成功**: `A0 04 67 CommandSuccess [Check]`
  - 操作成功后 12 字节的读写器识别字符串将会保存在内部的 Flash 中，断电后不丢失。
- **操作失败**: `A0 04 67 ErrorCode [Check]`

#### 2.1.19. cmd_get_reader_identifier

- **功能**: 读取读写器识别码。
- **上位机指令**: `A0 03 68 [Check]`
- **操作成功**: 

| Head | Len | Address | Cmd | Identifier | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x0F` | | `0x68` | 12 Bytes | |

  - **Identifier**: 12 字节的读写器识别字符。

#### 2.1.20. cmd_set_rf_link_profile

- **功能**: 设置射频链路的通讯速率。
- **上位机指令**: 

| Head | Len | Address | Cmd | ProfileID | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x69` | 1 Byte | |

  - **ProfileID**:
    - `0xD0`: Profile 0: Tari 25uS, FMO 40KHz。
    - `0xD1`: Profile 1: Tari 25uS, Miller 4 250KHz。(推荐设置, 系统默认)
    - `0xD2`: Profile 2: Tari 25uS, Miller 4 300KHz。
    - `0xD3`: Profile 3: Tari 6.25uS, FMO 400KHz。

- **操作成功**: `A0 04 69 CommandSuccess [Check]`
  - 操作成功后读写器会重新启动，配置保存在内部的 Flash 中，断电后不丢失。
- **操作失败**: `A0 04 69 ErrorCode [Check]`

#### 2.1.21. cmd_get_rf_link_profile

- **功能**: 读取射频链路的通讯速率。
- **上位机指令**: `A0 03 6A [Check]`
- **操作成功**: 

| Head | Len | Address | Cmd | ProfileID | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x6A` | 1 Byte | |

  - **ProfileID**:
    - `0xD0`: Profile 0: Tari 25uS, FMO 40KHz。
    - `0xD1`: Profile 1: Tari 25uS, Miller 4 250KHz。(推荐设置, 系统默认)
    - `0xD2`: Profile 2: Tari 25uS, Miller 4 300KHz。
    - `0xD3`: Profile 3: Tari 6.25uS, FMO 400KHz。

- **操作失败**: `A0 04 6A ErrorCode [Check]`

#### 2.1.22. cmd_get_rf_port_return_loss

- **功能**: 测量天线端口的回波损耗。
- **上位机指令**: 

| Head | Len | Address | Cmd | FreqParameter | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x7E` | 1 Byte | |

  - **FreqParameter**: 频率参数参见频率参数对应表。系统将获取此频点当前工作天线端口的回波损耗值。

- **操作成功**: 

| Head | Len | Address | Cmd | ReturnLoss | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x7E` | 1 Byte | |

  - **ReturnLoss**: 回波损耗值，单位是 dB。VSWR = (10^(RL/20) + 1) / (10^(RL/20) - 1)。

- **操作失败**: `A0 04 7E EE [Check]`

### 2.2. 18000-6C 标签操作命令

#### 2.2.1. 盘存指令合集

##### *******. cmd_inventory

- **模式**: 缓存模式
- **功能**: 读写器收到此命令后，进行多标签识别操作。标签数据存入读写器缓存区，使用提取缓存指令可获得标签数据，详见：2.4 缓存操作命令。
- **上位机指令**: 

| Head | Len | Address | Cmd | Repeat | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x80` | 1 Byte | |

  - **Repeat**: 盘存过程重复的次数。`0xFF` 则此轮盘存时间为最短时间。如果射频区域内只有一张标签，则此轮的盘存约耗时为 30-50mS。一般在四通道机器上快速轮询多个天线时使用此参数值。
  - **注意**: 将参数设置成 255 (0xFF) 时，将启动专为读少量标签设计的算法。对于少量标的应用来说，效率更高，反应更灵敏，但此参数不适合同时读取大量标签的应用。

- **操作成功**: 

| Head | Len | Address | Cmd | AntID | TagCount | ReadRate | TotalRead | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x0C` | | `0x80` | 1 Byte | 2 Bytes | 2 Bytes | 4 Bytes | |

  - **AntID**: 此次盘存使用的天线号。
  - **TagCount**: 识别标签的总数量，根据 EPC 号来区分标签，相同 EPC 号的标签将被视为同一张标签。若未清空缓存，标签数量为多次盘存操作的数量累加。
  - **ReadRate**: 此次执行命令的标签识别速度 (成功读取标签的次数/秒)。不区分是否多次读取同一张标签。
  - **TotalRead**: 此次执行命令的标签的总读取标签次数，不区分是否多次读取同一张标签。

- **操作失败**: `A0 04 80 ErrorCode [Check]`

##### *******. cmd_real_time_inventory

- **模式**: 实时模式 (Auto)
- **功能**: 读写器收到此命令后，进行多标签识别操作。标签数据实时上传，不存入读写器缓存区。此命令一轮盘存耗时较长，适用于大批量标签读取。
- **上位机指令**: 

| Head | Len | Address | Cmd | Repeat | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x04` | | `0x89` | 1 Byte | |

  - **Repeat**: 盘存过程重复的次数。`0xFF` 则此轮盘存时间为最短时间。如果射频区域内只有一张标签，则此轮的盘存约耗时为 30-50mS。一般在四通道机器上快速轮询多个天线时使用此参数值。
  - **注意**: 由于硬件为双 CPU 架构，主 CPU 负责轮询标签，副 CPU 负责数据管理。轮询标签和发送数据并行，互不占用对方的时间，因此串口的数据传输不影响读写器工作的效率。

- **标签应答**: (多条)

| Head | Len | Address | Cmd | FreqAnt | PC | EPC | RSSI | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | | | `0x89` | 1 Byte | 2 Bytes | N Bytes | 1 Byte | |

  - **FreqAnt**: 此字节高 6 位是读取标签的频点参数，低 2 位是天线号。
  - **PC**: 标签的 PC，固定两个字节。
  - **EPC**: 标签的 EPC 号，长度可变化。
  - **RSSI**: 标签的实时 RSSI。

- **命令完成**: 

| Head | Len | Address | Cmd | AntID | ReadRate | TotalRead | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x0A` | | `0x89` | 1 Byte | 2 Bytes | 4 Bytes | |

  - **AntID**: 此次盘存使用的天线号。
  - **ReadRate**: 此轮命令标签识别速率。
  - **TotalRead**: 标签应答的总记录数。

- **操作失败**: `A0 04 89 ErrorCode [Check]`

##### *******. cmd_customized_session_target_inventory

- **模式**: 实时模式 (Session)
- **功能**: 推荐使用的盘存指令。读写器收到此命令后，按照指定的 session 和 inventoried flag 进行多标签识别操作。标签数据实时上传，不存入读写器缓存区。此命令一轮盘存耗时短，普通盘存推荐使用此命令 S1 模式。
- **上位机指令**: 
 
  - **带相位指令 (固件 V8.2+)**:

    | Head | Len | Address | Cmd | Session | Target | SL | Phase | Repeat | Check |
    | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
    | `0xA0` | `0x08` | | `0x8B` | 1 Byte | 1 Byte | 1 Byte | 1 Byte | 1 Byte | |

    - **Phase**: 相位值; `00` 为关闭此功能, `01` 为打开此功能。

- **注意**: 由于硬件为双 CPU 架构，主 CPU 负责轮询标签，副 CPU 负责数据管理。轮询标签和发送数据并行，互不占用对方的时间，因此串口的数据传输不影响读写器工作的效率。

- **标签应答**: (多条)
  - **无相位**:

Head	Len	Address	Cmd 	FreqAnt	PC 	EPC	RSSI	Check
0xA0			0x8B		2 bytes	N bytes		

参数说明	FreqAnt	此字节高6位是读取标签的频点参数，低2位是天线号。
	PC	标签的PC ,固定两个字节
	EPC	标签的EPC号，长度可变化。
	RSSI	标签的实时RSSI。


  - **带相位**:

Head	Len	Address	Cmd 	FreqAnt	PC 	EPC	RSSI	Phase	Check
0xA0			0x8B		2 bytes	N bytes		2 bytes	

参数说明	FreqAnt		此字节高6位是读取标签的频点参数，低2位是天线号。
	PC		标签的PC ,固定两个字节。
	EPC		标签的EPC号，长度可变化。
	RSSI		标签的实时RSSI。
	Phase		相位值。

- **命令完成**: 

| Head | Len | Address | Cmd | AntID | ReadRate | TotalRead | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | `0x0A` | | `0x8B` | 1 Byte | 2 Bytes | 4 Bytes | |

- **操作失败**: 
Head	Len	Address	Cmd	ErrorCode 	Check
0xA0	0x04		 0x8B		


##### *******. cmd_fast_switch_ant_inventory
- **模式**: 实时模式 + 缓存模式
- **功能**: 快速轮询多个天线盘存标签。标签数据实时上传，同时存入读写器缓存区。
- **上位机指令**: 

  - **扩展指令 (8天线, 固件 V8.2+)**:


（此命令仅适用于固件版本为V8.2以上的读写器）：
Head	Len	Address	Cmd 	A	Stay	B	Stay	C	Stay	…	H	Stay	Interval	Reserve0	Session	Target	Reserve1	Reserve2	Reserve3	Phase	Repeat	Check
0xA0	0x20		0x8A											5 bytes						 	 	

参数说明	A	首先轮询的天线 (00 – 07) ，天线号大于7则表示不轮询。
	Stay	天线重复轮询的次数。每个天线可单独配置。
	B	第二个轮询的天线 (00 – 07) ，天线号大于7则表示不轮询。
	C	第三个轮询的天线 (00 – 07) ，天线号大于7则表示不轮询。
	…	…
	H	第八个轮询的天线 (00 – 07) ，天线号大于7则表示不轮询。
	Interval	天线间的休息时间。单位是mS。休息时无射频输出，可降低功耗。
	Reserve0	预留字节，5 bytes，默认全部为： 0x00。
	Session	指定盘存的session，00为S0，01为S1，02为S2，03为S3。
	Target	指定盘存的Inventoried Flag，00为A，01为B。
	Reserve1	预留字节，默认为： 0x00。
	Reserve2	预留字节，默认为： 0x00。
	Reserve3	预留字节，默认为： 0x00。
	Phase	00为关闭相位值，01为打开相位值。
	Repeat	重复以上天线切换顺序次数。


    - **A-H**: 天线号 0-7。`0xFF` 表示不启用。
    - **Session**: `00`-`03` (S0-S3)。
    - **Target**: `00` (A), `01` (B)。
    - **Phase**: `00` (关闭), `01` (开启)。
    - **Reserve**: 保留字节。

- **注意**:
  - 硬件为双 CPU 架构，主 CPU 负责轮询标签，副 CPU 负责数据管理和发送。
  - 轮询标签和发送数据并行，串口数据传输不影响读写器效率。
  - 每个天线盘存耗时约 30ms (无标签时)。

- **标签应答**: (多条)
  - **无相位**:
Head	Len	Address	Cmd 	FreqAnt	PC 	EPC	RSSI	Check
0xA0			0x8A		2 bytes	N bytes		

参数说明	FreqAnt	此字节高6位是读取标签的频点参数，低2位是天线号。
	PC	标签的PC ,固定两个字节。
	EPC	标签的EPC号，长度可变化。
	RSSI	标签的实时RSSI。
此字节高位为0时取天线号1/2/3/4；高位为1时取天线号5/6/7/8。（注：高位仅判断天线取值，不计入RSSI值）


  - **带相位**:

Head	Len	Address	Cmd 	FreqAnt	PC 	EPC	RSSI	Phase	Check
0xA0			0x8A		2 bytes	N bytes		2 bytes	

参数说明	FreqAnt		此字节高6位是读取标签的频点参数，低2位是天线号。
	PC		标签的PC ,固定两个字节。
	EPC		标签的EPC号，长度可变化。
	RSSI		标签的实时RSSI。
此字节高位为0时取天线号1/2/3/4；高位为1时取天线号5/6/7/8。（注：高位仅判断天线取值，不计入RSSI值）
	Phase		相位值


- **天线未连接应答**:

Head	Len	Address	Cmd	AntID 	ErrorCode 	Check
0xA0	0x05		 0x8A		0x22	


- **命令完成**: 

Head	Len	Address	Cmd	TotalRead	CommandDuration	Check
0xA0	0x0A		 0x8A	3 bytes	4 bytes	


  - **TotalRead**: 标签读取总次数 (高位在前)。
  - **CommandDuration**: 命令执行总时间 (单位: ms, 高位在前)。

- **操作失败**: 
Head	Len	Address	Cmd	ErrorCode 	Check
0xA0	0x04		 0x8A		

#### 2.2.2. 读写指令

##### *******. cmd_read

- **功能**: 读取 18000-6C 标签指定区域的数据。
- **上位机指令**: 

| Head | Len | Cmd | MemBank | WordAdd | WordCnt | Password | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | `09` | `81` | 1 Byte | 2 Bytes | 1 Byte | 4 Bytes | 1 Byte |

  - **MemBank**: 标签的存储区域。
    - `0x00`: RFU (保留区)
    - `0x01`: EPC (EPC 区)
    - `0x02`: TID (TID 区)
    - `0x03`: USER (用户区)
  - **WordAdd**: 读取数据区的起始地址 (以字为单位)。
  - **WordCnt**: 读取数据的长度 (以字为单位)。
  - **Password**: 访问密码 (4 字节)。如果标签未设置访问密码，则此字段必须为 `00 00 00 00`。

- **操作成功**: 

| Head | Len | Cmd | WordCnt | Data | PC | EPC | CRC | RSSI | AntID | ReadCount | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `81` | 1 Byte | N*2 Bytes | 2 Bytes | M*2 Bytes | 2 Bytes | 1 Byte | 1 Byte | 1 Byte | 1 Byte |

  - **WordCnt**: 读回的数据长度 (字)。
  - **Data**: 读回的数据。
  - **PC & EPC**: 标签的 PC 和 EPC 号。
  - **CRC**: 标签返回的 CRC16 校验码。
  - **RSSI**: 信号强度。
  - **AntID**: 读取到此标签的天线号。
  - **ReadCount**: 读到此标签的次数。

- **操作失败**: `A0 04 81 ErrorCode [Check]`

##### 2.2.2.2. cmd_write

- **功能**: 向 18000-6C 标签指定区域写入数据。
- **上位机指令**: 

| Head | Len | Cmd | MemBank | WordAdd | WordCnt | Password | Data | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `82` | 1 Byte | 2 Bytes | 1 Byte | 4 Bytes | N*2 Bytes | 1 Byte |

  - **MemBank**: 标签的存储区域。
    - `0x00`: RFU (保留区)
    - `0x01`: EPC (EPC 区)
    - `0x02`: TID (TID 区)
    - `0x03`: USER (用户区)
  - **WordAdd**: 写入数据区的起始地址 (以字为单位)。
  - **WordCnt**: 写入数据的长度 (以字为单位)。
  - **Password**: 访问密码 (4 字节)。
  - **Data**: 要写入的数据。

- **操作成功**: 

| Head | Len | Cmd | PC | EPC | CRC | RSSI | AntID | WriteCount | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `82` | 2 Bytes | M*2 Bytes | 2 Bytes | 1 Byte | 1 Byte | 1 Byte | 1 Byte |

  - **PC & EPC**: 标签的 PC 和 EPC 号。
  - **CRC**: 标签返回的 CRC16 校验码。
  - **RSSI**: 信号强度。
  - **AntID**: 写入此标签的天线号。
  - **WriteCount**: 写入此标签的次数。

- **操作失败**: `A0 04 82 ErrorCode [Check]`

##### 2.2.2.3. cmd_lock

- **功能**: 锁定 18000-6C 标签的指定区域。
- **上位机指令**: 

| Head | Len | Cmd | LockPayload | Password | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | `08` | `83` | 3 Bytes | 4 Bytes | 1 Byte |

  - **LockPayload**: 锁定操作的数据，3个字节，共24位。
  - **Password**: 访问密码 (4 字节)。

- **LockPayload 详细说明**:
  - 24个比特位分为10个区域，每个区域的动作(Action)占2位。
  - **Action**: 
    - `00`: 解锁 (Unlocked)
    - `01`: 永久解锁 (Permanently Unlocked)
    - `10`: 锁定 (Locked)
    - `11`: 永久锁定 (Permanently Locked)
  - **比特位分配**:

| Bit(s) | 描述 |
| :--- | :--- |
| 22-23 | Kill Password 的写权限 |
| 20-21 | Access Password 的写权限 |
| 18-19 | EPC Memory 的写权限 |
| 16-17 | TID Memory 的写权限 |
| 14-15 | User Memory 的写权限 |
| 12-13 | Kill Password 的读/写保护状态 |
| 10-11 | Access Password 的读/写保护状态 |
| 8-9 | EPC Memory 的读/写保护状态 |
| 6-7 | TID Memory 的读/写保护状态 |
| 4-5 | User Memory 的读/写保护状态 |
| 0-3 | 保留 (RFU) |

- **操作成功**: 

| Head | Len | Cmd | PC | EPC | CRC | RSSI | AntID | LockCount | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `83` | 2 Bytes | M*2 Bytes | 2 Bytes | 1 Byte | 1 Byte | 1 Byte | 1 Byte |

  - **PC & EPC**: 标签的 PC 和 EPC 号。
  - **CRC**: 标签返回的 CRC16 校验码。
  - **RSSI**: 信号强度。
  - **AntID**: 锁定此标签的天线号。
  - **LockCount**: 锁定此标签的次数。

- **操作失败**: `A0 04 83 ErrorCode [Check]`

##### 2.2.2.4. cmd_kill

- **功能**: 永久灭活 18000-6C 标签。
- **上位机指令**: 

| Head | Len | Cmd | KillPassword | Check |
| :--- | :--- | :--- | :--- | :--- |
| `A0` | `07` | `84` | 4 Bytes | 1 Byte |

  - **KillPassword**: 灭活密码 (4 字节)。

- **操作成功**: 

| Head | Len | Cmd | PC | EPC | CRC | RSSI | AntID | KillCount | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `84` | 2 Bytes | M*2 Bytes | 2 Bytes | 1 Byte | 1 Byte | 1 Byte | 1 Byte |

  - **PC & EPC**: 标签的 PC 和 EPC 号。
  - **CRC**: 标签返回的 CRC16 校验码。
  - **RSSI**: 信号强度。
  - **AntID**: 灭活此标签的天线号。
  - **KillCount**: 灭活此标签的次数。

- **操作失败**: `A0 04 84 ErrorCode [Check]`

##### 2.2.2.5. cmd_set_access_epc_match

- **功能**: 设置 ACCESS 操作的 EPC 匹配。
- **上位机指令**: 

| Head | Len | Cmd | MatchEnable | MatchEPC | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `85` | 1 Byte | N Bytes | 1 Byte |

  - **MatchEnable**: 匹配使能。
    - `0x00`: 关闭 EPC 匹配。
    - `0x01`: 开启 EPC 匹配。
  - **MatchEPC**: 匹配的 EPC 号。当 MatchEnable 为 `0x01` 时，此字段有效。

- **操作成功**: `A0 04 85 CommandSuccess [Check]`
  - **注意**: 此命令设置的 EPC 匹配仅对 `cmd_read`, `cmd_write`, `cmd_lock`, `cmd_kill` 命令有效。此设置不会保存到 FLASH，读写器重启或断电后会失效。
- **操作失败**: `A0 04 85 ErrorCode [Check]`

##### 2.2.2.6. cmd_get_access_epc_match

- **功能**: 查询 ACCESS 操作的 EPC 匹配状态。
- **上位机指令**: `A0 03 86 [Check]`

- **操作成功**: 

| Head | Len | Cmd | MatchEnable | MatchEPC | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `86` | 1 Byte | N Bytes | 1 Byte |

  - **MatchEnable**: 匹配使能。
    - `0x00`: 关闭 EPC 匹配。
    - `0x01`: 开启 EPC 匹配。
  - **MatchEPC**: 匹配的 EPC 号。当 MatchEnable 为 `0x01` 时，此字段有效。

- **操作失败**: `A0 04 86 ErrorCode [Check]`

##### 2.2.2.7. cmd_set_impinj_fast_tid

- **功能**: 设置 Monza 标签快速读 TID (设置不被保存至内部 FLASH)。
- **上位机指令**: 

| Head | Len | Cmd | Mode | Check |
| :--- | :--- | :--- | :--- | :--- |
| `A0` | `04` | `8C` | 1 Byte | 1 Byte |

  - **Mode**:
    - `0x00`: 关闭此功能。
    - `0x01`: 开启此功能。

- **操作成功**: `A0 04 8C CommandSuccess [Check]`
  - **注意**: 此设置不会保存到 FLASH，读写器重启或断电后会失效。
- **操作失败**: `A0 04 8C ErrorCode [Check]`

##### 2.2.2.8. cmd_set_and_save_impinj_fast_tid

- **功能**: 设置 Monza 标签快速读 TID (设置被保存至内部 FLASH)。
- **上位机指令**: 

| Head | Len | Cmd | Mode | Check |
| :--- | :--- | :--- | :--- | :--- |
| `A0` | `04` | `8D` | 1 Byte | 1 Byte |

  - **Mode**:
    - `0x00`: 关闭此功能。
    - `0x01`: 开启此功能。

- **操作成功**: `A0 04 8D CommandSuccess [Check]`
  - **注意**: 此设置会保存到 FLASH，读写器重启或断电后不会失效。
- **操作失败**: `A0 04 8D ErrorCode [Check]`

##### 2.2.2.9. cmd_get_impinj_fast_tid

- **功能**: 查询当前的快速 TID 设置。
- **上位机指令**: `A0 03 8E [Check]`

- **操作成功**: 

| Head | Len | Cmd | Mode | Check |
| :--- | :--- | :--- | :--- | :--- |
| `A0` | `04` | `8E` | 1 Byte | 1 Byte |

  - **Mode**:
    - `0x00`: 关闭此功能。
    - `0x01`: 开启此功能。

- **操作失败**: `A0 04 8E ErrorCode [Check]`

### 2.3. ISO 18000-6B 标签操作命令

#### 2.3.1. cmd_iso18000_6b_inventory

- **功能**: 盘存 18000-6B 标签。
- **上位机指令**: `A0 03 B0 [Check]`

- **标签应答**: 

| Head | Len | Cmd | UID | DSFID | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | `0C` | `B0` | 8 Bytes | 1 Byte | 1 Byte |

  - **UID**: 标签的 UID。
  - **DSFID**: 标签的 DSFID。

- **命令完成**: `A0 04 B0 CommandSuccess [Check]`

- **操作失败**: `A0 04 B0 ErrorCode [Check]`

#### 2.3.2. cmd_iso18000_6b_read

- **功能**: 读取 18000-6B 标签指定区域的数据。
- **上位机指令**: 

| Head | Len | Cmd | UID | StartAddress | WordCnt | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | `06` | `B1` | 8 Bytes | 1 Byte | 1 Byte | 1 Byte |

  - **UID**: 标签的 UID。
  - **StartAddress**: 起始地址 (块地址)。
  - **WordCnt**: 读取数据的长度 (块数)。

- **操作成功**: 

| Head | Len | Cmd | Data | Check |
| :--- | :--- | :--- | :--- | :--- |
| `A0` | `05+N` | `B1` | N Bytes | 1 Byte |

  - **Data**: 读取到的数据。

- **操作失败**: `A0 04 B1 ErrorCode [Check]`

#### 2.3.3. cmd_iso18000_6b_write

- **功能**: 向 18000-6B 标签指定区域写入数据。
- **上位机指令**: 

| Head | Len | Cmd | UID | StartAddress | Data | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | `06+N` | `B2` | 8 Bytes | 1 Byte | N Bytes | 1 Byte |

  - **UID**: 标签的 UID。
  - **StartAddress**: 起始地址 (块地址)。
  - **Data**: 写入的数据。

- **操作成功**: `A0 04 B2 CommandSuccess [Check]`

- **操作失败**: `A0 04 B2 ErrorCode [Check]`

#### 2.3.4. cmd_iso18000_6b_lock

- **功能**: 锁定 18000-6B 标签的指定块。
- **上位机指令**: 

| Head | Len | Cmd | UID | BlockAddress | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | `05` | `B3` | 8 Bytes | 1 Byte | 1 Byte |

  - **UID**: 标签的 UID。
  - **BlockAddress**: 锁定块的地址。

- **操作成功**: `A0 04 B3 CommandSuccess [Check]`

- **操作失败**: `A0 04 B3 ErrorCode [Check]`

#### 2.3.5. cmd_iso18000_6b_query_lock

- **功能**: 查询 18000-6B 标签的指定块是否锁定。
- **上位机指令**: 

| Head | Len | Cmd | UID | BlockAddress | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | `05` | `B4` | 8 Bytes | 1 Byte | 1 Byte |

  - **UID**: 标签的 UID。
  - **BlockAddress**: 查询块的地址。

- **操作成功**: 

| Head | Len | Cmd | LockStatus | Check |
| :--- | :--- | :--- | :--- | :--- |
| `A0` | `05` | `B4` | 1 Byte | 1 Byte |

  - **LockStatus**: 锁定状态。
    - `0x00`: 未锁定
    - `0x01`: 已锁定

- **操作失败**: `A0 04 B4 ErrorCode [Check]`

### 2.4. 缓存操作命令

#### 2.4.1. cmd_get_inventory_buffer

- **功能**: 提取标签数据保留缓存备份。
- **上位机指令**: `A0 03 90 [Check]`

- **操作成功**: 

| Head | Len | Cmd | PC | EPC | CRC | RSSI | AntID | ReadCount | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `90` | 2 Bytes | M*2 Bytes | 2 Bytes | 1 Byte | 1 Byte | 1 Byte | 1 Byte |

  - **PC & EPC**: 标签的 PC 和 EPC 号。
  - **CRC**: 标签返回的 CRC16 校验码。
  - **RSSI**: 信号强度。
  - **AntID**: 读取到此标签的天线号。
  - **ReadCount**: 读到此标签的次数。

- **操作失败**: `A0 04 90 ErrorCode [Check]`

#### 2.4.2. cmd_get_and_reset_inventory_buffer

- **功能**: 提取标签数据并删除缓存。
- **上位机指令**: `A0 03 91 [Check]`

- **操作成功**: 

| Head | Len | Cmd | PC | EPC | CRC | RSSI | AntID | ReadCount | Check |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `A0` | | `91` | 2 Bytes | M*2 Bytes | 2 Bytes | 1 Byte | 1 Byte | 1 Byte | 1 Byte |

  - **PC & EPC**: 标签的 PC 和 EPC 号。
  - **CRC**: 标签返回的 CRC16 校验码。
  - **RSSI**: 信号强度。
  - **AntID**: 读取到此标签的天线号。
  - **ReadCount**: 读到此标签的次数。

- **操作失败**: `A0 04 91 ErrorCode [Check]`

#### 2.4.3. cmd_get_inventory_buffer_tag_count

- **功能**: 查询缓存中已读标签个数。
- **上位机指令**: `A0 03 92 [Check]`

- **操作成功**: 

| Head | Len | Cmd | TagCount | Check |
| :--- | :--- | :--- | :--- | :--- |
| `A0` | `05` | `92` | 2 Bytes | 1 Byte |

  - **TagCount**: 缓存中已读标签的个数 (高位在前)。

- **操作失败**: `A0 04 92 ErrorCode [Check]`

#### 2.4.4. cmd_reset_inventory_buffer

- **功能**: 清空标签数据缓存。
- **上位机指令**: `A0 03 93 [Check]`

- **操作成功**: `A0 04 93 CommandSuccess [Check]`

- **操作失败**: `A0 04 93 ErrorCode [Check]`

---

## 2. 指令集定义

### 指令集一览表

| 序号 | 命令码 | 名称 | 描述 |
| :--- | :--- | :--- | :--- |
| **读写器操作命令** | |
| 1 | `0x70` | cmd_reset | 复位读写器 |
| 2 | `0x71` | cmd_set_uart_baudrate | 设置串口通讯波特率 |
| 3 | `0x72` | cmd_get_firmware_version | 读取读写器固件版本 |
| 4 | `0x73` | cmd_set_reader_address | 设置读写器地址 |
| 5 | `0x74` | cmd_set_work_antenna | 设置读写器工作天线 |
| 6 | `0x75` | cmd_get_work_antenna | 查询当前天线工作天线 |
| 7 | `0x76` | cmd_set_output_power | 设置读写器射频输出功率 |
| 8 | `0x77` | cmd_get_output_power | 查询读写器当前输出功率 |
| 9 | `0x78` | cmd_set_frequency_region | 设置读写器工作频率范围 |
| 10 | `0x79` | cmd_get_frequency_region | 查询读写器工作频率范围 |
| 11 | `0x7A` | cmd_set_beeper_mode | 设置蜂鸣器状态 |
| 12 | `0x7B` | cmd_get_reader_temperature | 查询当前设备的工作温度 |
| 13 | `0x60` | cmd_read_gpio_value | 读取 GPIO 电平 |
| 14 | `0x61` | cmd_write_gpio_value | 设置 GPIO 电平 |
| 15 | `0x62` | cmd_set_ant_connection_detector | 设置天线连接检测器状态 |
| 16 | `0x63` | cmd_get_ant_connection_detector | 读取天线连接检测器状态 |
| 17 | `0x66` | cmd_set_temporary_output_power | 设置读写器临时射频输出功率 |
| 18 | `0x67` | cmd_set_reader_identifier | 设置读写器识别码 |
| 19 | `0x68` | cmd_get_reader_identifier | 读取读写器识别码 |
| 20 | `0x69` | cmd_set_rf_link_profile | 设置射频链路的通讯速率 |
| 21 | `0x6A` | cmd_get_rf_link_profile | 读取射频链路的通讯速率 |
| 22 | `0x7E` | cmd_get_rf_port_return_loss | 测量天线端口的回波损耗 |
| **18000-6C 命令** | |
| 23 | `0x80` | cmd_inventory | 盘存标签 |
| 24 | `0x81` | cmd_read | 读标签 |
| 25 | `0x82` | cmd_write | 写标签 |
| 26 | `0x83` | cmd_lock | 锁定标签 |
| 27 | `0x84` | cmd_kill | 灭活标签 |
| 28 | `0x85` | cmd_set_access_epc_match | 匹配 ACCESS 操作的 EPC 号 |
| 29 | `0x86` | cmd_get_access_epc_match | 查询匹配的 EPC 状态 |
| 30 | `0x89` | cmd_real_time_inventory | 盘存标签(实时上传标签数据) |
| 31 | `0x8A` | cmd_fast_switch_ant_inventory | 快速轮询多个天线盘存标签 |
| 32 | `0x8B` | cmd_customized_session_target_inventory | 自定义 session 和 target 盘存 |
| 33 | `0x8C` | cmd_set_impinj_fast_tid | 设置 Monza 标签快速读 TID (设置不被保存至内部 FLASH) |
| 34 | `0x8D` | cmd_set_and_save_impinj_fast_tid | 设置 Monza 标签快速读 TID (设置被保存至内部 FLASH) |
| 35 | `0x8E` | cmd_get_impinj_fast_tid | 查询当前的快速 TID 设置 |
| **ISO18000-6B 命令** | |
| 36 | `0xB0` | cmd_iso18000_6b_inventory | 盘存 18000-6B 标签 |
| 37 | `0xB1` | cmd_iso18000_6b_read | 读 18000-6B 标签 |
| 38 | `0xB2` | cmd_iso18000_6b_write | 写 18000-6B 标签 |
| 39 | `0xB3` | cmd_iso18000_6b_lock | 锁定 18000-6B 标签 |
| 40 | `0xB4` | cmd_iso18000_6b_query_lock | 查询 18000-6B 标签 |
| **缓存操作命令** | |
| 41 | `0x90` | cmd_get_inventory_buffer | 提取标签数据保留缓存备份 |
| 42 | `0x91` | cmd_get_and_reset_inventory_buffer | 提取标签数据并删除缓存 |
| 43 | `0x92` | cmd_get_inventory_buffer_tag_count | 查询缓存中已读标签个数 |
| 44 | `0x93` | cmd_reset_inventory_buffer | 清空标签数据缓存 |

---

## 1. 通信协议结构

此通信协议是上位机通过串行通信接口操作读写器的通信规范。命令和响应数据由连续的字节流组成数据包，长度可变，并采用校验和方法进行检错。

### 1.1. RS232 参数设置

- **物理接口**: 符合 RS-232 规范要求。
- **帧格式**: 1位起始位、8位数据位、1位停止位、无奇偶校验。
- **通信波特率**: 38400bps、115200bps 可选。默认波特率为 115200bps。

### 1.2. 数据包格式定义

#### 1.2.1. 上位机指令数据包格式定义

| Head | Len | Address | Cmd | Data | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | 1 Byte | 1 Byte | 1 Byte | N Bytes | 1 Byte |

**参数说明:**

- **Head**: 数据包头，每包数据均以 `0xA0` 开始。
- **Len**: 数据包从 Len 后面开始的字节数，不包含 Len 本身。
- **Address**: 读写器地址。供 RS-485 接口串联时使用。一般地址从 0~254 (0xFE)，255 (0xFF) 为公用地址。读写器接收自身地址和公用地址的命令。
- **Cmd**: 命令码。
- **Data**: 命令参数。
- **Check**: 校验和，除校验和本身外所有字节的校验和。

#### 1.2.2. 读写器返回数据包定义

| Head | Len | Address | Cmd | Data | Check |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `0xA0` | 1 Byte | 1 Byte | 1 Byte | N Bytes | 1 Byte |

**参数说明:**

- **Head**: 数据包头，每包数据均以 `0xA0` 开始。
- **Len**: 数据包从 Len 后面开始的字节数，不包含 Len 本身。
- **Address**: 读写器自身的地址。
- **Cmd**: 命令码。
- **Data**: 读写器返回的数据。
- **Check**: 校验和，除校验和本身外所有字节的校验和。




错误代码表
序号	值	名 称	描    叙
1	0x10	command_success	命令成功完成
2	0x11	command_fail	命令执行失败
3	0x20	mcu_reset_error	CPU复位错误
4	0x21	cw_on_error	打开CW错误
5	0x22	antenna_missing_error	天线未连接
6	0x23	write_flash_error	写Flash错误
7	0x24	read_flash_error	读Flash错误
8	0x25	set_output_power_error	设置发射功率错误
9	0x31	tag_inventory_error	盘存标签错误
10	0x32	tag_read_error	读标签错误
11	0x33	tag_write_error	写标签错误
12	0x34	tag_lock_error	锁定标签错误
13	0x35	tag_kill_error	灭活标签错误
14	0x36	no_tag_error	无可操作标签错误
15	0x37	inventory_ok_but_access_fail	成功盘存但访问失败
16	0x38	buffer_is_empty_error	缓存为空
17	0x3C	nxp_custom_command_fail	NXP芯片自定义指令失败
18	0x40	access_or_password_error	访问标签错误或访问密码错误
19	0x41	parameter_invalid	无效的参数
20	0x42	parameter_invalid_wordCnt_too_long	wordCnt参数超过规定长度
21	0x43	parameter_invalid_membank_out_of_range	MemBank参数超出范围
22	0x44	parameter_invalid_lock_region_out_of_range	Lock数据区参数超出范围
23	0x45	parameter_invalid_lock_action_out_of_range	LockType参数超出范围
24	0x46	parameter_reader_address_invalid	读写器地址无效
25	0x47	parameter_invalid_antenna_id_out_of_range	Antenna_id 超出范围
26	0x48	parameter_invalid_output_power_out_of_range	输出功率参数超出范围
27	0x49	parameter_invalid_frequency_region_out_of_range	射频规范区域参数超出范围
28	0x4A	parameter_invalid_baudrate_out_of_range	波特率参数超出范围
29	0x4B	parameter_beeper_mode_out_of_range	蜂鸣器设置参数超出范围
30	0x4C	parameter_epc_match_len_too_long	EPC匹配长度越界
31	0x4D	parameter_epc_match_len_error	EPC匹配长度错误
32	0x4E	parameter_invalid_epc_match_mode	EPC匹配参数超出范围
33	0x4F	parameter_invalid_frequency_range	频率范围设置参数错误
34	0x50	fail_to_get_RN16_from_tag	无法接收标签的RN16
35	0x51	parameter_invalid_drm_mode	DRM设置参数错误
36	0x52	pll_lock_fail	PLL不能锁定
37	0x53	rf_chip_fail_to_response 	射频芯片无响应
38	0x54	fail_to_achieve_desired_output_power	输出达不到指定的输出功率
39	0x55	copyright_authentication_fail	版权认证未通过
40	0x56	spectrum_regulation_error	频谱规范设置错误
41	0x57	output_power_too_low	输出功率过低
42	0xEE	fail_to_get_rf_port_return_loss	测量回波损耗失败
