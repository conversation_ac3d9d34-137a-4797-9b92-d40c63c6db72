using System.ComponentModel;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using Microsoft.Win32;

namespace RFID_UI.ViewModels
{
    public class MainWindowViewModel : INotifyPropertyChanged
    {
        private string _logContent = "[11:06:02.353] 🔌 COM端口列表已刷新\n";
        private AdvancedCommandsViewModel? _advancedCommandsViewModel;

        // 用于自动滚动的事件
        public event Action? ScrollToEnd;

        public string LogContent
        {
            get => _logContent;
            set
            {
                _logContent = value;
                OnPropertyChanged();
            }
        }

        public ICommand ClearLogCommand { get; }
        public ICommand SaveLogCommand { get; }

        public AdvancedCommandsViewModel? AdvancedCommandsViewModel
        {
            get => _advancedCommandsViewModel;
        }

        public bool ShowSerialLogs
        {
            get => _advancedCommandsViewModel?.ShowSerialLogs ?? false;
            set
            {
                if (_advancedCommandsViewModel != null)
                {
                    _advancedCommandsViewModel.ShowSerialLogs = value;
                    OnPropertyChanged();
                }
            }
        }



        public MainWindowViewModel()
        {
            ClearLogCommand = new RelayCommand(ClearLog);
            SaveLogCommand = new RelayCommand(SaveLog);
        }

        public void SetAdvancedCommandsViewModel(AdvancedCommandsViewModel viewModel)
        {
            _advancedCommandsViewModel = viewModel;

            OnPropertyChanged(nameof(AdvancedCommandsViewModel));
            OnPropertyChanged(nameof(ShowSerialLogs));
        }

        private void ClearLog()
        {
            LogContent = "";
        }

        private void SaveLog()
        {
            try
            {
                if (string.IsNullOrEmpty(LogContent))
                {
                    System.Windows.MessageBox.Show("日志内容为空，无需保存。", "提示",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    return;
                }

                // 创建保存文件对话框
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存日志文件",
                    Filter = "文本文件 (*.txt)|*.txt|日志文件 (*.log)|*.log|所有文件 (*.*)|*.*",
                    DefaultExt = "txt",
                    FileName = $"RFID_Log_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // 添加文件头信息
                    var logHeader = $"RFID UI 操作日志\n";
                    logHeader += $"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
                    logHeader += $"应用版本: UHF RFID Reader Demo v4.3\n";
                    logHeader += new string('=', 50) + "\n\n";

                    var fullLogContent = logHeader + LogContent;

                    // 保存文件
                    File.WriteAllText(saveFileDialog.FileName, fullLogContent, System.Text.Encoding.UTF8);

                    // 显示成功消息
                    System.Windows.MessageBox.Show($"日志已成功保存到:\n{saveFileDialog.FileName}", "保存成功",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    // 添加保存日志到日志内容中
                    AddLog($"💾 日志已保存到: {Path.GetFileName(saveFileDialog.FileName)}");
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"保存日志失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                AddLog($"❌ 保存日志失败: {ex.Message}");
            }
        }

        public void AddLog(string message)
        {
            var timestamp = System.DateTime.Now.ToString("HH:mm:ss.fff");
            LogContent += $"[{timestamp}] {message}\n";

            // 触发自动滚动
            ScrollToEnd?.Invoke();
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // 简单的RelayCommand实现
    public class RelayCommand : ICommand
    {
        private readonly System.Action _execute;
        private readonly System.Func<bool>? _canExecute;

        public RelayCommand(System.Action execute, System.Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new System.ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event System.EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute();
        }
    }
}
