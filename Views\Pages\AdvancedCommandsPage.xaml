<Page x:Class="RFID_UI.Views.Pages.AdvancedCommandsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      xmlns:local="clr-namespace:RFID_UI.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="1000"
      Title="高级命令">

    <Page.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    </Page.Resources>

    <Grid Margin="15">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="35*"/>
            <ColumnDefinition Width="65*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧控制面板 -->
        <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
            <StackPanel MinWidth="280" MaxWidth="450">

                <!-- 盘点方式选择 -->
                <ui:Card Margin="2,2,2,2">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 标题 -->
                        <ui:TextBlock Grid.Row="0" Text="盘点方式" FontSize="14" Margin="2,2,2,2"/>

                        <!-- 盘点方式和按钮 -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 左侧：盘点方式选择 -->
                            <StackPanel Grid.Column="0">
                                <RadioButton Content="0x8A - 快速天线切换"
                                            IsChecked="{Binding IsFastAntennaMode}"
                                            FontSize="14"
                                            Margin="2,2,2,2"/>
                                <RadioButton Content="0x8B - 自定义会话目标"
                                            IsChecked="{Binding IsCustomSessionMode}"
                                            FontSize="14"/>
                            </StackPanel>

                            <!-- 右侧：盘点控制按钮 -->
                            <StackPanel Grid.Column="1" Margin="8,0,0,0" VerticalAlignment="Top">
                                <ui:Button Content="{Binding InventoryButtonText}"
                                          Icon="{ui:SymbolIcon Symbol=Play24}"
                                          Command="{Binding InventoryCommand}"
                                          Appearance="Primary"
                                          FontSize="14"
                                          MinWidth="120"
                                          HorizontalAlignment="Center"
                                          Margin="0,2,0,0"/>

                                <!-- 标签过滤按钮 -->
                                <ui:Button Content="标签过滤"
                                          Icon="{ui:SymbolIcon Symbol=Filter24}"
                                          Command="{Binding TagFilterCommand}"
                                          FontSize="14"
                                          MinWidth="120"
                                          HorizontalAlignment="Center"
                                          Margin="0,8,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </ui:Card>



                <!-- 参数设置区域 -->
                <ui:Card>
                    <StackPanel Margin="15">
                        <!-- 标题和重置按钮 -->
                        <Grid Margin="2,2,2,2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <ui:TextBlock Grid.Column="0" Text="参数设置" FontSize="14" VerticalAlignment="Center"/>
                            <ui:Button Grid.Column="1"
                                      Content="重置参数"
                                      Icon="{ui:SymbolIcon Symbol=ArrowReset24}"
                                      Command="{Binding ResetParametersCommand}"
                                      FontSize="14"
                                      MinWidth="80"/>
                        </Grid>

                        <!-- 动态参数控件 -->
                        <ContentControl x:Name="ParameterContentControl"
                                       Content="{Binding CurrentParameterControl}"/>
                    </StackPanel>
                </ui:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 右侧区域：标签数据展示 / 标签过滤设置 -->
        <Grid Grid.Column="1" Margin="15,0,0,0">
            <!-- 标签数据展示界面 -->
            <ui:Card VerticalAlignment="Top">
                <ui:Card.Style>
                    <Style TargetType="ui:Card">
                        <Setter Property="Visibility" Value="Visible"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsTagFilterVisible}" Value="True">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ui:Card.Style>

                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 第一行：标题和操作按钮 -->
                    <Grid Grid.Row="0" Margin="2,2,2,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 左侧：标题 -->
                        <ui:TextBlock Grid.Column="0" Text="📊 盘点统计" FontSize="14" VerticalAlignment="Center"/>

                        <!-- 右侧：操作按钮 -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <ui:Button Content="保存到目标标签列表"
                                      Icon="{ui:SymbolIcon Symbol=BookmarkAdd24}"
                                      Command="{Binding SaveToTargetListCommand}"
                                      FontSize="14"
                                      Margin="2,2,2,2"/>
                            <ui:Button Content="清除"
                                      Icon="{ui:SymbolIcon Symbol=Delete24}"
                                      Command="{Binding ClearTagDataCommand}"
                                      FontSize="14"
                                      Margin="2,2,2,2"/>
                            <ui:Button Content="导出"
                                      Icon="{ui:SymbolIcon Symbol=Save24}"
                                      Command="{Binding ExportTagDataCommand}"
                                      FontSize="14"/>
                        </StackPanel>
                    </Grid>

                    <!-- 第二行：核心统计信息 -->
                    <Grid Grid.Row="1" Margin="2,0,2,2" Background="#FFF8F8F8">
                        <StackPanel Orientation="Horizontal" Margin="8,4">
                            <ui:TextBlock FontSize="12" Margin="0,0,20,0">
                                <Run Text="⏱️ 耗时: "/>
                                <Run Text="{Binding InventoryDuration}"/>
                            </ui:TextBlock>
                            <ui:TextBlock FontSize="12" Margin="0,0,15,0">
                                <Run Text="📊 读取次数: "/>
                                <Run Text="{Binding TotalReadCount}"/>
                            </ui:TextBlock>
                            <ui:TextBlock FontSize="12" Margin="0,0,20,0">
                                <Run Text="📈 速率: "/>
                                <Run Text="{Binding ReadRate}"/>
                            </ui:TextBlock>
                            <ui:TextBlock FontSize="12">
                                <Run Text="🏷️ 发现标签: "/>
                                <Run Text="{Binding DiscoveredTagCount}"/>
                                <Run Text="个"/>
                            </ui:TextBlock>
                        </StackPanel>
                    </Grid>

                    <!-- 标签数据表格 -->
                    <DataGrid Grid.Row="2"
                             ItemsSource="{Binding TagDataCollection}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             FontSize="12"
                             FontFamily="Consolas"
                             VirtualizingPanel.IsVirtualizing="True"
                             VirtualizingPanel.VirtualizationMode="Recycling"
                             VirtualizingPanel.IsContainerVirtualizable="True"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="50"/>
                            <DataGridTextColumn Header="EPC" Binding="{Binding Epc}" Width="auto"/>
                            <DataGridTextColumn Header="PC" Binding="{Binding PC}" Width="80"/>
                            <DataGridTextColumn Header="RSSI" Binding="{Binding Rssi}" Width="80"/>
                            <DataGridTextColumn Header="天线" Binding="{Binding Antenna}" Width="60"/>
                            <DataGridTextColumn Header="频点" Binding="{Binding FreqPoint}" Width="60"/>
                            <DataGridTextColumn Header="相位" Binding="{Binding Phase}" Width="80"/>
                            <DataGridTextColumn Header="次数" Binding="{Binding Count}" Width="60"/>
                            <DataGridTextColumn Header="时间" Binding="{Binding LastSeen}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </ui:Card>

            <!-- 标签过滤设置界面 -->
            <ui:Card VerticalAlignment="Top">
                <ui:Card.Style>
                    <Style TargetType="ui:Card">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsTagFilterVisible}" Value="True">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </ui:Card.Style>

                <Grid Margin="15">
                    <StackPanel>
                        <!-- 标题和返回按钮 -->
                        <Grid Margin="2,2,2,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <ui:TextBlock Grid.Column="0" Text="🔍 标签过滤设置" FontSize="14" VerticalAlignment="Center"/>
                            <ui:Button Grid.Column="1" Content="返回数据"
                                      Icon="{ui:SymbolIcon Symbol=ArrowLeft24}"
                                      Command="{Binding ReturnToDataViewCommand}"
                                      FontSize="14"/>
                        </Grid>

                        <!-- 基础设置区域 -->
                        <GroupBox Header="过滤参数" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- 左侧：参数设置 -->
                                <Grid Grid.Column="0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="80"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="20"/>
                                        <ColumnDefinition Width="80"/>
                                        <ColumnDefinition Width="80"/>
                                        <ColumnDefinition Width="10"/>
                                        <ColumnDefinition Width="70"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 第一行：过滤ID、Session ID、过滤行为 -->
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="过滤ID:" VerticalAlignment="Center"/>
                                    <ComboBox Grid.Row="0" Grid.Column="1" ItemsSource="{Binding MaskIdOptions}"
                                             SelectedItem="{Binding SelectedMaskId}" FontSize="14"/>

                                    <TextBlock Grid.Row="0" Grid.Column="3" Text="Session:" VerticalAlignment="Center"/>
                                    <ComboBox Grid.Row="0" Grid.Column="4" ItemsSource="{Binding SessionIdOptions}"
                                             SelectedItem="{Binding SelectedSessionId}" FontSize="14"/>

                                    <TextBlock Grid.Row="0" Grid.Column="6" Text="过滤行为:" VerticalAlignment="Center"/>
                                    <ComboBox Grid.Row="0" Grid.Column="7" ItemsSource="{Binding ActionOptions}"
                                             SelectedItem="{Binding SelectedAction}" FontSize="14"/>

                                    <!-- 第二行：过滤区域、起始地址、长度显示 -->
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="过滤区域:" VerticalAlignment="Center" Margin="0,8,0,0"/>
                                    <ComboBox Grid.Row="1" Grid.Column="1" ItemsSource="{Binding MemBankOptions}"
                                             SelectedItem="{Binding SelectedMemBank}" FontSize="14" Margin="0,8,0,0"/>

                                    <TextBlock Grid.Row="1" Grid.Column="3" Text="起始地址:" VerticalAlignment="Center" Margin="0,8,0,0"/>
                                    <TextBox Grid.Row="1" Grid.Column="4" Text="{Binding StartAddress}" FontSize="14" Margin="0,8,0,0"/>

                                    <TextBlock Grid.Row="1" Grid.Column="6" Text="长度:" VerticalAlignment="Center" Margin="0,8,0,0"/>
                                    <StackPanel Grid.Row="1" Grid.Column="7" Orientation="Horizontal" Margin="0,8,0,0">
                                        <TextBlock Text="{Binding CalculatedMaskLength}" VerticalAlignment="Center" FontWeight="Bold"/>
                                        <TextBlock Text=" bit" Margin="2,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>

                                    <!-- 第三行：过滤值和清除选项 -->
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="过滤值:" VerticalAlignment="Center" Margin="0,8,0,0"/>
                                    <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3" Text="{Binding MaskValue, UpdateSourceTrigger=PropertyChanged}"
                                            FontSize="14" Margin="0,8,0,0"/>

                                    <TextBlock Grid.Row="2" Grid.Column="4" Text="清除选项:" VerticalAlignment="Center" Margin="8,8,8,0"/>
                                    <ComboBox Grid.Row="2" Grid.Column="5" Grid.ColumnSpan="3" ItemsSource="{Binding ClearMaskIdOptions}"
                                             SelectedItem="{Binding SelectedClearMaskId}" FontSize="14" Margin="0,8,0,0"/>
                                </Grid>

                                <!-- 右侧：操作按钮 -->
                                <StackPanel Grid.Column="1" Margin="20,0,0,0" VerticalAlignment="Center">
                                    <ui:Button Content="设置过滤" Command="{Binding SetFilterCommand}"
                                              Icon="{ui:SymbolIcon Symbol=Add24}"
                                              Appearance="Primary" FontSize="14" MinWidth="100" Margin="0,0,0,8"/>
                                    <ui:Button Content="清除过滤" Command="{Binding ClearFilterCommand}"
                                              Icon="{ui:SymbolIcon Symbol=Delete24}"
                                              FontSize="14" MinWidth="100" Margin="0,0,0,8"/>
                                    <ui:Button Content="查询过滤" Command="{Binding QueryFilterCommand}"
                                              Icon="{ui:SymbolIcon Symbol=Search24}"
                                              FontSize="14" MinWidth="100"/>
                                </StackPanel>
                            </Grid>
                        </GroupBox>

                        <!-- 掩码列表 -->
                        <GroupBox Header="当前掩码">
                            <StackPanel Margin="10">
                                <!-- 列标题 -->
                                <StackPanel Orientation="Horizontal" Background="#FFF0F0F0" Margin="0,0,0,5">
                                    <TextBlock Text="掩码ID" Width="80" FontSize="14" FontWeight="Bold" Padding="5,2"/>
                                    <TextBlock Text="Session" Width="60" FontSize="14" FontWeight="Bold" Padding="5,2"/>
                                    <TextBlock Text="行为" Width="60" FontSize="14" FontWeight="Bold" Padding="5,2"/>
                                    <TextBlock Text="存储区" Width="80" FontSize="14" FontWeight="Bold" Padding="5,2"/>
                                    <TextBlock Text="起始地址" Width="80" FontSize="14" FontWeight="Bold" Padding="5,2"/>
                                    <TextBlock Text="长度(bit)" Width="80" FontSize="14" FontWeight="Bold" Padding="5,2"/>
                                    <TextBlock Text="掩码值" Width="200" FontSize="14" FontWeight="Bold" Padding="5,2"/>
                                </StackPanel>

                                <!-- 数据列表 -->
                                <ListBox ItemsSource="{Binding MaskList}" Height="260" BorderThickness="1" BorderBrush="#FFCCCCCC">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding MaskId}" Width="80" FontSize="14" Padding="5,2"/>
                                                <TextBlock Text="{Binding SessionId}" Width="60" FontSize="14" Padding="5,2"/>
                                                <TextBlock Text="{Binding Action}" Width="60" FontSize="14" Padding="5,2"/>
                                                <TextBlock Text="{Binding MemBank}" Width="80" FontSize="14" Padding="5,2"/>
                                                <TextBlock Text="{Binding StartAddress}" Width="80" FontSize="14" Padding="5,2"/>
                                                <TextBlock Text="{Binding MaskLength}" Width="80" FontSize="14" Padding="5,2"/>
                                                <TextBlock Text="{Binding MaskValue}" Width="200" FontSize="14" Padding="5,2"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </Grid>
            </ui:Card>
        </Grid>
    </Grid>
</Page>
