# 智能盘点策略功能需求文档

## 📋 项目概述

基于当前RFID UI项目，实现一个新的智能盘点策略功能。该功能将使用标签过滤机制（0x98命令）实现目标导向的盘点测试，通过动态设置标签过滤条件，确保找到所有目标标签。

## 🎯 核心功能说明

- **使用标签过滤功能（0x98命令）**而非EPC匹配功能（0x85命令）
- **创建新的Tab页面**实现完整的盘点策略测试功能
- **实现智能过滤策略**：动态设置标签过滤条件，确保找到所有目标标签
- **双层循环控制**：支持多次盘点循环和批量过滤处理

## 🔧 具体实现要求

### 1. 高级命令页面增强

在现有的`AdvancedCommandsPage`标签数据表格中：
- **位置**：在清除按钮左侧添加"保存到目标标签列表"按钮
- **功能**：将当前显示的所有标签数据（EPC）添加到目标标签列表
- **交互**：提供用户确认对话框，显示将要保存的标签数量

### 2. 新建"盘点测试"Tab页面

创建新的页面`InventoryTestPage`，采用左右分栏布局：

#### 左侧：盘点策略设置界面（40%宽度）

**顶部控制区域：**
- 开始盘点按钮（支持开始/停止状态切换）
- 循环次数输入框（默认值：1，范围：1-10000）
- 间隔时间输入框（单位：秒，默认值：1，范围：0.1-60）

**盘点命令1参数设置：**
- 支持配置最多4套不同的盘点命令配置
- 每套命令包含：启用复选框 + 16进制命令输入框
- 支持拖拽排序功能，执行时按顺序使用已启用的命令
- 连续盘点时自动轮换使用不同的已启用命令（跳过未启用命令）
- 用户至少需要启用2个命令

**标签过滤参数设置：**
- Session选择下拉框（S0/S1/S2/S3，默认S0）
- 过滤行为设置（Action参数：00/01/10/11）
- 过滤区域设置（MemBank：EPC/TID/USER/保留区）
- 其他参数自动设置：
  - MaskID：按1、2、3、4、5顺序分配，每次清除过滤后重新从1开始
  - 起始地址：固定为32
  - 过滤值：使用目标标签的完整EPC
  - 长度：自动计算

**盘点命令2参数设置：**
- 单个16进制命令输入框
- 用于在设置过滤条件后执行的盘点命令

**参数管理功能：**
- 保存当前配置到文件
- 从文件加载配置
- 重置为默认配置

#### 右侧：目标标签列表与匹配状态（60%宽度）

**顶部统计区域：**
- 复制现有盘点统计显示格式
- 统计逻辑调整为单次完整盘点的汇总数据
- 显示格式：
  ```
  盘点命令1统计：耗时 00:02.123  读取次数 456  发现标签 12个
  盘点命令2统计：耗时 00:01.567  读取次数 234  发现标签 8个
  总计统计：    总耗时 00:03.690  总读取 690   目标匹配 15/20
  ```

**目标标签列表：**
- 表格显示：序号、EPC、匹配状态、首次发现时间、读取次数
- 匹配状态用颜色区分：绿色（已匹配）、红色（未匹配）、灰色（未开始）
- 支持手动添加/删除目标EPC
- 支持清空整个目标列表
- 支持从文件导入/导出目标列表

## 🚀 智能盘点策略执行逻辑

### 外层循环：用户设置的循环次数
```
循环3次，间隔2秒的执行流程：
第1次完整盘点(使用命令1) → 等待2秒 → 第2次完整盘点(使用命令3) → 等待2秒 → 第3次完整盘点(使用命令1)
```

**命令轮换规则：**
- 只在已启用的命令间轮换
- 示例：启用命令1和命令3，轮换顺序为 1→3→1→3
- 跳过未启用的命令

### 单次完整盘点流程

```
开始 → 清除全部过滤 → 执行盘点命令1 → 收集结果 → 分析未匹配标签
↓
情况1：匹配到全部目标标签
  → 直接结束本次盘点（不执行盘点命令2）
↓
情况2：有未匹配标签且数量 ≤ 5
  → 设置这些标签的EPC过滤 → 执行盘点命令2 → 收集结果 → 结束
↓
情况3：有未匹配标签且数量 > 5
  → 进入内层循环（批量过滤处理）
```

### 内层循环：批量过滤处理

当未匹配标签数量 > 5时：
```
确定X个未匹配标签（固定列表）
↓
第1批：设置前5个EPC过滤 → 执行盘点命令2 → 收集结果 → 清除过滤
↓
第2批：设置接下来5个EPC过滤 → 执行盘点命令2 → 收集结果 → 清除过滤
↓
重复直到X个未匹配标签都被设置过滤并执行过盘点命令2
```

**关键说明：**
- 执行盘点命令1后确定的未匹配标签列表在内层循环中保持固定
- 内层循环的目标是将这些固定的未匹配标签全部设置一遍过滤+盘点命令2
- 不需要在每批处理后重新计算未匹配数量

### 举例说明

**场景**：目标标签20个，设置循环2次，间隔1秒，启用命令1和命令3

**执行过程**：
1. **第1次完整盘点（使用命令1）**：
   - 清除过滤 → 执行命令1 → 发现12个目标标签
   - 确定8个未匹配标签 → 第1批设置前5个过滤 → 执行命令2 → 清除过滤
   - 第2批设置后3个过滤 → 执行命令2 → 清除过滤 → 本次盘点结束

2. **等待1秒**

3. **第2次完整盘点（使用命令3）**：
   - 清除过滤 → 执行命令3 → 根据整体匹配情况判断是否需要继续
   - 如果仍有未匹配标签，继续执行过滤+命令2的流程

## 🛠️ 技术实现要点

### 架构要求
- **框架**：使用WPF + ModernWpf框架保持UI一致性
- **模式**：遵循现有项目的MVVM架构模式
- **ViewModel**：创建`InventoryTestViewModel`管理页面逻辑
- **模型**：创建`TargetTag`模型类表示目标标签

### 集成要求
- **命令复用**：复用现有的`ReaderManager`中的0x98标签过滤命令实现
- **盘点复用**：复用现有的0x8A和0x8B盘点命令实现
- **数据集成**：与现有标签数据管理功能无缝集成
- **功能隔离**：确保不影响现有功能的正常使用

### 数据持久化
- **参数配置**：保存到JSON文件
- **目标标签列表**：支持导入/导出CSV格式
- **测试结果**：支持导出到Excel

### 错误处理
- **异常处理**：完善的异常处理和用户提示
- **状态检查**：设备连接状态检查
- **参数验证**：参数有效性验证
- **日志记录**：操作日志记录

### 性能优化
- **UI虚拟化**：大量目标标签时的UI虚拟化
- **批量优化**：批量过滤设置的优化
- **实时更新**：实时状态更新的性能优化

## 📝 开发计划

### 第一阶段：基础功能实现
1. 在`AdvancedCommandsPage`添加"保存到目标标签列表"按钮
2. 创建`InventoryTestPage`页面框架
3. 实现目标标签管理功能

### 第二阶段：核心逻辑实现
1. 实现智能盘点策略核心逻辑
2. 实现双层循环控制
3. 实现批量过滤处理

### 第三阶段：完善和优化
1. 完善UI交互和数据展示
2. 实现数据持久化功能
3. 性能优化和错误处理

## ✅ 验收标准

### 功能验收
- [ ] 能够保存当前盘点结果到目标标签列表
- [ ] 能够配置和执行智能盘点策略
- [ ] 能够正确处理双层循环逻辑
- [ ] 能够动态设置标签过滤条件
- [ ] 能够实时显示匹配状态和统计信息

### 性能验收
- [ ] 支持1000+目标标签的处理
- [ ] UI响应流畅，无卡顿现象
- [ ] 内存使用合理，无内存泄漏

### 稳定性验收
- [ ] 长时间运行稳定
- [ ] 异常情况下能正确恢复
- [ ] 与现有功能无冲突

---

*本文档作为智能盘点策略功能的开发指南，所有实现都应严格遵循此需求规范。*
