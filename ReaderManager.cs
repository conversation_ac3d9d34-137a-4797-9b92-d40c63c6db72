using System;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

// Response analysis types
public enum ResponseType
{
    NoResponse,
    InvalidFormat,
    Success,
    Error,
    DataResponse
}

public class ResponseInfo
{
    public bool IsSuccess { get; set; }
    public byte? ErrorCode { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public ResponseType ResponseType { get; set; }
}

// 标签数据事件参数
public class TagDataEventArgs : EventArgs
{
    public string EPC { get; set; } = string.Empty;
    public ushort PC { get; set; }
    public byte RSSI { get; set; }
    public byte Antenna { get; set; }
    public byte Frequency { get; set; }
    public ushort? Phase { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

// 命令完成事件参数
public class CommandCompletedEventArgs : EventArgs
{
    public byte Command { get; set; }
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public byte? ErrorCode { get; set; }
}

// 接收超时事件参数
public class ReceiveTimeoutEventArgs : EventArgs
{
    public byte Command { get; set; }
    public int TimeoutSeconds { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// 天线配置类，用于0x8A命令
/// </summary>
public class AntennaConfig
{
    public byte AntennaNumber { get; set; }  // 0-7 或 0xFF(禁用)
    public byte StayTime { get; set; }       // 停留时间(ms)
}

public class ReaderManager : IDisposable
{
    private SerialPort? serialPort;
    private readonly Action<string>? _addLogAction;
    private readonly Func<bool>? _getShowSerialLogs;

    // 持续接收相关字段
    private CancellationTokenSource? _receiveCancellationTokenSource;
    private bool _isReceiving = false;
    private byte _currentCommand = 0x00;
    private bool _disposed = false;

    // 高性能环形缓冲区
    private readonly RFID_UI.Utils.CircularBuffer _receiveBuffer = new RFID_UI.Utils.CircularBuffer(16384);

    // 数据包处理缓存池
    private readonly Queue<byte[]> _packetPool = new Queue<byte[]>();
    private readonly object _poolLock = new object();

    // 性能监控和优化
    private int _packetsProcessedCount = 0;
    private DateTime _lastPerformanceReport = DateTime.Now;
    private readonly RFID_UI.Utils.PerformanceOptimizer _performanceOptimizer = new RFID_UI.Utils.PerformanceOptimizer();

    // 事件定义
    public event EventHandler<TagDataEventArgs>? TagDataReceived;
    public event EventHandler<CommandCompletedEventArgs>? CommandCompleted;
    public event EventHandler<ReceiveTimeoutEventArgs>? ReceiveTimeout;

    public ReaderManager(string portName, int baudRate = 115200, Action<string>? addLogAction = null, Func<bool>? getShowSerialLogs = null)
    {
        _addLogAction = addLogAction;
        _getShowSerialLogs = getShowSerialLogs;
        serialPort = new SerialPort(portName, baudRate, Parity.None, 8, StopBits.One);
        serialPort.Handshake = Handshake.None;
        serialPort.DtrEnable = true;
        serialPort.RtsEnable = true;
        serialPort.ReadTimeout = 500;
        serialPort.WriteTimeout = 500;
    }

    public bool Connect()
    {
        try
        {
            if (serialPort != null && !serialPort.IsOpen)
            {
                serialPort.Open();
            }
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error opening serial port: {ex.Message}");
            return false;
        }
    }

    public void Disconnect()
    {
        // 停止持续接收
        StopContinuousReceive();

        if (serialPort != null && serialPort.IsOpen)
        {
            serialPort.Close();
        }
    }

    /// <summary>
    /// 停止持续接收数据
    /// </summary>
    public void StopContinuousReceive()
    {
        if (_receiveCancellationTokenSource != null)
        {
            _receiveCancellationTokenSource.Cancel();
            _receiveCancellationTokenSource = null;
        }
        _isReceiving = false;
        AddLog("🛑 已停止持续接收数据");
    }

    public bool IsConnected()
    {
        return serialPort?.IsOpen ?? false;
    }

    public void UpdatePortConfiguration(string portName, int baudRate = 115200)
    {
        // 如果当前连接已打开，先关闭
        if (serialPort?.IsOpen == true)
        {
            AddLog($"🔌 关闭当前串口连接");
            serialPort.Close();
        }

        // 释放旧的串口实例
        serialPort?.Dispose();

        // 创建新的串口配置
        serialPort = new SerialPort(portName, baudRate, Parity.None, 8, StopBits.One);
        serialPort.Handshake = Handshake.None;
        serialPort.DtrEnable = true;
        serialPort.RtsEnable = true;
        serialPort.ReadTimeout = 500;
        serialPort.WriteTimeout = 500;

        AddLog($"🔧 串口配置已更新: {portName}, {baudRate} bps");
    }

    private void AddLog(string message, bool isSerialLog = false)
    {
        // 如果是串口日志且用户关闭了串口日志显示，则不输出
        if (isSerialLog && _getShowSerialLogs?.Invoke() == false)
            return;

        _addLogAction?.Invoke(message);
    }

    /// <summary>
    /// 获取命令名称
    /// </summary>
    private string GetCommandName(byte cmd)
    {
        return cmd switch
        {
            0x70 => "复位读写器",
            0x71 => "设置串口波特率",
            0x72 => "获取固件版本",
            0x73 => "设置读写器地址",
            0x74 => "设置工作天线",
            0x75 => "查询工作天线",
            0x76 => "设置输出功率",
            0x77 => "查询输出功率",
            0x78 => "设置频率范围",
            0x79 => "查询频率范围",
            0x7A => "设置蜂鸣器状态",
            0x7B => "查询设备温度",
            0x89 => "实时盘存",
            0x8A => "快速天线切换盘存",
            0x8B => "自定义会话盘存",
            0x98 => "标签过滤",
            _ => $"未知命令(0x{cmd:X2})"
        };
    }

    public byte[]? ReadBytes()
    {
        if (serialPort != null && serialPort.BytesToRead > 0)
        {
            byte[] buffer = new byte[serialPort.BytesToRead];
            serialPort.Read(buffer, 0, buffer.Length);
            return buffer;
        }
        return null;
    }

    private byte CalculateChecksum(byte[] data)
    {
        byte sum = 0;
        foreach (byte b in data)
        {
            sum += b;
        }
        return (byte)(~sum + 1);
    }

    private byte[] BuildPacket(byte address, byte cmd, byte[]? data)
    {
        int dataLength = (data?.Length ?? 0);
        // The length byte in the protocol is for the fields that follow it (Address, Cmd, Data, Checksum)
        byte len = (byte)(1 + 1 + dataLength + 1); // Address (1) + Cmd (1) + Data (N) + Checksum (1)

        // This array is for checksum calculation: Head (1) + Len (1) + Address (1) + Cmd (1) + Data (N)
        byte[] checksumInput = new byte[1 + 1 + 1 + 1 + dataLength];
        checksumInput[0] = 0xA0; // Head
        checksumInput[1] = len;  // Len
        checksumInput[2] = address;
        checksumInput[3] = cmd;
        if (data != null && data.Length > 0)
        {
            Array.Copy(data, 0, checksumInput, 4, data.Length);
        }
        byte checksum = CalculateChecksum(checksumInput);

        // This is the final packet to be sent over serial: Head (1) + Len (1) + Address (1) + Cmd (1) + Data (N) + Checksum (1)
        byte[] finalPacket = new byte[1 + 1 + len]; // Head + Len + (Address + Cmd + Data + Checksum)
        finalPacket[0] = 0xA0; // Head
        finalPacket[1] = len;  // Len
        finalPacket[2] = address;
        finalPacket[3] = cmd;
        if (data != null && data.Length > 0)
        {
            Array.Copy(data, 0, finalPacket, 4, data.Length);
        }
        finalPacket[finalPacket.Length - 1] = checksum;

        return finalPacket;
    }

    /// <summary>
    /// 发送盘存命令（不等待响应，用于持续接收模式）
    /// </summary>
    public byte[]? SendInventoryCommand(byte cmd, byte[]? data = null, byte address = 0x01)
    {
        if (serialPort == null)
        {
            AddLog("❌ 串口未初始化");
            Console.WriteLine("Error: Serial port is not initialized.");

            // 触发命令完成事件，通知UI层命令失败
            var failedArgs = new CommandCompletedEventArgs
            {
                Command = cmd,
                IsSuccess = false,
                ErrorCode = 0xFF,
                Message = "串口未初始化"
            };
            CommandCompleted?.Invoke(this, failedArgs);
            return null;
        }

        if (!serialPort.IsOpen)
        {
            AddLog("❌ 设备未连接，请先连接设备");
            Console.WriteLine("Error: Serial port is not open. Please connect first.");

            // 触发命令完成事件，通知UI层命令失败
            var failedArgs = new CommandCompletedEventArgs
            {
                Command = cmd,
                IsSuccess = false,
                ErrorCode = 0xFE,
                Message = "设备未连接"
            };
            CommandCompleted?.Invoke(this, failedArgs);
            return null;
        }

        byte[] packet = BuildPacket(address, cmd, data);

        // 添加详细的发送日志
        string cmdName = GetCommandName(cmd);
        string packetHex = BitConverter.ToString(packet);
        AddLog($"📤 发送命令: {cmdName} (0x{cmd:X2})");
        AddLog($"📤 发送数据: {packetHex}", true); // 标记为串口日志
        Console.WriteLine($"Sending packet: {packetHex}");

        try
        {
            serialPort.DiscardInBuffer(); // Clear input buffer before sending command
            serialPort.Write(packet, 0, packet.Length);
            AddLog($"📤 命令发送成功: {packet.Length} 字节");
            Console.WriteLine($"Command sent successfully: {packet.Length} bytes");

            // 对于盘存命令，只发送不等待响应，返回一个标识表示发送成功
            return new byte[] { 0x01 }; // 简单的成功标识
        }
        catch (Exception ex)
        {
            AddLog($"❌ 命令发送失败: {ex.Message}");
            Console.WriteLine($"Error sending command: {ex.Message}");
            return null;
        }
    }

    public byte[]? SendCommand(byte cmd, byte[]? data = null, byte address = 0x01)
    {
        if (serialPort == null)
        {
            Console.WriteLine("Error: Serial port is not initialized.");
            return null;
        }

        if (!serialPort.IsOpen)
        {
            Console.WriteLine("Error: Serial port is not open. Please connect first.");
            return null;
        }

        byte[] packet = BuildPacket(address, cmd, data);

        // 添加详细的发送日志
        string cmdName = GetCommandName(cmd);
        string packetHex = BitConverter.ToString(packet);
        AddLog($"📤 发送命令: {cmdName} (0x{cmd:X2})");
        AddLog($"📤 发送数据: {packetHex}");
        Console.WriteLine($"Sending packet: {packetHex}");

        try
        {
            serialPort.DiscardInBuffer(); // Clear input buffer before sending command
            serialPort.Write(packet, 0, packet.Length);
            AddLog($"📤 命令发送成功: {packet.Length} 字节");
            Console.WriteLine($"Command sent successfully: {packet.Length} bytes");

            byte[] responseBuffer = new byte[256]; // Max possible response length
            int bytesRead = 0;
            long startTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            const int timeoutMs = 5000; // 5 second timeout for response

            // Read Head (0xA0)
            while (bytesRead < 1 && (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - startTime < timeoutMs))
            {
                if (serialPort.BytesToRead > 0)
                {
                    byte b = (byte)serialPort.ReadByte();
                    if (b == 0xA0)
                    {
                        responseBuffer[bytesRead++] = b;
                    }
                    else
                    {
                        // Discard if not header
                        bytesRead = 0;
                    }
                }
                Thread.Sleep(1);
            }
            if (bytesRead == 0)
            {
                Console.WriteLine("Error: Timeout waiting for response header (0xA0)");
                return null; // Timeout or no header
            }

            // Read Len
            while (bytesRead < 2 && (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - startTime < timeoutMs))
            {
                if (serialPort != null && serialPort.BytesToRead > 0)
                {
                    responseBuffer[bytesRead++] = (byte)serialPort.ReadByte();
                }
                Thread.Sleep(1);
            }
            if (bytesRead < 2)
            {
                Console.WriteLine("Error: Timeout waiting for length byte");
                return null; // Timeout or no length byte
            }

            byte len = responseBuffer[1];
            int totalPacketLength = 1 + 1 + len; // Head (1) + Len (1) + len

            // Read remaining bytes
            while (bytesRead < totalPacketLength && (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - startTime < timeoutMs))
            {
                if (serialPort != null && serialPort.BytesToRead > 0)
                {
                    responseBuffer[bytesRead++] = (byte)serialPort.ReadByte();
                }
                Thread.Sleep(1);
            }
            if (bytesRead < totalPacketLength)
            {
                Console.WriteLine($"Error: Incomplete packet received. Expected {totalPacketLength} bytes, got {bytesRead} bytes");
                return null; // Timeout or incomplete packet
            }

            byte[] completeResponse = new byte[bytesRead];
            Array.Copy(responseBuffer, 0, completeResponse, 0, bytesRead);

            // 添加详细的接收日志
            string responseHex = BitConverter.ToString(completeResponse);
            AddLog($"📥 接收响应: {responseHex}");
            AddLog($"📥 响应长度: {bytesRead} 字节");

            // 解析响应状态
            if (bytesRead >= 5)
            {
                byte responseCmd = completeResponse[3];
                string responseCmdName = GetCommandName(responseCmd);
                AddLog($"📥 响应命令: {responseCmdName} (0x{responseCmd:X2})");

                if (bytesRead == 5 && completeResponse[1] == 0x04)
                {
                    byte statusCode = completeResponse[4];
                    string statusMsg = ParseErrorCode(statusCode);
                    AddLog($"📥 命令状态: {statusMsg} (0x{statusCode:X2})");
                }
            }

            Console.WriteLine($"Received response: {responseHex}");
            return completeResponse;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sending/receiving data: {ex.Message}");
            return null;
        }
    }

    // Example command implementation
    public byte[]? ResetReader()
    {
        Console.WriteLine("Sending Reset Command...");
        return SendCommand(0x70);
    }

    public byte[]? SetBeeperMode(byte mode)
    {
        Console.WriteLine($"Setting beeper mode to {mode}...");
        return SendCommand(0x7A, new byte[] { mode });
    }

    public byte[]? GetFirmwareVersion()
    {
        Console.WriteLine("Getting firmware version...");
        return SendCommand(0x72);
    }

    // 0x73: Set Reader Address
    public byte[]? SetReaderAddress(byte newAddress)
    {
        Console.WriteLine($"Setting reader address to {newAddress}...");
        return SendCommand(0x73, new byte[] { newAddress });
    }



    // 0x8B: Customized Session Target Inventory (corrected implementation)
    public byte[]? InventoryRealtime(byte session, byte target, byte repeat)
    {
        Console.WriteLine($"Starting Real-Time Inventory (Session: {session}, Target: {target}, Repeat: {repeat})...");
        // Parameters: Session, Target, Repeat
        byte[] data = new byte[] { session, target, repeat };
        byte[]? response = SendCommand(0x8B, data);
        return response;
    }

    // 0x8A: Fast Switch Antenna Inventory (8天线支持)
    public byte[]? FastSwitchAntenna8(
        AntennaConfig[] antennas,    // 8个天线配置
        byte session,                // 0-3 (S0-S3)
        byte target,                 // 0=A, 1=B
        byte phase,                  // 0=关闭, 1=开启
        byte interval,               // 间隔时间(ms)
        byte repeat,                 // 重复次数
        byte reserve = 0x00)         // 保留字节
    {
        if (antennas.Length != 8)
        {
            Console.WriteLine("Error: FastSwitchAntenna8 requires exactly 8 antenna configurations.");
            return null;
        }

        Console.WriteLine($"Starting Fast Switch Antenna Inventory (8 antennas, Session: {session}, Target: {target}, Phase: {phase})...");

        // 构建22字节的数据包 (不包含Head、Len、Cmd)
        byte[] data = new byte[22];

        // 8个天线配置 (16字节)
        for (int i = 0; i < 8; i++)
        {
            data[i * 2] = antennas[i].AntennaNumber;
            data[i * 2 + 1] = antennas[i].StayTime;
        }

        // 其他参数 (6字节)
        data[16] = session;
        data[17] = target;
        data[18] = phase;
        data[19] = interval;
        data[20] = repeat;
        data[21] = reserve;

        return SendCommand(0x8A, data);
    }

    // 0x8B: Custom Session Inventory (多种格式支持)
    public byte[]? CustomSessionInventory(
        byte session,     // 0-3 (S0-S3)
        byte target,      // 0=A, 1=B
        byte repeat,      // 重复次数
        byte? sl = null,  // SL标志 (V8.2+)
        byte? phase = null) // 相位 0=关闭, 1=开启 (V8.2+)
    {
        Console.WriteLine($"Starting Custom Session Inventory (Session: {session}, Target: {target}, Repeat: {repeat})...");

        byte[] data;

        if (phase.HasValue && sl.HasValue)
        {
            // 带相位指令 (固件 V8.2+) - 需要同时有SL和Phase
            data = new byte[] { session, target, sl.Value, phase.Value, repeat };
            Console.WriteLine($"Using Phase Format (V8.2+): Session={session}, Target={target}, SL=0x{sl.Value:X2}, Phase={phase.Value}, Repeat={repeat}");
            AddLog($"📤 使用带相位格式: Session={session}, Target={target}, SL=0x{sl.Value:X2}, Phase={phase.Value}, Repeat={repeat}");
        }
        else if (sl.HasValue)
        {
            // 扩展指令 (固件 V8.2+)
            data = new byte[] { session, target, sl.Value, repeat };
            Console.WriteLine($"Using Extended Format (V8.2+): Session={session}, Target={target}, SL=0x{sl.Value:X2}, Repeat={repeat}");
            AddLog($"📤 使用扩展格式: Session={session}, Target={target}, SL=0x{sl.Value:X2}, Repeat={repeat}");
        }
        else
        {
            // 基础指令
            data = new byte[] { session, target, repeat };
            Console.WriteLine($"Using Basic Format: Session={session}, Target={target}, Repeat={repeat}");
            AddLog($"📤 使用基础格式: Session={session}, Target={target}, Repeat={repeat}");
        }

        return SendInventoryCommand(0x8B, data);
    }

    // 0x74: Set Work Antenna (支持8天线)
    public byte[]? SetWorkAntenna(byte antennaId)
    {
        if (antennaId > 7)
        {
            Console.WriteLine($"Error: Invalid antenna ID {antennaId}. Valid range: 0-7 (Antenna 1-8)");
            return null;
        }

        Console.WriteLine($"Setting work antenna to Antenna {antennaId + 1} (ID: {antennaId})...");
        return SendCommand(0x74, new byte[] { antennaId });
    }

    // 0x75: Get Work Antenna
    public byte[]? GetWorkAntenna()
    {
        Console.WriteLine("Querying current work antenna...");
        return SendCommand(0x75);
    }

    // 0x76: Set Output Power
    public byte[]? SetOutputPower(byte powerValue)
    {
        Console.WriteLine($"Setting output power to {powerValue}...");
        return SendCommand(0x76, new byte[] { powerValue });
    }

    // 0x77: Get Output Power
    public byte[]? GetOutputPower()
    {
        Console.WriteLine("Querying current output power...");
        return SendCommand(0x77);
    }

    // Helper method to parse antenna response (支持8天线)
    public int ParseAntennaResponse(byte[]? response)
    {
        if (response == null || response.Length < 5)
            return -1;

        // Check if it's a valid antenna response
        if (response[0] == 0xA0 && response[1] == 0x04 && response[3] == 0x75)
        {
            byte antennaId = response[4];
            if (antennaId <= 7) // 支持8天线 (0-7)
            {
                return antennaId; // Return antenna ID (0-7)
            }
        }

        return -1; // Invalid response
    }

    // 0x8A: Fast Switch Antenna Inventory (原始格式，向后兼容)
    public byte[]? FastSwitchAntennaInventory(byte[] antennas, byte[] stayTimes, byte session = 1, byte target = 0, byte interval = 10, byte repeat = 1)
    {
        if (antennas.Length != stayTimes.Length || antennas.Length > 8)
        {
            Console.WriteLine("Error: Invalid antenna configuration. Maximum 8 antennas supported.");
            return null;
        }

        Console.WriteLine($"Starting fast switch antenna inventory with {antennas.Length} antennas...");

        // 按照原始格式构建数据包
        // 格式: A|Sta|B|Sta|C|Sta|D|Sta|E|Sta|F|Sta|G|Sta|H|Sta|Interval|Reserve(5)|Session|Target|Reserve(3)|Phase|Repeat
        var data = new List<byte>();

        // 添加8个天线配置 (天线号 + 停留时间)，固定8对
        for (int i = 0; i < 8; i++)
        {
            if (i < antennas.Length)
            {
                data.Add(antennas[i]); // 天线号
                data.Add(stayTimes[i]); // 停留时间
            }
            else
            {
                // 未勾选的天线使用FF值
                data.Add(0xFF); // 天线号FF表示不使用
                data.Add(0x00); // 停留时间0
            }
        }

        // Interval
        data.Add(interval);

        // Reserve (5字节)
        data.AddRange(new byte[] { 0x00, 0x00, 0x00, 0x00, 0x00 });

        // Session
        data.Add(session);

        // Target
        data.Add(target);

        // Reserve (3字节)
        data.AddRange(new byte[] { 0x00, 0x00, 0x00 });

        // Phase (暂时固定为0，表示不启用相位)
        data.Add(0x00);

        // Repeat
        data.Add(repeat);

        AddLog($"📤 0x8A命令(原始格式): Session={session}, Target={target}, Repeat={repeat}");

        return SendInventoryCommand(0x8A, data.ToArray());
    }

    // 0x8A: Fast Switch Antenna Inventory (V3.8协议格式，支持SL和临时功率)
    public byte[]? FastSwitchAntennaInventoryV38(
        byte[] antennas,
        byte[] stayTimes,
        byte session = 1,
        byte target = 0,
        byte interval = 10,
        byte repeat = 1,
        bool enableSL = false,
        byte slValue = 0,
        bool enablePhase = false,
        byte tempPowerValue = 30)
    {
        if (antennas.Length != stayTimes.Length || antennas.Length > 8)
        {
            Console.WriteLine("Error: Invalid antenna configuration. Maximum 8 antennas supported.");
            return null;
        }

        // 验证临时功率值范围 (20-33 dBm)
        if (tempPowerValue < 20 || tempPowerValue > 33)
        {
            AddLog($"❌ 临时功率值超出范围: {tempPowerValue} dBm (有效范围: 20-33 dBm)");
            Console.WriteLine($"Error: Temporary power value out of range: {tempPowerValue} dBm (valid range: 20-33 dBm)");
            return null;
        }

        Console.WriteLine($"Starting fast switch antenna inventory with {antennas.Length} antennas...");

        // 按照V3.8协议格式构建数据包 (0x25 = 37字节)
        // 格式: A|StayA|B|StayB|C|StayC|D|StayD|E|StayE|F|StayF|G|StayG|H|StayH|Interval|Reserve1|Reserve2|Reserve3|Reserve4|SL|Session|Target|Phase|Pow1|Pow2|Pow3|Pow4|Pow5|Pow6|Pow7|Pow8|Repeat
        var data = new List<byte>();

        // 添加8个天线配置 (天线号 + 停留时间)，固定8对 (16字节)
        for (int i = 0; i < 8; i++)
        {
            if (i < antennas.Length)
            {
                data.Add(antennas[i]); // 天线号
                data.Add(stayTimes[i]); // 停留时间
            }
            else
            {
                // 未勾选的天线使用FF值
                data.Add(0xFF); // 天线号FF表示不使用
                data.Add(0x00); // 停留时间0
            }
        }

        // Interval (1字节)
        data.Add(interval);

        // Reserve1-4 (4字节)
        data.AddRange(new byte[] { 0x00, 0x00, 0x00, 0x00 });

        // SL (1字节) - 根据enableSL决定
        data.Add(enableSL ? slValue : (byte)0x00);

        // Session (1字节)
        data.Add(session);

        // Target (1字节)
        data.Add(target);

        // Phase (1字节) - 根据enablePhase决定
        data.Add(enablePhase ? (byte)0x01 : (byte)0x00);

        // Pow1-Pow8 (8字节) - 临时功率值
        for (int i = 0; i < 8; i++)
        {
            data.Add(tempPowerValue);
        }

        // Repeat (1字节)
        data.Add(repeat);

        string slInfo = enableSL ? $", SL={slValue}" : "";
        string phaseInfo = enablePhase ? ", Phase=ON" : "";
        AddLog($"📤 0x8A命令(V3.8格式): Session={session}, Target={target}{slInfo}{phaseInfo}, TempPower={tempPowerValue}, Repeat={repeat}");

        return SendInventoryCommand(0x8A, data.ToArray());
    }

    // 简化的4天线快速轮询（向后兼容）
    public byte[]? FastSwitchAntennaInventory4(byte antenna1 = 0, byte stay1 = 10, byte antenna2 = 1, byte stay2 = 10,
                                              byte antenna3 = 2, byte stay3 = 10, byte antenna4 = 3, byte stay4 = 10,
                                              byte interval = 10, byte repeat = 1)
    {
        Console.WriteLine("Starting 4-antenna fast switch inventory...");

        // 构建4天线基础指令数据包
        byte[] data = {
            antenna1, stay1, antenna2, stay2, antenna3, stay3, antenna4, stay4,
            interval, repeat
        };

        return SendCommand(0x8A, data);
    }

    // Helper method to parse error codes
    public string ParseErrorCode(byte errorCode)
    {
        return errorCode switch
        {
            0x10 => "Command Success",
            0x11 => "Command Failed",
            0x41 => "Inventory Complete - No Tags Found", // 基于实际观察添加
            0x80 => "No Tag Response",
            0x81 => "Tag Return Error",
            0x82 => "Command Length Error",
            0x83 => "Illegal Command",
            0x84 => "Parameter Error",
            0x85 => "Antenna Not Connected",
            0x86 => "Frequency Setting Error",
            0x87 => "Power Setting Error",
            0xEE => "Operation Failed",
            _ => $"Unknown Error Code: 0x{errorCode:X2}"
        };
    }

    // Helper method to check if response indicates success
    public bool IsResponseSuccess(byte[]? response)
    {
        if (response == null || response.Length < 5)
            return false;

        // Check if it's an error response (Len = 4, contains error code)
        if (response.Length == 5 && response[1] == 0x04)
        {
            byte errorCode = response[3]; // Error code is at position 3
            return errorCode == 0x10; // 0x10 = Command Success
        }

        return true; // Assume success for other response formats
    }

    // Enhanced method to get detailed response information
    public ResponseInfo AnalyzeResponse(byte[]? response)
    {
        if (response == null)
        {
            return new ResponseInfo
            {
                IsSuccess = false,
                ErrorCode = null,
                ErrorMessage = "No response received (timeout or connection error)",
                ResponseType = ResponseType.NoResponse
            };
        }

        if (response.Length < 5)
        {
            return new ResponseInfo
            {
                IsSuccess = false,
                ErrorCode = null,
                ErrorMessage = $"Invalid response length: {response.Length} bytes (minimum 5 required)",
                ResponseType = ResponseType.InvalidFormat
            };
        }

        // Check packet header
        if (response[0] != 0xA0)
        {
            return new ResponseInfo
            {
                IsSuccess = false,
                ErrorCode = null,
                ErrorMessage = $"Invalid packet header: 0x{response[0]:X2} (expected 0xA0)",
                ResponseType = ResponseType.InvalidFormat
            };
        }

        // Check if it's an error response (Len = 4, contains error code)
        if (response.Length == 5 && response[1] == 0x04)
        {
            byte errorCode = response[3];
            bool isSuccess = errorCode == 0x10;

            return new ResponseInfo
            {
                IsSuccess = isSuccess,
                ErrorCode = errorCode,
                ErrorMessage = ParseErrorCode(errorCode),
                ResponseType = isSuccess ? ResponseType.Success : ResponseType.Error
            };
        }

        // For longer responses, assume success (data response)
        return new ResponseInfo
        {
            IsSuccess = true,
            ErrorCode = null,
            ErrorMessage = "Command executed successfully",
            ResponseType = ResponseType.DataResponse
        };
    }

    /// <summary>
    /// 开始持续接收数据（用于盘存命令）
    /// </summary>
    /// <param name="command">当前执行的命令</param>
    /// <param name="timeoutSeconds">超时时间（秒）</param>
    public async Task StartContinuousReceiveAsync(byte command, int timeoutSeconds = 3)
    {
        if (_isReceiving)
        {
            AddLog("⚠️ 已在接收数据中，请先停止当前接收");
            return;
        }

        _currentCommand = command;
        _isReceiving = true;
        _receiveCancellationTokenSource = new CancellationTokenSource();

        AddLog($"🔄 开始持续接收数据，超时时间: {timeoutSeconds}秒");

        try
        {
            await Task.Run(() => ContinuousReceiveLoop(timeoutSeconds, _receiveCancellationTokenSource.Token));
        }
        catch (OperationCanceledException)
        {
            AddLog("🛑 接收数据已被取消");

            // 触发命令完成事件，通知UI层操作被取消
            var cancelledArgs = new CommandCompletedEventArgs
            {
                Command = _currentCommand,
                IsSuccess = false,
                ErrorCode = 0xFC,
                Message = "操作被用户取消"
            };
            CommandCompleted?.Invoke(this, cancelledArgs);
        }
        catch (Exception ex)
        {
            AddLog($"❌ 接收数据异常: {ex.Message}");

            // 触发命令完成事件，通知UI层发生异常
            var exceptionArgs = new CommandCompletedEventArgs
            {
                Command = _currentCommand,
                IsSuccess = false,
                ErrorCode = 0xFD,
                Message = $"接收异常: {ex.Message}"
            };
            CommandCompleted?.Invoke(this, exceptionArgs);
        }
        finally
        {
            _isReceiving = false;
            _receiveCancellationTokenSource = null;
        }
    }

    /// <summary>
    /// 持续接收循环
    /// </summary>
    private void ContinuousReceiveLoop(int timeoutSeconds, CancellationToken cancellationToken)
    {
        if (serialPort == null || !serialPort.IsOpen)
        {
            AddLog("❌ 串口未连接，无法接收数据");
            return;
        }

        var startTime = DateTime.Now;
        var timeoutTime = startTime.AddSeconds(timeoutSeconds);
        bool hasReceivedData = false;

        while (!cancellationToken.IsCancellationRequested && DateTime.Now < timeoutTime)
        {
            try
            {
                if (serialPort.BytesToRead > 0)
                {
                    // 批量处理多个数据包
                    var packets = ReceiveMultiplePackets();
                    foreach (var response in packets)
                    {
                        hasReceivedData = true;
                        ProcessReceivedPacket(response);

                        // 性能监控和优化
                        _packetsProcessedCount++;
                        if (DateTime.Now - _lastPerformanceReport > TimeSpan.FromSeconds(5))
                        {
                            _performanceOptimizer.OptimizeIfNeeded();
                            var perfReport = _performanceOptimizer.GetPerformanceReport();
                            AddLog($"📊 性能统计: 已处理{_packetsProcessedCount}个数据包", true);
                            AddLog($"🔧 性能状态: {perfReport}", true);
                            _lastPerformanceReport = DateTime.Now;
                        }

                        // 检查是否为命令完成响应
                        if (IsCommandCompletionResponse(response))
                        {
                            AddLog("✅ 收到命令完成响应，结束接收");
                            return; // 直接返回，不需要break
                        }
                    }

                    // 如果处理了数据包，重置超时时间
                    if (packets.Count > 0)
                    {
                        timeoutTime = DateTime.Now.AddSeconds(timeoutSeconds);
                    }
                }

                Thread.Sleep(1); // 减少延迟，提高响应速度
            }
            catch (Exception ex)
            {
                AddLog($"❌ 接收数据时发生异常: {ex.Message}");
                break;
            }
        }

        // 处理超时情况
        if (!hasReceivedData && !cancellationToken.IsCancellationRequested)
        {
            var timeoutArgs = new ReceiveTimeoutEventArgs
            {
                Command = _currentCommand,
                TimeoutSeconds = timeoutSeconds,
                Message = $"命令 0x{_currentCommand:X2} 接收超时"
            };
            ReceiveTimeout?.Invoke(this, timeoutArgs);
            AddLog($"⏰ 接收超时: {timeoutArgs.Message}");
        }
    }

    /// <summary>
    /// 高性能批量接收多个数据包
    /// </summary>
    private List<byte[]> ReceiveMultiplePackets()
    {
        var packets = new List<byte[]>();

        if (serialPort == null || !serialPort.IsOpen)
            return packets;

        try
        {
            // 读取所有可用数据到环形缓冲区
            if (serialPort.BytesToRead > 0)
            {
                byte[] tempBuffer = GetTempBuffer(serialPort.BytesToRead);
                int bytesRead = serialPort.Read(tempBuffer, 0, serialPort.BytesToRead);
                int written = _receiveBuffer.Write(tempBuffer, 0, bytesRead);

                if (written < bytesRead)
                {
                    AddLog($"⚠️ 缓冲区满，丢失{bytesRead - written}字节数据", true);
                }

                ReturnTempBuffer(tempBuffer);

                if (bytesRead > 100) // 只在大量数据时记录日志
                {
                    AddLog($"📥 读取{bytesRead}字节，缓冲区大小: {_receiveBuffer.Count}", true);
                }
            }

            // 从环形缓冲区中提取所有完整的数据包
            byte[]? packet;
            while ((packet = _receiveBuffer.ExtractPacket()) != null)
            {
                packets.Add(packet);
            }

            if (packets.Count > 5) // 只在大量数据包时记录日志
            {
                AddLog($"🔄 批量处理{packets.Count}个数据包，缓冲区剩余: {_receiveBuffer.Count}字节", true);
            }
        }
        catch (Exception ex)
        {
            AddLog($"❌ 批量接收数据包异常: {ex.Message}");
        }

        return packets;
    }

    /// <summary>
    /// 获取临时缓冲区（对象池）
    /// </summary>
    private byte[] GetTempBuffer(int minSize)
    {
        lock (_poolLock)
        {
            if (_packetPool.Count > 0)
            {
                var buffer = _packetPool.Dequeue();
                if (buffer.Length >= minSize)
                    return buffer;
            }
        }

        // 创建新缓冲区，大小为2的幂次
        int size = 1024;
        while (size < minSize) size *= 2;
        return new byte[size];
    }

    /// <summary>
    /// 归还临时缓冲区（对象池）
    /// </summary>
    private void ReturnTempBuffer(byte[] buffer)
    {
        if (buffer.Length <= 8192) // 只缓存小于8KB的缓冲区
        {
            lock (_poolLock)
            {
                if (_packetPool.Count < 10) // 限制池大小
                {
                    _packetPool.Enqueue(buffer);
                }
            }
        }
    }

    /// <summary>
    /// 接收单个数据包（优化版本）
    /// </summary>
    private byte[]? ReceiveSinglePacket()
    {
        if (serialPort == null || !serialPort.IsOpen)
            return null;

        try
        {
            // 检查是否有足够的数据
            if (serialPort.BytesToRead < 2)
                return null;

            // 读取可用的所有数据到缓冲区
            int availableBytes = serialPort.BytesToRead;
            byte[] buffer = new byte[availableBytes];
            int actualRead = serialPort.Read(buffer, 0, availableBytes);

            // 在缓冲区中查找完整的数据包
            for (int i = 0; i < actualRead - 1; i++)
            {
                if (buffer[i] == 0xA0) // 找到头字节
                {
                    if (i + 1 < actualRead) // 确保有长度字节
                    {
                        byte len = buffer[i + 1];
                        int totalPacketLength = 1 + 1 + len; // Head + Len + Data

                        if (i + totalPacketLength <= actualRead) // 确保有完整数据包
                        {
                            byte[] packet = new byte[totalPacketLength];
                            Array.Copy(buffer, i, packet, 0, totalPacketLength);

                            // 将剩余数据放回串口缓冲区（如果有的话）
                            int remainingBytes = actualRead - (i + totalPacketLength);
                            if (remainingBytes > 0)
                            {
                                // 注意：这里需要特殊处理，因为SerialPort不支持"放回"数据
                                // 我们需要使用一个内部缓冲区来处理这种情况
                                AddLog($"⚠️ 检测到多个数据包合并，剩余{remainingBytes}字节需要处理", true);
                            }

                            return packet;
                        }
                    }
                }
            }

            // 如果没有找到完整数据包，等待更多数据
            return null;
        }
        catch (Exception ex)
        {
            AddLog($"❌ 接收数据包异常: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 高性能处理接收到的数据包
    /// </summary>
    private void ProcessReceivedPacket(byte[] response)
    {
        // 只在需要时生成详细日志
        bool needDetailedLog = _getShowSerialLogs?.Invoke() ?? false;

        if (needDetailedLog)
        {
            string responseHex = BitConverter.ToString(response);
            AddLog($"📥 接收响应: {responseHex}", true);

            if (response.Length >= 4)
            {
                AddLog($"🔍 数据包: Head=0x{response[0]:X2}, Len=0x{response[1]:X2}, Cmd=0x{response[3]:X2}", true);
            }
        }

        // 快速检查是否为命令完成响应
        if (IsCommandCompletionResponse(response))
        {
            if (needDetailedLog)
                AddLog($"✅ 命令完成响应", true);
            ProcessCommandCompletionResponse(response);
        }
        else
        {
            // 解析为标签数据（减少日志输出）
            ProcessTagDataResponse(response);
        }
    }

    /// <summary>
    /// 检查是否为命令完成响应
    /// </summary>
    private bool IsCommandCompletionResponse(byte[] response)
    {
        // 根据协议文档，命令完成响应有明确的格式：
        // 0x8A命令完成: A0 0A 8A TotalRead(3) CommandDuration(4) Check (总长度11字节)
        // 0x8B命令完成: A0 0A 8B AntID(1) ReadRate(2) TotalRead(4) Check (总长度11字节)
        // 操作失败: A0 04 8A/8B ErrorCode Check (总长度6字节)

        if (response.Length < 4)
            return false;

        // 检查头字节
        if (response[0] != 0xA0)
            return false;

        // 检查命令字段是否匹配当前执行的命令
        byte cmdInResponse = response[3];
        if (cmdInResponse != _currentCommand)
            return false;

        // 检查是否为命令完成响应的特定格式
        byte len = response[1];

        if (_currentCommand == 0x8A || _currentCommand == 0x8B)
        {
            // 对于0x8A和0x8B命令：
            // 1. 命令成功完成响应: Len = 0x0A (10字节数据，总长度12字节，包含Address)
            // 2. 操作失败响应: Len = 0x04 (4字节数据，总长度7字节，包含Address)
            bool isCompletionResponse = (len == 0x0A && response.Length == 12) ||
                                      (len == 0x04 && response.Length == 7);

            if (isCompletionResponse)
            {
                AddLog($"🔍 检测到命令完成响应: Len=0x{len:X2}, 总长度={response.Length}字节", true);
            }

            return isCompletionResponse;
        }

        // 对于其他命令，使用原来的逻辑
        return len == 0x04 && response.Length >= 5;
    }

    /// <summary>
    /// 处理命令完成响应
    /// </summary>
    private void ProcessCommandCompletionResponse(byte[] response)
    {
        if (response.Length < 4)
            return;

        byte len = response[1];
        byte cmd = response[3];

        if (cmd == 0x8A || cmd == 0x8B)
        {
            if (len == 0x0A && response.Length == 12)
            {
                // 命令成功完成响应（包含Address字段）
                ProcessInventoryCompletionResponse(response);
            }
            else if (len == 0x04 && response.Length == 7)
            {
                // 操作失败响应（包含Address字段）
                // 格式: A0 04 [Address] 8A/8B ErrorCode Check
                // 位置: 0  1     2       3    4        5
                byte errorCode = response[4]; // ErrorCode在位置4
                string errorMsg = ParseErrorCode(errorCode);

                AddLog($"❌ 命令执行失败: {errorMsg} (0x{errorCode:X2})");

                var completedArgs = new CommandCompletedEventArgs
                {
                    Command = _currentCommand,
                    IsSuccess = false,
                    ErrorCode = errorCode,
                    Message = errorMsg
                };

                CommandCompleted?.Invoke(this, completedArgs);
            }
        }
        else
        {
            // 其他命令的处理逻辑
            if (response.Length >= 5)
            {
                byte statusCode = response[4];
                string statusMsg = ParseErrorCode(statusCode);
                bool isSuccess = statusCode == 0x10;

                AddLog($"📋 命令完成: {statusMsg} (0x{statusCode:X2})");

                var completedArgs = new CommandCompletedEventArgs
                {
                    Command = _currentCommand,
                    IsSuccess = isSuccess,
                    ErrorCode = statusCode,
                    Message = statusMsg
                };

                CommandCompleted?.Invoke(this, completedArgs);
            }
        }
    }

    /// <summary>
    /// 处理盘存命令的成功完成响应
    /// </summary>
    private void ProcessInventoryCompletionResponse(byte[] response)
    {
        byte cmd = response[3];

        if (cmd == 0x8A)
        {
            // 0x8A命令完成: A0 0A [Address] 8A TotalRead(3) CommandDuration(4) Check
            if (response.Length >= 12)
            {
                // 解析TotalRead (3字节，高位在前) - 位置4,5,6
                uint totalRead = (uint)((response[4] << 16) | (response[5] << 8) | response[6]);

                // 解析CommandDuration (4字节，高位在前) - 位置7,8,9,10
                uint duration = (uint)((response[7] << 24) | (response[8] << 16) | (response[9] << 8) | response[10]);

                AddLog($"✅ 0x8A命令执行完成");
                AddLog($"📊 标签读取总次数: {totalRead}");
                AddLog($"⏱️ 命令执行时间: {duration}ms");

                var completedArgs = new CommandCompletedEventArgs
                {
                    Command = _currentCommand,
                    IsSuccess = true,
                    ErrorCode = null,
                    Message = $"命令完成，读取{totalRead}次，耗时{duration}ms"
                };

                CommandCompleted?.Invoke(this, completedArgs);
            }
        }
        else if (cmd == 0x8B)
        {
            // 0x8B命令完成: A0 0A [Address] 8B AntID(1) ReadRate(2) TotalRead(4) Check
            if (response.Length >= 12)
            {
                // AntID - 位置4
                byte antId = response[4];
                // ReadRate - 位置5,6 (2字节，高位在前)
                ushort readRate = (ushort)((response[5] << 8) | response[6]);
                // TotalRead - 位置7,8,9,10 (4字节，高位在前)
                uint totalRead = (uint)((response[7] << 24) | (response[8] << 16) | (response[9] << 8) | response[10]);

                AddLog($"✅ 0x8B命令执行完成");
                AddLog($"📡 天线ID: {antId}");
                AddLog($"📈 读取速率: {readRate}");
                AddLog($"📊 标签读取总次数: {totalRead}");

                var completedArgs = new CommandCompletedEventArgs
                {
                    Command = _currentCommand,
                    IsSuccess = true,
                    ErrorCode = null,
                    Message = $"命令完成，天线{antId}读取{totalRead}次，速率{readRate}"
                };

                CommandCompleted?.Invoke(this, completedArgs);
            }
        }
    }

    /// <summary>
    /// 判断状态码是否表示命令完成（成功或失败）
    /// </summary>
    private bool IsCommandCompleteStatus(byte statusCode)
    {
        return statusCode switch
        {
            0x10 => true, // Command Success - 命令成功完成
            0x11 => true, // Command Failed - 命令失败
            0x41 => true, // Parameter Invalid - 参数无效（命令失败）
            0x80 => true, // No Tag Response - 无标签响应（盘存完成）
            0x81 => true, // Tag Return Error - 标签返回错误
            0x82 => true, // Memory Overrun - 内存溢出
            0x83 => true, // Memory Locked - 内存锁定
            0x84 => true, // Parameter Error - 参数错误
            0x85 => true, // Antenna Not Connected - 天线未连接
            0x86 => true, // Frequency Setting Error - 频率设置错误
            0x87 => true, // Power Setting Error - 功率设置错误
            0xEE => true, // Operation Failed - 操作失败
            _ => false    // 其他状态码可能是中间状态，不表示命令完成
        };
    }

    /// <summary>
    /// 处理标签数据响应
    /// </summary>
    private void ProcessTagDataResponse(byte[] response)
    {
        try
        {
            // 根据协议解析标签数据
            // 格式: Head|Len|Address|Cmd|FreqAnt|PC|EPC|RSSI|[Phase]|Check

            // 检查最小长度，确保能安全访问基本字段
            if (response.Length < 10) // Head(1)+Len(1)+Addr(1)+Cmd(1)+FreqAnt(1)+PC(2)+至少1字节EPC+RSSI(1)+Check(1) = 10字节
            {
                AddLog($"⚠️ 数据包长度不足，无法解析标签数据: {response.Length}字节");
                return;
            }

            // 解析数据包长度字段
            byte packetLen = response[1];
            int totalPacketLength = 1 + 1 + packetLen; // Head + Len + Data

            // 验证实际接收长度与Len字段是否一致
            if (response.Length != totalPacketLength)
            {
                AddLog($"⚠️ 数据包长度不一致: Len字段指示{totalPacketLength}字节，实际接收{response.Length}字节");
                return;
            }

            // 解析FreqAnt字段
            byte freqAnt = response[4];
            byte frequency = (byte)((freqAnt >> 2) & 0x3F); // 高6位是频点参数
            byte antennaFromFreqAnt = (byte)(freqAnt & 0x03); // 低2位是天线号

            // 解析PC字段（2字节，大端序）
            ushort pc = (ushort)((response[5] << 8) | response[6]);

            // 从PC字段计算EPC长度（PC的高5位表示EPC长度，以字为单位）
            int epcLengthWords = (pc >> 11) & 0x1F;
            int epcLengthBytes = epcLengthWords * 2;

            // 解析EPC数据
            byte[] epcBytes = new byte[epcLengthBytes];
            Array.Copy(response, 7, epcBytes, 0, epcLengthBytes);
            string epc = BitConverter.ToString(epcBytes).Replace("-", "");

            // 解析RSSI字段
            byte rssiRaw = response[7 + epcLengthBytes];

            // 根据协议文档处理天线号和RSSI
            byte antenna;
            byte rssi;

            // RSSI字段的高位用于判断天线号范围
            if ((rssiRaw & 0x80) == 0) // 高位为0：天线1-4
            {
                antenna = antennaFromFreqAnt; // 使用FreqAnt字段的低2位 (0-3对应天线1-4)
                rssi = rssiRaw; // RSSI值不需要处理
            }
            else // 高位为1：天线5-8
            {
                antenna = (byte)(4 + antennaFromFreqAnt); // 天线5-8 (4+0, 4+1, 4+2, 4+3)
                rssi = (byte)(rssiRaw & 0x7F); // 清除高位，保留实际RSSI值
            }

            // 根据数据包实际长度判断是否包含相位数据
            ushort? phase = null;
            // 无相位: Head(1)+Len(1)+Addr(1)+Cmd(1)+FreqAnt(1)+PC(2)+EPC(N)+RSSI(1)+Check(1) = 9+EPC长度
            int expectedLengthWithoutPhase = 9 + epcLengthBytes;
            // 带相位: 上述 + Phase(2)
            int expectedLengthWithPhase = expectedLengthWithoutPhase + 2;

            if (response.Length == expectedLengthWithPhase)
            {
                // 数据包长度匹配带相位格式，解析相位数据（2字节，大端序）
                int phaseIndex = 7 + epcLengthBytes + 1; // FreqAnt(1)+PC(2)+EPC(N)+RSSI(1)之后
                phase = (ushort)((response[phaseIndex] << 8) | response[phaseIndex + 1]);
            }
            else if (response.Length != expectedLengthWithoutPhase)
            {
                // 数据包长度既不匹配无相位也不匹配带相位格式
                AddLog($"⚠️ 数据包长度异常: EPC长度{epcLengthBytes}字节，期望{expectedLengthWithoutPhase}(无相位)或{expectedLengthWithPhase}(带相位)字节，实际{response.Length}字节");
                // 继续解析，但不解析相位
            }

            // 创建标签数据事件
            var tagDataArgs = new TagDataEventArgs
            {
                EPC = epc,
                PC = pc,
                RSSI = rssi,
                Antenna = antenna,
                Frequency = frequency,
                Phase = phase,
                Timestamp = DateTime.Now
            };

            // 触发事件
            TagDataReceived?.Invoke(this, tagDataArgs);

            // 详细日志输出（用于调试）
            if (_getShowSerialLogs?.Invoke() == true)
            {
                string phaseInfo = phase.HasValue ? $", 相位: {phase.Value}" : "";
                string pcInfo = $"PC: 0x{pc:X4}";
                AddLog($"🏷️ 标签: EPC={epc}, 天线={antenna + 1}, RSSI={rssi}, 频点={frequency}, {pcInfo}{phaseInfo}", true);
            }
        }
        catch (Exception ex)
        {
            AddLog($"❌ 解析标签数据异常: {ex.Message}");
            if (_getShowSerialLogs?.Invoke() == true)
            {
                string hexData = BitConverter.ToString(response);
                AddLog($"🔍 原始数据: {hexData}", true);
            }
        }
    }

    #region IDisposable Implementation

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 停止持续接收
                StopContinuousReceive();

                // 释放串口资源
                if (serialPort != null)
                {
                    if (serialPort.IsOpen)
                    {
                        serialPort.Close();
                    }
                    serialPort.Dispose();
                    serialPort = null;
                }

                // 释放CancellationTokenSource
                _receiveCancellationTokenSource?.Dispose();
                _receiveCancellationTokenSource = null;

                // 清除事件订阅
                TagDataReceived = null;
                CommandCompleted = null;
                ReceiveTimeout = null;
            }

            _disposed = true;
        }
    }

    ~ReaderManager()
    {
        Dispose(false);
    }

    #endregion

    #region 标签过滤命令 (0x98)

    /// <summary>
    /// 设置标签掩码
    /// </summary>
    public byte[]? SetTagMask(RFID_UI.Models.TagMask mask)
    {
        try
        {
            // 计算掩码数据字节长度
            int maskByteLength = (mask.MaskBitLength + 7) / 8; // 向上取整

            var data = new List<byte>();

            // Function (设置掩码)
            data.Add((byte)mask.MaskId); // 0x01-0x05

            // Target
            data.Add((byte)mask.Target);

            // Action
            data.Add((byte)mask.Action);

            // MemBank
            data.Add((byte)mask.MemBank);

            // StartingMaskAdd (1字节) - 固定为32
            data.Add(0x20); // 32的十六进制

            // MaskBitLen (1字节)
            data.Add((byte)(mask.MaskBitLength & 0xFF));

            // Mask数据 - 确保数据长度正确
            if (mask.MaskData.Length != maskByteLength)
            {
                // 重新计算掩码数据
                byte[] correctMaskData = new byte[maskByteLength];
                Array.Copy(mask.MaskData, 0, correctMaskData, 0, Math.Min(mask.MaskData.Length, maskByteLength));
                data.AddRange(correctMaskData);
            }
            else
            {
                data.AddRange(mask.MaskData);
            }

            // Truncate
            data.Add(mask.Truncate ? (byte)0x01 : (byte)0x00);

            AddLog($"📤 设置标签掩码{mask.MaskId}: Target={mask.Target}, Action={mask.Action}, MemBank={mask.MemBank}");
            AddLog($"🔍 掩码内容: {mask.MaskValueHex}, 长度: {mask.MaskBitLength}bit, 字节长度: {maskByteLength}");
            AddLog($"🔍 数据包内容: {BitConverter.ToString(data.ToArray()).Replace("-", " ")}");

            return SendCommand(0x98, data.ToArray());
        }
        catch (Exception ex)
        {
            AddLog($"❌ 设置标签掩码失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 清除标签掩码
    /// </summary>
    /// <param name="maskId">掩码ID，0=清除所有，1-5=清除指定掩码</param>
    public byte[]? ClearTagMask(int maskId = 0)
    {
        try
        {
            byte function = (byte)maskId; // 0x00=清除所有，0x01-0x05=清除指定掩码

            AddLog($"📤 清除标签掩码: {(maskId == 0 ? "全部" : $"Mask No.{maskId}")} (Function: 0x{function:X2})");

            return SendCommand(0x98, new byte[] { function });
        }
        catch (Exception ex)
        {
            AddLog($"❌ 清除标签掩码失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 查询当前标签掩码
    /// </summary>
    public List<RFID_UI.Models.TagMask>? QueryTagMasks()
    {
        try
        {
            AddLog($"📤 查询当前标签掩码");

            // 使用现有的SendCommand方法，但需要处理多个响应包
            var allMasks = new List<RFID_UI.Models.TagMask>();

            // 发送查询命令并等待响应
            byte[]? firstResponse = SendCommand(0x98, new byte[] { 0x20 });

            if (firstResponse == null)
            {
                AddLog("❌ 查询掩码命令无响应");
                return null;
            }

            // 检查是否为无掩码响应
            if (firstResponse.Length == 6 && firstResponse[1] == 0x0B && firstResponse[4] == 0x00)
            {
                AddLog("📋 当前无设置的标签掩码");
                return new List<RFID_UI.Models.TagMask>();
            }

            // 解析第一个掩码数据包
            var firstMask = ParseSingleMaskPacket(firstResponse);
            if (firstMask != null)
            {
                allMasks.Add(firstMask);
                AddLog($"📋 解析掩码: Mask No.{firstMask.MaskId}, 长度: {firstMask.MaskBitLength} bit, 值: {firstMask.MaskValueHex}", true);

                // 从第一个包中获取掩码总数
                int totalMasks = firstResponse.Length >= 6 ? firstResponse[5] : 1;

                // 继续读取剩余的掩码数据包
                for (int i = 1; i < totalMasks; i++)
                {
                    Thread.Sleep(50); // 稍等一下让数据包到达

                    // 尝试读取更多数据包
                    var additionalPackets = ReceiveMultiplePackets();
                    foreach (var packet in additionalPackets)
                    {
                        if (packet.Length >= 6 && packet[0] == 0xA0 && packet[3] == 0x98)
                        {
                            var mask = ParseSingleMaskPacket(packet);
                            if (mask != null && !allMasks.Any(m => m.MaskId == mask.MaskId))
                            {
                                allMasks.Add(mask);
                                AddLog($"📋 解析掩码: Mask No.{mask.MaskId}, 长度: {mask.MaskBitLength} bit, 值: {mask.MaskValueHex}", true);
                            }
                        }
                    }
                }
            }

            AddLog($"📋 查询到 {allMasks.Count} 个标签掩码");
            return allMasks;
        }
        catch (Exception ex)
        {
            AddLog($"❌ 查询标签掩码失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 解析标签掩码查询响应
    /// </summary>
    private List<RFID_UI.Models.TagMask> ParseTagMaskResponse(byte[] response)
    {
        var masks = new List<RFID_UI.Models.TagMask>();

        try
        {
            if (response.Length < 6)
                return masks;

            // 检查是否为无掩码响应
            if (response.Length == 6 && response[1] == 0x0B && response[4] == 0x00)
            {
                AddLog("📋 当前无设置的标签掩码");
                return masks;
            }

            // 单个掩码数据包解析
            // 数据包格式: A0 0D 01 98 [MaskID] 05 00 00 01 20 10 [MaskData] [Check]
            if (response.Length >= 13 && response[0] == 0xA0 && response[3] == 0x98)
            {
                var mask = ParseSingleMaskPacket(response);
                if (mask != null)
                {
                    masks.Add(mask);
                    AddLog($"📋 解析掩码: Mask No.{mask.MaskId}, 长度: {mask.MaskBitLength} bit, 值: {mask.MaskValueHex}", true);
                }
            }

            if (masks.Count > 0)
            {
                AddLog($"📋 本次解析到 {masks.Count} 个标签掩码");
            }
        }
        catch (Exception ex)
        {
            AddLog($"❌ 解析标签掩码响应失败: {ex.Message}");
            if (_getShowSerialLogs?.Invoke() == true)
            {
                string hexData = BitConverter.ToString(response);
                AddLog($"🔍 原始数据: {hexData}", true);
            }
        }

        return masks;
    }

    /// <summary>
    /// 解析单个掩码
    /// </summary>
    private RFID_UI.Models.TagMask? ParseSingleMask(byte[] response, int offset)
    {
        try
        {
            if (offset + 15 > response.Length)
                return null;

            var mask = new RFID_UI.Models.TagMask();

            // Mask ID
            mask.MaskId = response[offset + 4];

            // Mask quantity (跳过)
            // Target
            mask.Target = (RFID_UI.Models.MaskTarget)response[offset + 6];

            // Action
            mask.Action = (RFID_UI.Models.MaskAction)response[offset + 7];

            // MemBank
            mask.MemBank = (RFID_UI.Models.MemoryBank)response[offset + 8];

            // StartingMaskAdd (1字节)
            mask.StartingAddress = response[offset + 9];

            // MaskBitLen (1字节)
            mask.MaskBitLength = response[offset + 10];

            // Mask数据
            int maskByteLength = (mask.MaskBitLength + 7) / 8;
            if (offset + 11 + maskByteLength <= response.Length)
            {
                mask.MaskData = new byte[maskByteLength];
                Array.Copy(response, offset + 11, mask.MaskData, 0, maskByteLength);
            }

            // Truncate
            if (offset + 11 + maskByteLength < response.Length)
            {
                mask.Truncate = response[offset + 11 + maskByteLength] != 0;
            }

            return mask;
        }
        catch (Exception ex)
        {
            AddLog($"❌ 解析单个掩码失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 解析单个掩码数据包
    /// 数据包格式: A0 0D 01 98 [MaskID] [Quantity] [Target] [Action] [MemBank] [StartAddr] [BitLen] [MaskData...] [Truncate] [Check]
    /// </summary>
    private RFID_UI.Models.TagMask? ParseSingleMaskPacket(byte[] packet)
    {
        try
        {
            if (packet.Length < 13)
                return null;

            // 验证包头和命令
            if (packet[0] != 0xA0 || packet[3] != 0x98)
                return null;

            var mask = new RFID_UI.Models.TagMask();

            // Mask ID (位置4)
            mask.MaskId = packet[4];

            // Mask quantity (位置5，跳过)

            // Target (位置6)
            mask.Target = (RFID_UI.Models.MaskTarget)packet[6];

            // Action (位置7)
            mask.Action = (RFID_UI.Models.MaskAction)packet[7];

            // MemBank (位置8)
            mask.MemBank = (RFID_UI.Models.MemoryBank)packet[8];

            // StartingMaskAdd (位置9)
            mask.StartingAddress = packet[9];

            // MaskBitLen (位置10)
            mask.MaskBitLength = packet[10];

            // Mask数据 (从位置11开始)
            int maskByteLength = (mask.MaskBitLength + 7) / 8;
            if (11 + maskByteLength < packet.Length)
            {
                mask.MaskData = new byte[maskByteLength];
                Array.Copy(packet, 11, mask.MaskData, 0, maskByteLength);
            }
            else
            {
                mask.MaskData = new byte[0];
            }

            // Truncate (在掩码数据后面)
            if (11 + maskByteLength < packet.Length)
            {
                mask.Truncate = packet[11 + maskByteLength] != 0;
            }

            return mask;
        }
        catch (Exception ex)
        {
            AddLog($"❌ 解析单个掩码数据包失败: {ex.Message}");
            if (_getShowSerialLogs?.Invoke() == true)
            {
                string hexData = BitConverter.ToString(packet);
                AddLog($"🔍 原始数据包: {hexData}", true);
            }
            return null;
        }
    }

    #endregion
}
