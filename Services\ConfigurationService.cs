using System;
using System.IO;
using System.Text.Json;

namespace RFID_UI.Services
{
    /// <summary>
    /// 应用程序配置服务
    /// </summary>
    public class ConfigurationService
    {
        private const string ConfigFileName = "rfid_config.json";
        private static readonly string ConfigFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "RFID_UI",
            ConfigFileName
        );

        /// <summary>
        /// 应用程序配置
        /// </summary>
        public class AppConfig
        {
            public string LastUsedPort { get; set; } = "COM1";
            public int BaudRate { get; set; } = 115200;
            public int ReaderAddress { get; set; } = 1;
            public double PowerLevel { get; set; } = 20.0;
            public string BuzzerMode { get; set; } = "开启";
            public bool ShowSerialLogs { get; set; } = false;
            public int ReceiveTimeout { get; set; } = 3;
            public bool AutoSaveInventoryData { get; set; } = false;
            public string LastExportPath { get; set; } = "";
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public static AppConfig LoadConfig()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    var json = File.ReadAllText(ConfigFilePath);
                    var config = JsonSerializer.Deserialize<AppConfig>(json);
                    return config ?? new AppConfig();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载配置失败: {ex.Message}");
            }

            return new AppConfig();
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public static void SaveConfig(AppConfig config)
        {
            try
            {
                var directory = Path.GetDirectoryName(ConfigFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                var json = JsonSerializer.Serialize(config, options);
                File.WriteAllText(ConfigFilePath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取默认导出路径
        /// </summary>
        public static string GetDefaultExportPath()
        {
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            return Path.Combine(documentsPath, "RFID_Data");
        }
    }
}
