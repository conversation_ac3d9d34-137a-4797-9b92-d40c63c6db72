<Page x:Class="RFID_UI.Views.Pages.InventoryTestPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      xmlns:local="clr-namespace:RFID_UI.Views.Pages"
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="盘点测试">

    <Grid Margin="20">
        <Grid.ColumnDefinitions>
            <!-- 左侧：盘点策略设置界面（40%宽度） -->
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="20"/>
            <!-- 右侧：目标标签列表与匹配状态（60%宽度） -->
            <ColumnDefinition Width="3*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧：盘点策略设置界面 -->
        <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- 顶部控制区域 -->
                <ui:Card Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <ui:TextBlock Text="📋 盘点控制" FontSize="14" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 开始盘点按钮 -->
                            <ui:Button Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="5"
                                      Content="{Binding InventoryButtonText}"
                                      Icon="{ui:SymbolIcon Symbol=Play24}"
                                      Command="{Binding StartInventoryCommand}"
                                      Appearance="Primary"
                                      FontSize="14"
                                      MinWidth="150"
                                      HorizontalAlignment="Center"
                                      Margin="0,0,0,15"/>

                            <!-- 循环次数 -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="循环次数:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBox Grid.Row="1" Grid.Column="1"
                                    Text="{Binding CycleCount}"
                                    FontSize="14"
                                    Width="70"
                                    HorizontalAlignment="Left"
                                    Margin="0,0,0,8"/>

                            <!-- 间隔时间 -->
                            <TextBlock Grid.Row="1" Grid.Column="3" Text="间隔时间(秒):" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBox Grid.Row="1" Grid.Column="4"
                                    Text="{Binding IntervalSeconds}"
                                    FontSize="14"
                                    Width="70"
                                    HorizontalAlignment="Left"
                                    Margin="0,0,0,8"/>
                        </Grid>

                        <!-- 命令轮换信息 -->
                        <StackPanel Margin="0,10,0,0">
                            <TextBlock Text="{Binding CurrentCommandInfo}"
                                      FontSize="12"
                                      Foreground="DodgerBlue"
                                      Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding CommandRotationHistory}"
                                      FontSize="11"
                                      Foreground="Gray"
                                      TextWrapping="Wrap"
                                      MaxWidth="400"/>
                        </StackPanel>
                    </StackPanel>
                </ui:Card>

                <!-- 盘点命令1参数设置 -->
                <ui:Card Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <ui:TextBlock Text="⚡ 盘点命令1配置" FontSize="14" Margin="0,0,0,15"/>
                        
                        <!-- 命令配置列表 -->
                        <ItemsControl ItemsSource="{Binding Command1Configs}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <CheckBox Grid.Column="0" IsChecked="{Binding IsEnabled}" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Grid.Column="1" Text="{Binding DisplayName}" VerticalAlignment="Center" MinWidth="80" Margin="0,0,8,0"/>
                                        <TextBox Grid.Column="2" Text="{Binding HexCommand}" FontFamily="Consolas" FontSize="12" Margin="0,0,8,0"/>
                                        <ui:Button Grid.Column="3" Content="🔄" ToolTip="拖拽排序" FontSize="12" MinWidth="30"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ui:Card>

                <!-- 标签过滤参数设置 -->
                <ui:Card Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <ui:TextBlock Text="🔍 标签过滤参数" FontSize="14" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Session -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Session:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox Grid.Row="0" Grid.Column="1" 
                                     ItemsSource="{Binding SessionOptions}"
                                     SelectedItem="{Binding SelectedSession}"
                                     FontSize="14" Margin="0,0,0,8"/>

                            <!-- Action -->
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="Action:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox Grid.Row="0" Grid.Column="4" 
                                     ItemsSource="{Binding ActionOptions}"
                                     SelectedItem="{Binding SelectedAction}"
                                     FontSize="14" Margin="0,0,0,8"/>

                            <!-- MemBank -->
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="MemBank:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="4"
                                     ItemsSource="{Binding MemBankOptions}"
                                     SelectedItem="{Binding SelectedMemBank}"
                                     FontSize="14" Margin="0,0,0,8"/>
                        </Grid>
                    </StackPanel>
                </ui:Card>

                <!-- 盘点命令2参数设置 -->
                <ui:Card Margin="0,0,0,20">
                    <StackPanel Margin="20">
                        <ui:TextBlock Text="⚡ 盘点命令2配置" FontSize="14" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="16进制命令:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBox Grid.Column="1" Text="{Binding Command2Hex}" FontFamily="Consolas" FontSize="12"/>
                        </Grid>
                    </StackPanel>
                </ui:Card>

                <!-- 参数管理功能 -->
                <ui:Card>
                    <StackPanel Margin="20">
                        <ui:TextBlock Text="💾 参数管理" FontSize="14" Margin="0,0,0,15"/>
                        
                        <StackPanel Orientation="Horizontal">
                            <ui:Button Content="保存配置" 
                                      Icon="{ui:SymbolIcon Symbol=Save24}"
                                      Command="{Binding SaveConfigCommand}"
                                      FontSize="14" Margin="0,0,8,0"/>
                            <ui:Button Content="加载配置" 
                                      Icon="{ui:SymbolIcon Symbol=Open24}"
                                      Command="{Binding LoadConfigCommand}"
                                      FontSize="14" Margin="0,0,8,0"/>
                            <ui:Button Content="重置配置" 
                                      Icon="{ui:SymbolIcon Symbol=ArrowReset24}"
                                      Command="{Binding ResetConfigCommand}"
                                      FontSize="14"/>
                        </StackPanel>
                    </StackPanel>
                </ui:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 右侧：目标标签列表与匹配状态 -->
        <Grid Grid.Column="2">
            <!-- 统一Card容器：包含盘点统计和目标标签列表 -->
            <ui:Card>
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <!-- 盘点统计 -->
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <!-- 分隔空间 -->
                        <RowDefinition Height="10"/>
                        <!-- 列表标题和操作按钮 -->
                        <RowDefinition Height="Auto"/>
                        <!-- 快速统计信息 -->
                        <RowDefinition Height="Auto"/>
                        <!-- 目标标签表格 -->
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 盘点统计标题 -->
                    <Grid Grid.Row="0" Margin="2,2,2,2">
                        <ui:TextBlock Text="📊 盘点统计" FontSize="14" VerticalAlignment="Center"/>
                    </Grid>

                    <!-- 盘点统计信息 -->
                    <Grid Grid.Row="1" Margin="2,0,2,2" Background="#FFF8F8F8">
                        <StackPanel Margin="8,4">
                            <ui:TextBlock FontSize="12" Margin="0,0,0,2">
                                <Run Text="{Binding Command1Statistics}"/>
                            </ui:TextBlock>
                            <ui:TextBlock FontSize="12" Margin="0,0,0,2">
                                <Run Text="{Binding Command2Statistics}"/>
                            </ui:TextBlock>
                            <ui:TextBlock FontSize="12" FontWeight="Bold">
                                <Run Text="{Binding TotalStatistics}"/>
                            </ui:TextBlock>
                        </StackPanel>
                    </Grid>

                    <!-- 分隔空间 -->
                    <Rectangle Grid.Row="2" Fill="Transparent"/>

                    <!-- 列表标题和操作按钮 - 与高级命令页面保持一致 -->
                    <Grid Grid.Row="3" Margin="2,2,2,2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 左侧：标题 -->
                        <ui:TextBlock Grid.Column="0" Text="🏷️ 目标标签列表" FontSize="14" VerticalAlignment="Center"/>

                        <!-- 右侧：操作按钮 -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <ui:Button Content="添加"
                                      Icon="{ui:SymbolIcon Symbol=Add24}"
                                      Command="{Binding AddTargetTagCommand}"
                                      FontSize="14"
                                      Margin="2,2,2,2"/>
                            <ui:Button Content="清空"
                                      Icon="{ui:SymbolIcon Symbol=Delete24}"
                                      Command="{Binding ClearTargetTagsCommand}"
                                      FontSize="14"
                                      Margin="2,2,2,2"/>
                            <ui:Button Content="导入"
                                      Icon="{ui:SymbolIcon Symbol=Open24}"
                                      Command="{Binding ImportTargetTagsCommand}"
                                      FontSize="14"
                                      Margin="2,2,2,2"/>
                            <ui:Button Content="导出"
                                      Icon="{ui:SymbolIcon Symbol=Save24}"
                                      Command="{Binding ExportTargetTagsCommand}"
                                      FontSize="14"/>
                        </StackPanel>
                    </Grid>

                    <!-- 快速统计信息 -->
                    <Grid Grid.Row="4" Margin="2,0,2,2" Background="#FFF8F8F8">
                        <ui:TextBlock Text="{Binding TargetTagSummary}"
                                     FontSize="12"
                                     Margin="8,4"/>
                    </Grid>

                    <!-- 目标标签表格 - 与高级命令页面保持一致 -->
                    <DataGrid Grid.Row="5"
                             ItemsSource="{Binding TargetTags}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="True"
                             IsReadOnly="False"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             FontSize="12"
                             FontFamily="Consolas"
                             SelectionMode="Extended"
                             VirtualizingPanel.IsVirtualizing="True"
                             VirtualizingPanel.VirtualizationMode="Recycling"
                             VirtualizingPanel.IsContainerVirtualizable="True"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="50"/>
                            <DataGridTextColumn Header="EPC" Binding="{Binding Epc}" Width="auto"/>
                            <DataGridTextColumn Header="匹配状态" Binding="{Binding MatchStatusText}" Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="{Binding MatchStatusColor}"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="首次发现时间" Binding="{Binding FirstFoundTimeText}" Width="120"/>
                            <DataGridTextColumn Header="读取次数" Binding="{Binding ReadCount}" Width="80"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </ui:Card>
        </Grid>
    </Grid>
</Page>
