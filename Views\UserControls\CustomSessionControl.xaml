<UserControl x:Class="RFID_UI.Views.UserControls.CustomSessionControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
             xmlns:local="clr-namespace:RFID_UI.Views.UserControls"
             mc:Ignorable="d" 
             d:DesignHeight="400" d:DesignWidth="600"
             d:DataContext="{d:DesignInstance local:CustomSessionViewModel}">

    <!-- 参数配置 -->
    <StackPanel Margin="0">
        <!-- Session选择 -->
        <Grid Margin="2,2,2,2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <ui:TextBlock Grid.Column="0" Text="Session:" VerticalAlignment="Center" Margin="2,2,2,2"/>
            <ComboBox Grid.Column="1"
                     ItemsSource="{Binding SessionOptions}"
                     SelectedItem="{Binding SelectedSession}"
                     FontSize="14"
                     Width="120"/>
        </Grid>

        <!-- Target选择 -->
        <Grid Margin="2,2,2,2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <ui:TextBlock Grid.Column="0" Text="Target:" VerticalAlignment="Center" Margin="2,2,2,2"/>
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <RadioButton Content="A"
                            IsChecked="{Binding IsTargetASelected}"
                            FontSize="14" Margin="2,2,2,2"/>
                <RadioButton Content="B"
                            IsChecked="{Binding IsTargetBSelected}"
                            FontSize="14"/>
            </StackPanel>
        </Grid>

        <!-- 重复次数 -->
        <Grid Margin="2,2,2,2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="70"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <ui:TextBlock Grid.Column="0" Text="重复次数:" VerticalAlignment="Center" Margin="2,2,2,2"/>
            <ui:TextBox Grid.Column="1"
                       Text="{Binding RepeatCount}"
                       FontSize="14" Margin="2,2,2,2"/>
            <ui:TextBlock Grid.Column="2" Text="次" VerticalAlignment="Center"/>
        </Grid>

        <!-- SL标志 -->
        <Grid Margin="2,2,2,2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <CheckBox Grid.Column="0"
                     Content="使用SL:"
                     IsChecked="{Binding UseSL}"
                     FontSize="14"
                     VerticalAlignment="Center"
                     Margin="2,2,2,2"/>
            <ComboBox Grid.Column="1"
                     ItemsSource="{Binding SLOptions}"
                     SelectedItem="{Binding SelectedSL}"
                     IsEnabled="{Binding UseSL}"
                     FontSize="14"
                     Width="80"/>
        </Grid>

        <!-- 相位检测 -->
        <StackPanel Orientation="Vertical" Margin="0,10,0,0">
            <CheckBox Content="启用相位检测"
                     IsChecked="{Binding EnablePhase}"
                     FontSize="14"/>
            <TextBlock Text="注意：启用相位检测需要同时启用SL参数"
                      FontSize="12"
                      Foreground="Gray"
                      Margin="20,2,0,0"/>
        </StackPanel>
    </StackPanel>
</UserControl>
