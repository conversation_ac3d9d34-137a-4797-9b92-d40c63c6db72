using System.Windows;

namespace RFID_UI
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private void Application_Startup(object sender, StartupEventArgs e)
        {
            try
            {
                // 先创建一个简单的主窗口，不传递任何参数
                var mainWindow = new MainWindow();

                // 设置为主窗口并显示
                this.MainWindow = mainWindow;
                mainWindow.Show();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"启动错误: {ex.Message}\n\n详细信息:\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
