using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace RFID_UI.Models
{
    /// <summary>
    /// 标签数据模型
    /// </summary>
    public class TagData : INotifyPropertyChanged
    {
        private int _index;
        private string _epc = string.Empty;
        private string _pc = string.Empty;
        private string _rssi = string.Empty;
        private string _antenna = string.Empty;
        private string _freqPoint = string.Empty;
        private string _phase = string.Empty;
        private int _count;
        private string _lastSeen = string.Empty;
        private byte _rawFreqAnt;

        /// <summary>
        /// 序号
        /// </summary>
        public int Index
        {
            get => _index;
            set
            {
                _index = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// EPC号
        /// </summary>
        public string Epc
        {
            get => _epc;
            set
            {
                _epc = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// PC值
        /// </summary>
        public string PC
        {
            get => _pc;
            set
            {
                _pc = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 信号强度
        /// </summary>
        public string Rssi
        {
            get => _rssi;
            set
            {
                _rssi = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 天线号
        /// </summary>
        public string Antenna
        {
            get => _antenna;
            set
            {
                _antenna = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 频点
        /// </summary>
        public string FreqPoint
        {
            get => _freqPoint;
            set
            {
                _freqPoint = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 相位值
        /// </summary>
        public string Phase
        {
            get => _phase;
            set
            {
                _phase = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 读取次数
        /// </summary>
        public int Count
        {
            get => _count;
            set
            {
                _count = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 最后读取时间
        /// </summary>
        public string LastSeen
        {
            get => _lastSeen;
            set
            {
                _lastSeen = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 原始FreqAnt字节（高6位频点，低2位天线）
        /// </summary>
        public byte RawFreqAnt
        {
            get => _rawFreqAnt;
            set
            {
                _rawFreqAnt = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
