using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows;
using RFID_UI.Models;
using RFID_UI.Commands;

namespace RFID_UI.ViewModels
{
    public class TagFilterDialogViewModel : INotifyPropertyChanged
    {
        private readonly ReaderManager _readerManager;
        private readonly Action<string>? _addLogAction;

        // 设置过滤相关属性
        private string _selectedMaskId = "Mask No.1";
        private string _selectedSessionId = "S0";
        private string _selectedAction = "00";
        private string _selectedMemBank = "EPC";
        private string _startAddress = "32";
        private string _maskValue = "";
        private string _calculatedMaskLength = "0";

        // 清除过滤相关属性
        private string _selectedClearMaskId = "Mask ALL";

        // 掩码列表
        private ObservableCollection<TagMaskInfo> _maskList = new();

        public TagFilterDialogViewModel(ReaderManager readerManager, Action<string>? addLogAction = null)
        {
            try
            {
                addLogAction?.Invoke("🔧 TagFilterDialogViewModel: 开始初始化...");

                _readerManager = readerManager ?? throw new ArgumentNullException(nameof(readerManager));
                _addLogAction = addLogAction;

                addLogAction?.Invoke("🔧 TagFilterDialogViewModel: 初始化选项...");
                // 初始化选项
                InitializeOptions();

                addLogAction?.Invoke("🔧 TagFilterDialogViewModel: 初始化命令...");
                // 初始化命令
                SetFilterCommand = new RelayCommand(ExecuteSetFilter, CanExecuteSetFilter);
                ClearFilterCommand = new RelayCommand(ExecuteClearFilter);
                QueryFilterCommand = new RelayCommand(ExecuteQueryFilter);

                addLogAction?.Invoke("✅ TagFilterDialogViewModel: 初始化完成");
            }
            catch (Exception ex)
            {
                addLogAction?.Invoke($"❌ TagFilterDialogViewModel初始化失败: {ex.Message}");
                addLogAction?.Invoke($"❌ 异常详情: {ex}");
                throw; // 重新抛出异常
            }
        }

        #region 属性

        public ObservableCollection<string> MaskIdOptions { get; private set; } = new();
        public ObservableCollection<string> SessionIdOptions { get; private set; } = new();
        public ObservableCollection<string> ActionOptions { get; private set; } = new();
        public ObservableCollection<string> MemBankOptions { get; private set; } = new();
        public ObservableCollection<string> ClearMaskIdOptions { get; private set; } = new();

        public string SelectedMaskId
        {
            get => _selectedMaskId;
            set { _selectedMaskId = value; OnPropertyChanged(); }
        }

        public string SelectedSessionId
        {
            get => _selectedSessionId;
            set { _selectedSessionId = value; OnPropertyChanged(); }
        }

        public string SelectedAction
        {
            get => _selectedAction;
            set { _selectedAction = value; OnPropertyChanged(); }
        }

        public string SelectedMemBank
        {
            get => _selectedMemBank;
            set { _selectedMemBank = value; OnPropertyChanged(); }
        }

        public string StartAddress
        {
            get => _startAddress;
            set { _startAddress = value; OnPropertyChanged(); }
        }

        public string MaskValue
        {
            get => _maskValue;
            set
            {
                if (ValidateHexInput(value))
                {
                    _maskValue = value.ToUpper();
                    OnPropertyChanged();
                    CalculatedMaskLength = CalculateMaskLength(value);
                    OnPropertyChanged(nameof(CalculatedMaskLength));
                }
            }
        }

        public string CalculatedMaskLength
        {
            get => _calculatedMaskLength;
            private set { _calculatedMaskLength = value; OnPropertyChanged(); }
        }

        public string SelectedClearMaskId
        {
            get => _selectedClearMaskId;
            set { _selectedClearMaskId = value; OnPropertyChanged(); }
        }

        public ObservableCollection<TagMaskInfo> MaskList
        {
            get => _maskList;
            set { _maskList = value; OnPropertyChanged(); }
        }

        #endregion

        #region 命令

        public ICommand SetFilterCommand { get; }
        public ICommand ClearFilterCommand { get; }
        public ICommand QueryFilterCommand { get; }

        #endregion

        #region 私有方法

        private void InitializeOptions()
        {
            try
            {
                _addLogAction?.Invoke("🔧 初始化MaskIdOptions...");
                // 掩码ID选项
                MaskIdOptions.Clear();
                for (int i = 1; i <= 5; i++)
                {
                    MaskIdOptions.Add($"Mask No.{i}");
                }

                _addLogAction?.Invoke("🔧 初始化SessionIdOptions...");
                // Session ID选项
                SessionIdOptions.Clear();
                SessionIdOptions.Add("S0");
                SessionIdOptions.Add("S1");
                SessionIdOptions.Add("S2");
                SessionIdOptions.Add("S3");
                SessionIdOptions.Add("SL");

                _addLogAction?.Invoke("🔧 初始化ActionOptions...");
                // 过滤行为选项
                ActionOptions.Clear();
                for (int i = 0; i <= 7; i++)
                {
                    ActionOptions.Add($"{i:D2}");
                }

                _addLogAction?.Invoke("🔧 初始化MemBankOptions...");
                // 存储区选项
                MemBankOptions.Clear();
                MemBankOptions.Add("保留区");
                MemBankOptions.Add("EPC");
                MemBankOptions.Add("TID");
                MemBankOptions.Add("USER");

                _addLogAction?.Invoke("🔧 初始化ClearMaskIdOptions...");
                // 清除掩码ID选项
                ClearMaskIdOptions.Clear();
                ClearMaskIdOptions.Add("Mask ALL");
                for (int i = 1; i <= 5; i++)
                {
                    ClearMaskIdOptions.Add($"Mask No.{i}");
                }

                _addLogAction?.Invoke("✅ 所有选项初始化完成");
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ InitializeOptions异常: {ex.Message}");
                throw;
            }
        }

        private bool ValidateHexInput(string input)
        {
            // 允许十六进制字符和空格
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[0-9A-Fa-f\s]*$");
        }

        private string CalculateMaskLength(string hexValue)
        {
            if (string.IsNullOrWhiteSpace(hexValue))
                return "0";

            // 移除空格和非十六进制字符
            string cleanHex = System.Text.RegularExpressions.Regex.Replace(
                hexValue.ToUpper(), @"[^0-9A-F]", "");

            // 每个十六进制字符代表4位
            int bitLength = cleanHex.Length * 4;
            return bitLength.ToString();
        }

        private TagMask CreateTagMaskFromInput()
        {
            var mask = new TagMask();

            // 解析掩码ID
            if (SelectedMaskId.StartsWith("Mask No."))
            {
                mask.MaskId = int.Parse(SelectedMaskId.Substring(8));
            }

            // 解析Target
            mask.Target = SelectedSessionId switch
            {
                "S0" => MaskTarget.InventoriedS0,
                "S1" => MaskTarget.InventoriedS1,
                "S2" => MaskTarget.InventoriedS2,
                "S3" => MaskTarget.InventoriedS3,
                "SL" => MaskTarget.SL,
                _ => MaskTarget.InventoriedS0
            };

            // 解析Action
            if (byte.TryParse(SelectedAction, out byte actionValue))
            {
                mask.Action = (MaskAction)actionValue;
            }

            // 解析MemBank
            mask.MemBank = SelectedMemBank switch
            {
                "保留区" => MemoryBank.Reserved,
                "EPC" => MemoryBank.EPC,
                "TID" => MemoryBank.TID,
                "USER" => MemoryBank.USER,
                _ => MemoryBank.EPC
            };

            // 解析起始地址
            if (uint.TryParse(StartAddress, out uint startAddr))
            {
                mask.StartingAddress = startAddr;
            }

            // 设置掩码值
            mask.MaskValueHex = MaskValue;

            return mask;
        }

        private void AddLog(string message)
        {
            _addLogAction?.Invoke(message);
        }

        #endregion

        #region 命令实现

        private bool CanExecuteSetFilter()
        {
            return !string.IsNullOrWhiteSpace(MaskValue) && 
                   !string.IsNullOrWhiteSpace(StartAddress);
        }

        private void ExecuteSetFilter()
        {
            try
            {
                var mask = CreateTagMaskFromInput();
                var response = _readerManager.SetTagMask(mask);

                if (response != null)
                {
                    AddLog("✅ 标签掩码设置成功");
                    // 自动刷新列表
                    ExecuteQueryFilter();
                }
                else
                {
                    AddLog("❌ 标签掩码设置失败");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 设置标签掩码异常: {ex.Message}");
            }
        }

        private void ExecuteClearFilter()
        {
            try
            {
                int maskId = 0;
                if (SelectedClearMaskId.StartsWith("Mask No."))
                {
                    maskId = int.Parse(SelectedClearMaskId.Substring(8));
                }

                var response = _readerManager.ClearTagMask(maskId);

                if (response != null)
                {
                    AddLog("✅ 标签掩码清除成功");
                    // 自动刷新列表
                    ExecuteQueryFilter();
                }
                else
                {
                    AddLog("❌ 标签掩码清除失败");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 清除标签掩码异常: {ex.Message}");
            }
        }

        private void ExecuteQueryFilter()
        {
            try
            {
                var masks = _readerManager.QueryTagMasks();

                if (masks != null)
                {
                    MaskList.Clear();
                    foreach (var mask in masks)
                    {
                        MaskList.Add(TagMaskInfo.FromTagMask(mask));
                    }

                    AddLog($"✅ 查询标签掩码成功，共 {masks.Count} 个");
                }
                else
                {
                    AddLog("❌ 查询标签掩码失败");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 查询标签掩码异常: {ex.Message}");
            }
        }

        #endregion

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
