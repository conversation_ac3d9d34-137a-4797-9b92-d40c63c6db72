# UHF RFID读写器通讯协议摘要 V3.8

## 基本通讯参数
- 波特率：115200
- 数据位：8
- 停止位：1
- 校验位：无

## 数据包格式
```
Head | Len | Address | Cmd | Data | Checksum
0xA0 | 1字节 | 1字节 | 1字节 | N字节 | 1字节
```

## 校验和计算
- 算法：对Head+Len+Address+Cmd+Data求和，然后取反加1

## 命令码列表

### 系统设置指令
- 0x70: 复位读写器
- 0x71: 设置串口通讯波特率
- 0x72: 获取固件版本
- 0x73: 设置读写器地址 ✅ 已实现
- 0x74: 设置读写器工作天线
- 0x75: 查询当前工作天线
- 0x76: 设置读写器射频输出功率
- 0x77: 查询读写器当前输出功率
- 0x78: 设置读写器工作频率范围
- 0x79: 查询读写器工作频率范围
- 0x7A: 设置蜂鸣器状态 ✅ 已实现
- 0x7B: 查询当前设备的工作温度
- 0x60: 读取GPIO电平
- 0x61: 设置GPIO电平
- 0x62: 设置天线连接检测器状态
- 0x63: 读取天线连接检测器状态
- 0x66: 设置读写器临时射频输出功率
- 0x67: 设置读写器识别码
- 0x68: 读取读写器识别码
- 0x69: 设置射频链路的通讯速率
- 0x6A: 读取射频链路的通讯速率
- 0x7E: 测量天线端口的回波损耗

### 18000-6C标签操作命令
- 0x80: 盘存标签（缓存模式）
- 0x81: 读标签
- 0x82: 写标签
- 0x83: 锁定标签
- 0x84: 灭活标签
- 0x85: 设置ACCESS操作的EPC匹配
- 0x86: 查询ACCESS操作的EPC匹配状态
- 0x89: 实时盘存标签
- 0x8A: 快速轮询多个天线盘存标签
- 0x8B: 自定义session和target盘存 ✅ 已实现
- 0x8C: 设置Monza标签快速读TID（临时）
- 0x8D: 设置Monza标签快速读TID（保存）
- 0x8E: 查询当前的快速TID设置

### ISO18000-6B标签操作命令
- 0xB0: 盘存18000-6B标签
- 0xB1: 读18000-6B标签
- 0xB2: 写18000-6B标签
- 0xB3: 锁定18000-6B标签
- 0xB4: 查询18000-6B标签锁定状态

### 缓存操作命令
- 0x90: 提取标签数据保留缓存备份
- 0x91: 提取标签数据并删除缓存
- 0x92: 查询缓存中已读标签个数
- 0x93: 清空标签数据缓存

## 响应格式
请添加各命令的响应数据格式说明

## 错误码定义

### 常用错误码
- 0x10: 命令成功 (CommandSuccess)
- 0x11: 命令失败
- 0x80: 无标签响应
- 0x81: 标签返回错误
- 0x82: 命令长度错误
- 0x83: 非法命令
- 0x84: 参数错误
- 0x85: 天线未连接
- 0x86: 频率设置错误
- 0x87: 功率设置错误
- 0xEE: 操作失败

---
注：请将PDF中的具体协议内容填入此文件
