using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace RFID_UI.Models
{
    /// <summary>
    /// 标签掩码模型
    /// </summary>
    public class TagMask : INotifyPropertyChanged
    {
        private int _maskId = 1;
        private MaskTarget _target = MaskTarget.InventoriedS0;
        private MaskAction _action = MaskAction.AssertSL_DeassertSL;
        private MemoryBank _memBank = MemoryBank.EPC;
        private uint _startingAddress = 32;
        private int _maskBitLength = 0;
        private byte[] _maskData = Array.Empty<byte>();
        private bool _truncate = false;
        private string _description = "";

        /// <summary>
        /// 掩码ID (1-5)
        /// </summary>
        public int MaskId
        {
            get => _maskId;
            set { _maskId = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 目标对象
        /// </summary>
        public MaskTarget Target
        {
            get => _target;
            set { _target = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 操作类型
        /// </summary>
        public MaskAction Action
        {
            get => _action;
            set { _action = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 存储区
        /// </summary>
        public MemoryBank MemBank
        {
            get => _memBank;
            set { _memBank = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 起始地址
        /// </summary>
        public uint StartingAddress
        {
            get => _startingAddress;
            set { _startingAddress = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 掩码位长度
        /// </summary>
        public int MaskBitLength
        {
            get => _maskBitLength;
            set { _maskBitLength = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 掩码数据
        /// </summary>
        public byte[] MaskData
        {
            get => _maskData;
            set { _maskData = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// Truncate标志
        /// </summary>
        public bool Truncate
        {
            get => _truncate;
            set { _truncate = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 掩码描述
        /// </summary>
        public string Description
        {
            get => _description;
            set { _description = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 掩码值的十六进制字符串表示
        /// </summary>
        public string MaskValueHex
        {
            get => BitConverter.ToString(MaskData).Replace("-", "");
            set
            {
                if (!string.IsNullOrWhiteSpace(value))
                {
                    try
                    {
                        // 移除空格和非十六进制字符
                        string cleanHex = System.Text.RegularExpressions.Regex.Replace(
                            value.ToUpper(), @"[^0-9A-F]", "");

                        // 确保偶数长度
                        if (cleanHex.Length % 2 != 0)
                            cleanHex = "0" + cleanHex;

                        // 转换为字节数组
                        byte[] bytes = new byte[cleanHex.Length / 2];
                        for (int i = 0; i < bytes.Length; i++)
                        {
                            bytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
                        }

                        MaskData = bytes;
                        MaskBitLength = cleanHex.Length * 4; // 每个十六进制字符4位
                    }
                    catch
                    {
                        // 忽略无效输入
                    }
                }
                else
                {
                    MaskData = Array.Empty<byte>();
                    MaskBitLength = 0;
                }
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 掩码目标对象枚举
    /// </summary>
    public enum MaskTarget : byte
    {
        [Description("S0")]
        InventoriedS0 = 0x00,
        
        [Description("S1")]
        InventoriedS1 = 0x01,
        
        [Description("S2")]
        InventoriedS2 = 0x02,
        
        [Description("S3")]
        InventoriedS3 = 0x03,
        
        [Description("SL")]
        SL = 0x04
    }

    /// <summary>
    /// 掩码操作类型枚举
    /// </summary>
    public enum MaskAction : byte
    {
        [Description("00 - 匹配时Assert SL/A，不匹配时Deassert SL/B")]
        AssertSL_DeassertSL = 0x00,
        
        [Description("01 - 匹配时Assert SL/A，不匹配时无操作")]
        AssertSL_DoNothing = 0x01,
        
        [Description("02 - 匹配时无操作，不匹配时Deassert SL/B")]
        DoNothing_DeassertSL = 0x02,
        
        [Description("03 - 匹配时Negate SL/翻转，不匹配时无操作")]
        NegateSL_DoNothing = 0x03,
        
        [Description("04 - 匹配时Deassert SL/B，不匹配时Assert SL/A")]
        DeassertSL_AssertSL = 0x04,
        
        [Description("05 - 匹配时Deassert SL/B，不匹配时无操作")]
        DeassertSL_DoNothing = 0x05,
        
        [Description("06 - 匹配时无操作，不匹配时Assert SL/A")]
        DoNothing_AssertSL = 0x06,
        
        [Description("07 - 匹配时无操作，不匹配时Negate SL/翻转")]
        DoNothing_NegateSL = 0x07
    }

    /// <summary>
    /// 存储区枚举
    /// </summary>
    public enum MemoryBank : byte
    {
        [Description("保留区")]
        Reserved = 0x00,
        
        [Description("EPC")]
        EPC = 0x01,
        
        [Description("TID")]
        TID = 0x02,
        
        [Description("USER")]
        USER = 0x03
    }
}
