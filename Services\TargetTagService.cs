using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using RFID_UI.Models;

namespace RFID_UI.Services
{
    /// <summary>
    /// 目标标签管理服务
    /// 提供全局的目标标签列表管理功能
    /// </summary>
    public class TargetTagService : INotifyPropertyChanged
    {
        private static TargetTagService? _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static TargetTagService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new TargetTagService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 目标标签列表
        /// </summary>
        public ObservableCollection<TargetTag> TargetTags { get; }

        /// <summary>
        /// 目标标签数量
        /// </summary>
        public int TargetTagCount => TargetTags.Count;

        /// <summary>
        /// 已匹配标签数量
        /// </summary>
        public int MatchedTagCount => TargetTags.Count(t => t.IsMatched);

        /// <summary>
        /// 未匹配标签数量
        /// </summary>
        public int UnmatchedTagCount => TargetTags.Count(t => !t.IsMatched);

        private TargetTagService()
        {
            TargetTags = new ObservableCollection<TargetTag>();
            TargetTags.CollectionChanged += (s, e) => OnPropertyChanged(nameof(TargetTagCount));
        }

        /// <summary>
        /// 添加目标标签
        /// </summary>
        /// <param name="epc">EPC号</param>
        /// <returns>是否添加成功（如果已存在则返回false）</returns>
        public bool AddTargetTag(string epc)
        {
            if (string.IsNullOrWhiteSpace(epc))
                return false;

            // 检查是否已存在
            if (TargetTags.Any(t => t.Epc.Equals(epc, StringComparison.OrdinalIgnoreCase)))
                return false;

            var targetTag = new TargetTag
            {
                Index = TargetTags.Count + 1,
                Epc = epc.ToUpper(),
                IsMatched = false,
                FirstFoundTime = null,
                ReadCount = 0
            };

            TargetTags.Add(targetTag);
            OnPropertyChanged(nameof(MatchedTagCount));
            OnPropertyChanged(nameof(UnmatchedTagCount));
            return true;
        }

        /// <summary>
        /// 批量添加目标标签
        /// </summary>
        /// <param name="epcs">EPC列表</param>
        /// <returns>成功添加的数量</returns>
        public int AddTargetTags(System.Collections.Generic.IEnumerable<string> epcs)
        {
            int addedCount = 0;
            foreach (var epc in epcs)
            {
                if (AddTargetTag(epc))
                    addedCount++;
            }
            return addedCount;
        }

        /// <summary>
        /// 从TagData列表添加目标标签
        /// </summary>
        /// <param name="tagDataList">标签数据列表</param>
        /// <returns>成功添加的数量</returns>
        public int AddTargetTagsFromTagData(System.Collections.Generic.IEnumerable<TagData> tagDataList)
        {
            var epcs = tagDataList.Select(t => t.Epc);
            return AddTargetTags(epcs);
        }

        /// <summary>
        /// 移除目标标签
        /// </summary>
        /// <param name="epc">EPC号</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveTargetTag(string epc)
        {
            var targetTag = TargetTags.FirstOrDefault(t => t.Epc.Equals(epc, StringComparison.OrdinalIgnoreCase));
            if (targetTag != null)
            {
                TargetTags.Remove(targetTag);
                UpdateIndices();
                OnPropertyChanged(nameof(MatchedTagCount));
                OnPropertyChanged(nameof(UnmatchedTagCount));
                return true;
            }
            return false;
        }

        /// <summary>
        /// 清空所有目标标签
        /// </summary>
        public void ClearTargetTags()
        {
            TargetTags.Clear();
            OnPropertyChanged(nameof(MatchedTagCount));
            OnPropertyChanged(nameof(UnmatchedTagCount));
        }

        /// <summary>
        /// 更新标签匹配状态
        /// </summary>
        /// <param name="epc">EPC号</param>
        /// <param name="isMatched">是否匹配</param>
        /// <param name="firstFoundTime">首次发现时间</param>
        /// <param name="readCount">读取次数</param>
        public void UpdateTagMatchStatus(string epc, bool isMatched, DateTime? firstFoundTime = null, int readCount = 0)
        {
            var targetTag = TargetTags.FirstOrDefault(t => t.Epc.Equals(epc, StringComparison.OrdinalIgnoreCase));
            if (targetTag != null)
            {
                bool wasMatched = targetTag.IsMatched;
                targetTag.IsMatched = isMatched;
                
                if (firstFoundTime.HasValue && targetTag.FirstFoundTime == null)
                {
                    targetTag.FirstFoundTime = firstFoundTime.Value;
                }
                
                if (readCount > targetTag.ReadCount)
                {
                    targetTag.ReadCount = readCount;
                }

                // 如果匹配状态发生变化，更新统计
                if (wasMatched != isMatched)
                {
                    OnPropertyChanged(nameof(MatchedTagCount));
                    OnPropertyChanged(nameof(UnmatchedTagCount));
                }
            }
        }

        /// <summary>
        /// 重置所有标签的匹配状态
        /// </summary>
        public void ResetAllMatchStatus()
        {
            foreach (var tag in TargetTags)
            {
                tag.IsMatched = false;
                tag.FirstFoundTime = null;
                tag.ReadCount = 0;
            }
            OnPropertyChanged(nameof(MatchedTagCount));
            OnPropertyChanged(nameof(UnmatchedTagCount));
        }

        /// <summary>
        /// 获取未匹配的目标标签EPC列表
        /// </summary>
        /// <returns>未匹配的EPC列表</returns>
        public System.Collections.Generic.List<string> GetUnmatchedEpcs()
        {
            return TargetTags.Where(t => !t.IsMatched).Select(t => t.Epc).ToList();
        }

        /// <summary>
        /// 更新序号
        /// </summary>
        private void UpdateIndices()
        {
            for (int i = 0; i < TargetTags.Count; i++)
            {
                TargetTags[i].Index = i + 1;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
