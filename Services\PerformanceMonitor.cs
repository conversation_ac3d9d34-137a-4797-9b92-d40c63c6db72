using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;

namespace RFID_UI.Services
{
    /// <summary>
    /// 性能监控服务
    /// </summary>
    public class PerformanceMonitor
    {
        private static readonly Dictionary<string, Stopwatch> _timers = new Dictionary<string, Stopwatch>();
        private static readonly Dictionary<string, long> _counters = new Dictionary<string, long>();
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 开始计时
        /// </summary>
        public static void StartTimer(string name)
        {
            lock (_lockObject)
            {
                if (_timers.ContainsKey(name))
                {
                    _timers[name].Restart();
                }
                else
                {
                    _timers[name] = Stopwatch.StartNew();
                }
            }
        }

        /// <summary>
        /// 停止计时并返回耗时（毫秒）
        /// </summary>
        public static long StopTimer(string name)
        {
            lock (_lockObject)
            {
                if (_timers.TryGetValue(name, out var timer))
                {
                    timer.Stop();
                    return timer.ElapsedMilliseconds;
                }
                return 0;
            }
        }

        /// <summary>
        /// 获取计时器耗时（毫秒）
        /// </summary>
        public static long GetElapsedTime(string name)
        {
            lock (_lockObject)
            {
                if (_timers.TryGetValue(name, out var timer))
                {
                    return timer.ElapsedMilliseconds;
                }
                return 0;
            }
        }

        /// <summary>
        /// 增加计数器
        /// </summary>
        public static void IncrementCounter(string name, long increment = 1)
        {
            lock (_lockObject)
            {
                if (_counters.ContainsKey(name))
                {
                    _counters[name] += increment;
                }
                else
                {
                    _counters[name] = increment;
                }
            }
        }

        /// <summary>
        /// 获取计数器值
        /// </summary>
        public static long GetCounter(string name)
        {
            lock (_lockObject)
            {
                return _counters.TryGetValue(name, out var count) ? count : 0;
            }
        }

        /// <summary>
        /// 重置计数器
        /// </summary>
        public static void ResetCounter(string name)
        {
            lock (_lockObject)
            {
                _counters[name] = 0;
            }
        }

        /// <summary>
        /// 获取内存使用情况
        /// </summary>
        public static string GetMemoryUsage()
        {
            var process = Process.GetCurrentProcess();
            var workingSet = process.WorkingSet64 / 1024 / 1024; // MB
            var privateMemory = process.PrivateMemorySize64 / 1024 / 1024; // MB
            
            return $"工作集: {workingSet}MB, 私有内存: {privateMemory}MB";
        }

        /// <summary>
        /// 获取性能报告
        /// </summary>
        public static string GetPerformanceReport()
        {
            lock (_lockObject)
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== 性能监控报告 ===");
                report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine();

                report.AppendLine("计时器:");
                foreach (var timer in _timers)
                {
                    report.AppendLine($"  {timer.Key}: {timer.Value.ElapsedMilliseconds}ms");
                }

                report.AppendLine();
                report.AppendLine("计数器:");
                foreach (var counter in _counters)
                {
                    report.AppendLine($"  {counter.Key}: {counter.Value}");
                }

                report.AppendLine();
                report.AppendLine($"内存使用: {GetMemoryUsage()}");

                return report.ToString();
            }
        }

        /// <summary>
        /// 清理所有监控数据
        /// </summary>
        public static void Clear()
        {
            lock (_lockObject)
            {
                _timers.Clear();
                _counters.Clear();
            }
        }
    }
}
