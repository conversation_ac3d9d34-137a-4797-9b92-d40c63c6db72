using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace RFID_UI.Models
{
    /// <summary>
    /// 标签掩码显示信息类，用于UI列表显示
    /// </summary>
    public class TagMaskInfo : INotifyPropertyChanged
    {
        private string _maskId = "";
        private string _sessionId = "";
        private string _action = "";
        private string _memBank = "";
        private string _startAddress = "";
        private string _maskLength = "";
        private string _maskValue = "";

        /// <summary>
        /// 过滤ID
        /// </summary>
        public string MaskId
        {
            get => _maskId;
            set { _maskId = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// Session ID
        /// </summary>
        public string SessionId
        {
            get => _sessionId;
            set { _sessionId = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 过滤行为
        /// </summary>
        public string Action
        {
            get => _action;
            set { _action = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 过滤区域
        /// </summary>
        public string MemBank
        {
            get => _memBank;
            set { _memBank = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 起始地址(bit)
        /// </summary>
        public string StartAddress
        {
            get => _startAddress;
            set { _startAddress = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 过滤长度(bit)
        /// </summary>
        public string MaskLength
        {
            get => _maskLength;
            set { _maskLength = value; OnPropertyChanged(); }
        }

        /// <summary>
        /// 过滤值
        /// </summary>
        public string MaskValue
        {
            get => _maskValue;
            set { _maskValue = value; OnPropertyChanged(); }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 从TagMask创建TagMaskInfo
        /// </summary>
        public static TagMaskInfo FromTagMask(TagMask mask)
        {
            return new TagMaskInfo
            {
                MaskId = $"Mask No.{mask.MaskId}",
                SessionId = GetTargetDescription(mask.Target),
                Action = $"{(byte)mask.Action:D2}",
                MemBank = GetMemBankDescription(mask.MemBank),
                StartAddress = mask.StartingAddress.ToString(),
                MaskLength = mask.MaskBitLength.ToString(),
                MaskValue = mask.MaskValueHex
            };
        }

        private static string GetTargetDescription(MaskTarget target)
        {
            return target switch
            {
                MaskTarget.InventoriedS0 => "S0",
                MaskTarget.InventoriedS1 => "S1",
                MaskTarget.InventoriedS2 => "S2",
                MaskTarget.InventoriedS3 => "S3",
                MaskTarget.SL => "SL",
                _ => "Unknown"
            };
        }

        private static string GetMemBankDescription(MemoryBank memBank)
        {
            return memBank switch
            {
                MemoryBank.Reserved => "保留区",
                MemoryBank.EPC => "EPC",
                MemoryBank.TID => "TID",
                MemoryBank.USER => "USER",
                _ => "Unknown"
            };
        }
    }
}
