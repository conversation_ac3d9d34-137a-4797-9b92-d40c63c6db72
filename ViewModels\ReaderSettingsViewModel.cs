using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows.Media;
using Wpf.Ui.Controls;

namespace RFID_UI.ViewModels
{
    public class ReaderSettingsViewModel : INotifyPropertyChanged
    {
        private readonly ReaderManager _readerManager;
        private readonly Action<string>? _addLogAction;
        private string _selectedPort = "";
        private string _connectionStatus = "状态: 未连接";
        private Brush _connectionStatusColor = Brushes.Red;
        private int _readerAddress = 1;
        private string _selectedAntenna = "天线1";
        private double _powerLevel = 20.0;
        private string _selectedBuzzer = "开启";
        private string _deviceVersion = "未获取";
        private string _detailedConnectionStatus = "设备未连接";
        private InfoBarSeverity _connectionSeverity = InfoBarSeverity.Warning;

        public ObservableCollection<string> AvailablePorts { get; }
        public ObservableCollection<string> AntennaOptions { get; }
        public ObservableCollection<string> BuzzerOptions { get; }

        public string SelectedPort
        {
            get => _selectedPort;
            set { _selectedPort = value; OnPropertyChanged(); }
        }

        public string ConnectionStatus
        {
            get => _connectionStatus;
            set { _connectionStatus = value; OnPropertyChanged(); }
        }

        public Brush ConnectionStatusColor
        {
            get => _connectionStatusColor;
            set { _connectionStatusColor = value; OnPropertyChanged(); }
        }

        public int ReaderAddress
        {
            get => _readerAddress;
            set { _readerAddress = value; OnPropertyChanged(); }
        }

        public string SelectedAntenna
        {
            get => _selectedAntenna;
            set { _selectedAntenna = value; OnPropertyChanged(); }
        }

        public double PowerLevel
        {
            get => _powerLevel;
            set { _powerLevel = value; OnPropertyChanged(); }
        }

        public string SelectedBuzzer
        {
            get => _selectedBuzzer;
            set { _selectedBuzzer = value; OnPropertyChanged(); }
        }

        public string DeviceVersion
        {
            get => _deviceVersion;
            set { _deviceVersion = value; OnPropertyChanged(); }
        }

        public string DetailedConnectionStatus
        {
            get => _detailedConnectionStatus;
            set { _detailedConnectionStatus = value; OnPropertyChanged(); }
        }

        public InfoBarSeverity ConnectionSeverity
        {
            get => _connectionSeverity;
            set { _connectionSeverity = value; OnPropertyChanged(); }
        }

        public string CurrentAddress => _readerAddress.ToString();
        public string CurrentAntenna => _selectedAntenna;
        public string CurrentPower => $"{_powerLevel:F1} dBm";
        public string CurrentBuzzer => _selectedBuzzer;

        // Commands
        public ICommand RefreshPortsCommand { get; }
        public ICommand ConnectCommand { get; }
        public ICommand DisconnectCommand { get; }
        public ICommand SetAddressCommand { get; }
        public ICommand SetAntennaCommand { get; }
        public ICommand SetPowerCommand { get; }
        public ICommand GetPowerCommand { get; }
        public ICommand SetBuzzerCommand { get; }
        public ICommand ResetCommand { get; }
        public ICommand GetVersionCommand { get; }

        public ReaderSettingsViewModel(ReaderManager readerManager, Action<string>? addLogAction = null)
        {
            _readerManager = readerManager;
            _addLogAction = addLogAction;

            AvailablePorts = new ObservableCollection<string>();
            AntennaOptions = new ObservableCollection<string> { "天线1", "天线2", "天线3", "天线4", "天线5", "天线6", "天线7", "天线8" };
            BuzzerOptions = new ObservableCollection<string> { "开启", "关闭" };

            RefreshPortsCommand = new RelayCommand(RefreshPorts);
            ConnectCommand = new RelayCommand(Connect);
            DisconnectCommand = new RelayCommand(Disconnect);
            SetAddressCommand = new RelayCommand(SetAddress);
            SetAntennaCommand = new RelayCommand(SetAntenna);
            SetPowerCommand = new RelayCommand(SetPower);
            GetPowerCommand = new RelayCommand(GetPower);
            SetBuzzerCommand = new RelayCommand(SetBuzzer);
            ResetCommand = new RelayCommand(Reset);
            GetVersionCommand = new RelayCommand(GetVersion);

            RefreshPorts();
        }

        private void RefreshPorts()
        {
            AvailablePorts.Clear();
            var ports = System.IO.Ports.SerialPort.GetPortNames();
            foreach (var port in ports)
            {
                AvailablePorts.Add(port);
            }
            
            if (AvailablePorts.Count > 0 && string.IsNullOrEmpty(SelectedPort))
            {
                SelectedPort = AvailablePorts[0];
            }
        }

        private void Connect()
        {
            // 验证串口选择
            if (!ValidatePortSelection(SelectedPort, out string portError))
            {
                UpdateConnectionStatus(portError, false);
                AddLog($"❌ 连接失败: {portError}");
                return;
            }

            try
            {
                // 更新ReaderManager的串口配置
                _readerManager.UpdatePortConfiguration(SelectedPort, 115200);

                // 尝试连接
                bool connected = _readerManager.Connect();

                if (connected)
                {
                    UpdateConnectionStatus("连接成功", true);
                    GetVersion(); // 自动获取版本信息

                    // 添加连接成功日志
                    AddLog($"🔌 成功连接到 {SelectedPort}");
                }
                else
                {
                    UpdateConnectionStatus("连接失败", false);
                    AddLog($"❌ 连接 {SelectedPort} 失败");
                }
            }
            catch (System.Exception ex)
            {
                UpdateConnectionStatus($"连接错误: {ex.Message}", false);
                AddLog($"❌ 连接异常: {ex.Message}");
            }
        }

        private void Disconnect()
        {
            try
            {
                _readerManager.Disconnect();
                UpdateConnectionStatus("已断开连接", false);
                DeviceVersion = "未获取";
                AddLog("🔌 设备连接已断开");
            }
            catch (System.Exception ex)
            {
                UpdateConnectionStatus($"断开连接错误: {ex.Message}", false);
                AddLog($"❌ 断开连接异常: {ex.Message}");
            }
        }

        private void UpdateConnectionStatus(string message, bool isConnected)
        {
            ConnectionStatus = $"状态: {message}";
            ConnectionStatusColor = isConnected ? Brushes.Green : Brushes.Red;
            DetailedConnectionStatus = message;
            ConnectionSeverity = isConnected ? InfoBarSeverity.Success : InfoBarSeverity.Error;
            
            // 通知相关属性更新
            OnPropertyChanged(nameof(CurrentAddress));
            OnPropertyChanged(nameof(CurrentAntenna));
            OnPropertyChanged(nameof(CurrentPower));
            OnPropertyChanged(nameof(CurrentBuzzer));
        }

        private void SetAddress()
        {
            if (!_readerManager.IsConnected())
            {
                UpdateConnectionStatus("设备未连接", false);
                AddLog("❌ 设置地址失败：设备未连接");
                return;
            }

            try
            {
                // 验证地址有效性
                if (!ValidateReaderAddress(_readerAddress, out string addressError))
                {
                    AddLog($"❌ 地址设置失败：{addressError}");
                    return;
                }

                byte[]? response = _readerManager.SetReaderAddress((byte)_readerAddress);
                var responseInfo = _readerManager.AnalyzeResponse(response);

                if (responseInfo.IsSuccess)
                {
                    AddLog($"✅ 读写器地址已设置为: {_readerAddress}");
                    OnPropertyChanged(nameof(CurrentAddress));
                }
                else
                {
                    AddLog($"❌ 设置地址失败: {responseInfo.ErrorMessage}");
                }
            }
            catch (System.Exception ex)
            {
                AddLog($"❌ 设置地址异常: {ex.Message}");
            }
        }

        private void SetAntenna()
        {
            if (!_readerManager.IsConnected())
            {
                UpdateConnectionStatus("设备未连接", false);
                AddLog("❌ 设置天线失败：设备未连接");
                return;
            }

            try
            {
                // 验证天线选择
                if (!ValidateAntennaSelection(_selectedAntenna, out string antennaError))
                {
                    AddLog($"❌ 天线设置失败：{antennaError}");
                    return;
                }

                // 解析天线号 (天线1 -> 0, 天线2 -> 1, ...)
                byte antennaId = (byte)(AntennaOptions.IndexOf(_selectedAntenna));

                byte[]? response = _readerManager.SetWorkAntenna(antennaId);
                var responseInfo = _readerManager.AnalyzeResponse(response);

                if (responseInfo.IsSuccess)
                {
                    AddLog($"✅ 工作天线已设置为: {_selectedAntenna}");
                    OnPropertyChanged(nameof(CurrentAntenna));
                }
                else
                {
                    AddLog($"❌ 设置天线失败: {responseInfo.ErrorMessage}");
                }
            }
            catch (System.Exception ex)
            {
                AddLog($"❌ 设置天线异常: {ex.Message}");
            }
        }

        private void SetPower()
        {
            if (!_readerManager.IsConnected())
            {
                UpdateConnectionStatus("设备未连接", false);
                AddLog("❌ 设置功率失败：设备未连接");
                return;
            }

            try
            {
                // 验证功率有效性
                if (!ValidatePowerLevel(_powerLevel, out string powerError))
                {
                    AddLog($"❌ 功率设置失败：{powerError}");
                    return;
                }

                // 将dBm转换为设备内部功率值 (通常是 0-255 或特定映射)
                byte powerValue = (byte)Math.Round(_powerLevel);

                AddLog($"🔧 正在设置所有天线功率为: {_powerLevel:F1} dBm");
                byte[]? response = _readerManager.SetOutputPower(powerValue);
                var responseInfo = _readerManager.AnalyzeResponse(response);

                if (responseInfo.IsSuccess)
                {
                    AddLog($"✅ 所有天线输出功率已统一设置为: {_powerLevel:F1} dBm");
                    OnPropertyChanged(nameof(CurrentPower));
                }
                else
                {
                    AddLog($"❌ 设置功率失败: {responseInfo.ErrorMessage}");
                }
            }
            catch (System.Exception ex)
            {
                AddLog($"❌ 设置功率异常: {ex.Message}");
            }
        }

        private void GetPower()
        {
            if (!_readerManager.IsConnected())
            {
                UpdateConnectionStatus("设备未连接", false);
                AddLog("❌ 读取功率失败：设备未连接");
                return;
            }

            try
            {
                AddLog("📋 正在读取当前输出功率...");

                byte[]? response = _readerManager.GetOutputPower();
                var responseInfo = _readerManager.AnalyzeResponse(response);

                if (responseInfo.IsSuccess && response != null && response.Length > 5)
                {
                    // 解析功率值 (跳过头部，提取功率数据)
                    byte powerValue = response[4]; // Head+Len+Address+Cmd之后的第一个字节

                    // 将设备功率值转换为dBm (根据设备规格调整)
                    double powerDbm = powerValue; // 简单映射，可能需要根据实际设备调整

                    PowerLevel = powerDbm;
                    AddLog($"✅ 当前输出功率: {powerDbm:F1} dBm (原始值: {powerValue})");
                    OnPropertyChanged(nameof(CurrentPower));
                }
                else
                {
                    AddLog($"❌ 读取功率失败: {responseInfo.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 读取功率异常: {ex.Message}");
            }
        }

        private void SetBuzzer()
        {
            if (!_readerManager.IsConnected())
            {
                UpdateConnectionStatus("设备未连接", false);
                AddLog("❌ 设置蜂鸣器失败：设备未连接");
                return;
            }

            try
            {
                // 将选择转换为设备值 (开启=1, 关闭=0)
                byte buzzerMode = _selectedBuzzer == "开启" ? (byte)1 : (byte)0;

                byte[]? response = _readerManager.SetBeeperMode(buzzerMode);
                var responseInfo = _readerManager.AnalyzeResponse(response);

                if (responseInfo.IsSuccess)
                {
                    AddLog($"✅ 蜂鸣器已设置为: {_selectedBuzzer}");
                    OnPropertyChanged(nameof(CurrentBuzzer));
                }
                else
                {
                    AddLog($"❌ 设置蜂鸣器失败: {responseInfo.ErrorMessage}");
                }
            }
            catch (System.Exception ex)
            {
                AddLog($"❌ 设置蜂鸣器异常: {ex.Message}");
            }
        }

        private void Reset()
        {
            if (!_readerManager.IsConnected())
            {
                UpdateConnectionStatus("设备未连接", false);
                AddLog("❌ 复位失败：设备未连接");
                return;
            }

            try
            {
                AddLog("⚠️ 正在复位读写器...");

                byte[]? response = _readerManager.ResetReader();
                var responseInfo = _readerManager.AnalyzeResponse(response);

                if (responseInfo.IsSuccess)
                {
                    AddLog("✅ 读写器复位成功");
                    // 复位后设备会重启，连接状态可能改变
                    UpdateConnectionStatus("设备已复位", false);
                    DeviceVersion = "未获取";
                }
                else
                {
                    AddLog($"❌ 复位失败: {responseInfo.ErrorMessage}");
                }
            }
            catch (System.Exception ex)
            {
                AddLog($"❌ 复位异常: {ex.Message}");
            }
        }

        private void GetVersion()
        {
            if (!_readerManager.IsConnected())
            {
                DeviceVersion = "设备未连接";
                AddLog("❌ 获取版本失败：设备未连接");
                return;
            }

            try
            {
                AddLog("📋 正在获取固件版本...");

                byte[]? response = _readerManager.GetFirmwareVersion();
                var responseInfo = _readerManager.AnalyzeResponse(response);

                if (responseInfo.IsSuccess && response != null && response.Length > 5)
                {
                    // 解析版本信息 (跳过头部，提取版本数据)
                    var versionData = new byte[response.Length - 5]; // 去掉Head+Len+Address+Cmd+Check
                    Array.Copy(response, 4, versionData, 0, versionData.Length);

                    string versionString = System.Text.Encoding.ASCII.GetString(versionData).Trim('\0');
                    DeviceVersion = string.IsNullOrEmpty(versionString) ? "UHF RFID Reader v3.8" : versionString;

                    AddLog($"✅ 固件版本: {DeviceVersion}");
                }
                else
                {
                    DeviceVersion = "获取失败";
                    AddLog($"❌ 获取版本失败: {responseInfo.ErrorMessage}");
                }
            }
            catch (System.Exception ex)
            {
                DeviceVersion = $"错误: {ex.Message}";
                AddLog($"❌ 获取版本异常: {ex.Message}");
            }
        }

        private void AddLog(string message)
        {
            _addLogAction?.Invoke(message);
        }

        /// <summary>
        /// 验证读写器地址是否有效
        /// </summary>
        private bool ValidateReaderAddress(int address, out string errorMessage)
        {
            errorMessage = "";

            if (address < 0 || address > 254)
            {
                errorMessage = $"读写器地址 {address} 超出有效范围 (0-254)";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证功率值是否有效
        /// </summary>
        private bool ValidatePowerLevel(double power, out string errorMessage)
        {
            errorMessage = "";

            if (power < 0 || power > 30)
            {
                errorMessage = $"功率值 {power:F1} dBm 超出有效范围 (0-30 dBm)";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证天线选择是否有效
        /// </summary>
        private bool ValidateAntennaSelection(string antenna, out string errorMessage)
        {
            errorMessage = "";

            if (string.IsNullOrEmpty(antenna) || !AntennaOptions.Contains(antenna))
            {
                errorMessage = $"无效的天线选择: {antenna}";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证串口选择是否有效
        /// </summary>
        private bool ValidatePortSelection(string port, out string errorMessage)
        {
            errorMessage = "";

            if (string.IsNullOrEmpty(port))
            {
                errorMessage = "请选择串口";
                return false;
            }

            if (!AvailablePorts.Contains(port))
            {
                errorMessage = $"串口 {port} 不可用，请刷新端口列表";
                return false;
            }

            return true;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
