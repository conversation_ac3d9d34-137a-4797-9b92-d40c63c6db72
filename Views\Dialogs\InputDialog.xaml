<Window x:Class="RFID_UI.Views.Dialogs.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        Title="输入对话框" 
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和说明 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,15">
            <ui:TextBlock x:Name="TitleTextBlock" Text="请输入内容" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
            <ui:TextBlock x:Name="MessageTextBlock" Text="请在下方输入框中输入内容：" FontSize="12" TextWrapping="Wrap"/>
        </StackPanel>

        <!-- 输入区域 -->
        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" Margin="0,0,0,15">
            <TextBox x:Name="InputTextBox" 
                     AcceptsReturn="True" 
                     AcceptsTab="True"
                     VerticalScrollBarVisibility="Auto"
                     HorizontalScrollBarVisibility="Auto"
                     TextWrapping="Wrap"
                     FontFamily="Consolas"
                     FontSize="12"
                     Padding="8"/>
        </Border>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right">
            <ui:Button x:Name="OkButton" Content="确定" 
                      Click="OkButton_Click"
                      IsDefault="True"
                      MinWidth="80" 
                      Margin="0,0,10,0"/>
            <ui:Button x:Name="CancelButton" Content="取消" 
                      Click="CancelButton_Click"
                      IsCancel="True"
                      MinWidth="80"/>
        </StackPanel>
    </Grid>
</Window>
