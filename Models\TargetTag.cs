using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Media;

namespace RFID_UI.Models
{
    /// <summary>
    /// 目标标签数据模型
    /// </summary>
    public class TargetTag : INotifyPropertyChanged
    {
        private int _index;
        private string _epc = string.Empty;
        private bool _isMatched = false;
        private DateTime? _firstFoundTime;
        private int _readCount = 0;

        /// <summary>
        /// 序号
        /// </summary>
        public int Index
        {
            get => _index;
            set
            {
                _index = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// EPC号
        /// </summary>
        public string Epc
        {
            get => _epc;
            set
            {
                _epc = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 是否已匹配
        /// </summary>
        public bool IsMatched
        {
            get => _isMatched;
            set
            {
                _isMatched = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MatchStatusText));
                OnPropertyChanged(nameof(MatchStatusColor));
            }
        }

        /// <summary>
        /// 首次发现时间
        /// </summary>
        public DateTime? FirstFoundTime
        {
            get => _firstFoundTime;
            set
            {
                _firstFoundTime = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(FirstFoundTimeText));
            }
        }

        /// <summary>
        /// 读取次数
        /// </summary>
        public int ReadCount
        {
            get => _readCount;
            set
            {
                _readCount = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 匹配状态文本
        /// </summary>
        public string MatchStatusText
        {
            get
            {
                if (!IsMatched)
                    return "未匹配";
                else
                    return "已匹配";
            }
        }

        /// <summary>
        /// 匹配状态颜色
        /// </summary>
        public Brush MatchStatusColor
        {
            get
            {
                if (!IsMatched)
                    return Brushes.Red;
                else
                    return Brushes.Green;
            }
        }

        /// <summary>
        /// 首次发现时间文本
        /// </summary>
        public string FirstFoundTimeText
        {
            get
            {
                if (FirstFoundTime.HasValue)
                    return FirstFoundTime.Value.ToString("HH:mm:ss.fff");
                else
                    return "-";
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
