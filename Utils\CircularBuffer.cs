using System;

namespace RFID_UI.Utils
{
    /// <summary>
    /// 高性能环形缓冲区，用于串口数据接收
    /// </summary>
    public class CircularBuffer
    {
        private readonly byte[] _buffer;
        private readonly int _capacity;
        private int _head = 0;
        private int _tail = 0;
        private int _count = 0;
        private readonly object _lock = new object();

        public CircularBuffer(int capacity = 8192)
        {
            _capacity = capacity;
            _buffer = new byte[capacity];
        }

        public int Count
        {
            get
            {
                lock (_lock)
                {
                    return _count;
                }
            }
        }

        public int Available => _capacity - Count;

        /// <summary>
        /// 写入数据到缓冲区
        /// </summary>
        public int Write(byte[] data, int offset, int count)
        {
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (offset < 0 || count < 0 || offset + count > data.Length)
                throw new ArgumentException("Invalid offset or count");

            lock (_lock)
            {
                int actualCount = Math.Min(count, Available);
                
                for (int i = 0; i < actualCount; i++)
                {
                    _buffer[_tail] = data[offset + i];
                    _tail = (_tail + 1) % _capacity;
                    _count++;
                }

                return actualCount;
            }
        }

        /// <summary>
        /// 从缓冲区读取数据
        /// </summary>
        public int Read(byte[] data, int offset, int count)
        {
            if (data == null) throw new ArgumentNullException(nameof(data));
            if (offset < 0 || count < 0 || offset + count > data.Length)
                throw new ArgumentException("Invalid offset or count");

            lock (_lock)
            {
                int actualCount = Math.Min(count, _count);
                
                for (int i = 0; i < actualCount; i++)
                {
                    data[offset + i] = _buffer[_head];
                    _head = (_head + 1) % _capacity;
                    _count--;
                }

                return actualCount;
            }
        }

        /// <summary>
        /// 查看指定位置的字节（不移除）
        /// </summary>
        public byte Peek(int index)
        {
            lock (_lock)
            {
                if (index >= _count)
                    throw new ArgumentOutOfRangeException(nameof(index));
                
                int actualIndex = (_head + index) % _capacity;
                return _buffer[actualIndex];
            }
        }

        /// <summary>
        /// 丢弃指定数量的字节
        /// </summary>
        public void Discard(int count)
        {
            lock (_lock)
            {
                int actualCount = Math.Min(count, _count);
                _head = (_head + actualCount) % _capacity;
                _count -= actualCount;
            }
        }

        /// <summary>
        /// 查找数据包边界
        /// </summary>
        public int FindPacket(out int packetLength)
        {
            lock (_lock)
            {
                packetLength = 0;
                
                // 查找头字节 0xA0
                for (int i = 0; i < _count; i++)
                {
                    if (Peek(i) == 0xA0)
                    {
                        // 检查是否有长度字节
                        if (i + 1 < _count)
                        {
                            byte len = Peek(i + 1);
                            int totalLength = 1 + 1 + len; // Head + Len + Data
                            
                            // 检查是否有完整数据包
                            if (i + totalLength <= _count)
                            {
                                packetLength = totalLength;
                                return i; // 返回数据包起始位置
                            }
                        }
                        // 数据包不完整，返回-1等待更多数据
                        return -1;
                    }
                }
                
                // 没有找到头字节
                return -1;
            }
        }

        /// <summary>
        /// 提取完整数据包
        /// </summary>
        public byte[]? ExtractPacket()
        {
            int startIndex = FindPacket(out int packetLength);
            if (startIndex == -1 || packetLength == 0)
                return null;

            lock (_lock)
            {
                // 丢弃头字节之前的无效数据
                if (startIndex > 0)
                {
                    Discard(startIndex);
                }

                // 提取数据包
                byte[] packet = new byte[packetLength];
                Read(packet, 0, packetLength);
                
                return packet;
            }
        }

        /// <summary>
        /// 清空缓冲区
        /// </summary>
        public void Clear()
        {
            lock (_lock)
            {
                _head = 0;
                _tail = 0;
                _count = 0;
            }
        }
    }
}
