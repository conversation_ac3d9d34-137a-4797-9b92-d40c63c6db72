using System.Windows;

namespace RFID_UI.Views.Dialogs
{
    /// <summary>
    /// InputDialog.xaml 的交互逻辑
    /// </summary>
    public partial class InputDialog : Window
    {
        public string InputText { get; private set; } = string.Empty;

        public InputDialog()
        {
            InitializeComponent();
        }

        public InputDialog(string title, string message, string defaultText = "") : this()
        {
            Title = title;
            TitleTextBlock.Text = title;
            MessageTextBlock.Text = message;
            InputTextBox.Text = defaultText;
            
            // 设置焦点到输入框
            Loaded += (s, e) => InputTextBox.Focus();
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            InputText = InputTextBox.Text;
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 显示输入对话框的静态方法
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="message">提示信息</param>
        /// <param name="defaultText">默认文本</param>
        /// <param name="owner">父窗口</param>
        /// <returns>用户输入的文本，如果取消则返回null</returns>
        public static string? ShowDialog(string title, string message, string defaultText = "", Window? owner = null)
        {
            var dialog = new InputDialog(title, message, defaultText);
            if (owner != null)
            {
                dialog.Owner = owner;
            }

            if (dialog.ShowDialog() == true)
            {
                return dialog.InputText;
            }

            return null;
        }
    }
}
