using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace RFID_UI.Services
{
    /// <summary>
    /// 日志级别
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error
    }

    /// <summary>
    /// 日志服务
    /// </summary>
    public class LoggingService
    {
        private static readonly string LogDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "RFID_UI",
            "Logs"
        );

        private static readonly object _lockObject = new object();

        /// <summary>
        /// 写入日志到文件
        /// </summary>
        public static async Task WriteLogAsync(string message, LogLevel level = LogLevel.Info)
        {
            try
            {
                await Task.Run(() => WriteLog(message, level));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 同步写入日志
        /// </summary>
        public static void WriteLog(string message, LogLevel level = LogLevel.Info)
        {
            try
            {
                lock (_lockObject)
                {
                    // 确保日志目录存在
                    if (!Directory.Exists(LogDirectory))
                    {
                        Directory.CreateDirectory(LogDirectory);
                    }

                    // 生成日志文件名（按日期）
                    var fileName = $"rfid_log_{DateTime.Now:yyyyMMdd}.txt";
                    var filePath = Path.Combine(LogDirectory, fileName);

                    // 格式化日志消息
                    var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}";

                    // 写入文件
                    File.AppendAllText(filePath, logEntry + Environment.NewLine, Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理旧日志文件（保留指定天数）
        /// </summary>
        public static void CleanupOldLogs(int keepDays = 30)
        {
            try
            {
                if (!Directory.Exists(LogDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-keepDays);
                var files = Directory.GetFiles(LogDirectory, "rfid_log_*.txt");

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理日志文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取日志目录路径
        /// </summary>
        public static string GetLogDirectory()
        {
            return LogDirectory;
        }
    }
}
