using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows.Threading;
using RFID_UI.Commands;
using RFID_UI.Models;
using RFID_UI.Services;

namespace RFID_UI.ViewModels
{
    /// <summary>
    /// 盘点测试页面ViewModel
    /// </summary>
    public class InventoryTestViewModel : INotifyPropertyChanged
    {
        private readonly ReaderManager _readerManager;
        private readonly Action<string>? _addLogAction;
        private readonly TargetTagService _targetTagService;

        // 命令完成等待机制
        private TaskCompletionSource<CommandCompletedEventArgs>? _commandCompletionSource;

        // 盘点控制相关
        private string _inventoryButtonText = "🚀 开始盘点";
        private bool _isInventoryRunning = false;
        private int _cycleCount = 1;
        private double _intervalSeconds = 1.0;

        // 盘点命令1配置
        private ObservableCollection<CommandConfig> _command1Configs;

        // 标签过滤参数
        private string _selectedSession = "S0";
        private string _selectedAction = "00";
        private string _selectedMemBank = "EPC";

        // 盘点命令2配置
        private string _command2Hex = "";

        // 统计信息
        private string _command1Statistics = "盘点命令1统计：等待执行...";
        private string _command2Statistics = "盘点命令2统计：等待执行...";
        private string _totalStatistics = "总计统计：等待执行...";
        private string _targetTagSummary = "目标标签：0个，已匹配：0个，未匹配：0个";

        // 命令轮换相关
        private int _currentCommandIndex = 0;
        private string _currentCommandInfo = "当前命令：未选择";
        private string _commandRotationHistory = "";
        private List<string> _rotationLog = new List<string>();

        public InventoryTestViewModel(ReaderManager readerManager, Action<string>? addLogAction = null)
        {
            _readerManager = readerManager ?? throw new ArgumentNullException(nameof(readerManager));
            _addLogAction = addLogAction;
            _targetTagService = TargetTagService.Instance;

            // 初始化命令配置
            InitializeCommandConfigs();
            InitializeOptions();
            InitializeCommands();

            // 订阅目标标签服务事件
            _targetTagService.PropertyChanged += OnTargetTagServicePropertyChanged;

            // 订阅RFID标签数据接收事件
            _readerManager.TagDataReceived += OnTagDataReceived;

            // 订阅命令完成事件
            _readerManager.CommandCompleted += OnCommandCompleted;

            // 初始化统计信息
            UpdateTargetTagSummary();
        }

        #region 属性

        /// <summary>
        /// 盘点按钮文本
        /// </summary>
        public string InventoryButtonText
        {
            get => _inventoryButtonText;
            set
            {
                _inventoryButtonText = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 是否正在盘点
        /// </summary>
        public bool IsInventoryRunning
        {
            get => _isInventoryRunning;
            set
            {
                _isInventoryRunning = value;
                OnPropertyChanged();
                InventoryButtonText = value ? "🛑 停止盘点" : "🚀 开始盘点";
            }
        }

        /// <summary>
        /// 循环次数
        /// </summary>
        public int CycleCount
        {
            get => _cycleCount;
            set
            {
                _cycleCount = Math.Max(1, Math.Min(10000, value));
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 间隔时间（秒）
        /// </summary>
        public double IntervalSeconds
        {
            get => _intervalSeconds;
            set
            {
                _intervalSeconds = Math.Max(0.1, Math.Min(60.0, value));
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 盘点命令1配置列表
        /// </summary>
        public ObservableCollection<CommandConfig> Command1Configs
        {
            get => _command1Configs;
            set
            {
                _command1Configs = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Session选项（与标签过滤设置保持一致）
        /// </summary>
        public List<string> SessionOptions { get; } = new List<string> { "S0", "S1", "S2", "S3", "SL" };

        /// <summary>
        /// 选中的Session
        /// </summary>
        public string SelectedSession
        {
            get => _selectedSession;
            set
            {
                _selectedSession = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Action选项（与标签过滤设置保持一致）
        /// </summary>
        public List<string> ActionOptions { get; } = new List<string> { "00", "01", "02", "03", "04", "05", "06", "07" };

        /// <summary>
        /// 选中的Action
        /// </summary>
        public string SelectedAction
        {
            get => _selectedAction;
            set
            {
                _selectedAction = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// MemBank选项（与标签过滤设置保持一致）
        /// </summary>
        public List<string> MemBankOptions { get; } = new List<string> { "保留区", "EPC", "TID", "USER" };

        /// <summary>
        /// 选中的MemBank
        /// </summary>
        public string SelectedMemBank
        {
            get => _selectedMemBank;
            set
            {
                _selectedMemBank = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 盘点命令2的16进制命令
        /// </summary>
        public string Command2Hex
        {
            get => _command2Hex;
            set
            {
                _command2Hex = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 盘点命令1统计信息
        /// </summary>
        public string Command1Statistics
        {
            get => _command1Statistics;
            set
            {
                _command1Statistics = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 盘点命令2统计信息
        /// </summary>
        public string Command2Statistics
        {
            get => _command2Statistics;
            set
            {
                _command2Statistics = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 总计统计信息
        /// </summary>
        public string TotalStatistics
        {
            get => _totalStatistics;
            set
            {
                _totalStatistics = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 目标标签摘要信息
        /// </summary>
        public string TargetTagSummary
        {
            get => _targetTagSummary;
            set
            {
                _targetTagSummary = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 当前使用的命令信息
        /// </summary>
        public string CurrentCommandInfo
        {
            get => _currentCommandInfo;
            set
            {
                _currentCommandInfo = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 命令轮换历史记录
        /// </summary>
        public string CommandRotationHistory
        {
            get => _commandRotationHistory;
            set
            {
                _commandRotationHistory = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 目标标签列表
        /// </summary>
        public ObservableCollection<TargetTag> TargetTags => _targetTagService.TargetTags;

        #endregion

        #region 命令

        public ICommand StartInventoryCommand { get; private set; }
        public ICommand SaveConfigCommand { get; private set; }
        public ICommand LoadConfigCommand { get; private set; }
        public ICommand ResetConfigCommand { get; private set; }
        public ICommand AddTargetTagCommand { get; private set; }
        public ICommand ClearTargetTagsCommand { get; private set; }
        public ICommand ImportTargetTagsCommand { get; private set; }
        public ICommand ExportTargetTagsCommand { get; private set; }

        #endregion

        #region 私有方法

        private void InitializeCommandConfigs()
        {
            _command1Configs = new ObservableCollection<CommandConfig>
            {
                new CommandConfig { DisplayName = "命令1", IsEnabled = true, HexCommand = "" },
                new CommandConfig { DisplayName = "命令2", IsEnabled = true, HexCommand = "" },
                new CommandConfig { DisplayName = "命令3", IsEnabled = false, HexCommand = "" },
                new CommandConfig { DisplayName = "命令4", IsEnabled = false, HexCommand = "" }
            };
        }

        private void InitializeOptions()
        {
            // 设置默认值（与标签过滤设置保持一致）
            _selectedSession = "S0";
            _selectedAction = "00";
            _selectedMemBank = "EPC";
            _command2Hex = "";
        }

        private void InitializeCommands()
        {
            StartInventoryCommand = new RelayCommand(ExecuteStartInventory, CanExecuteStartInventory);
            SaveConfigCommand = new RelayCommand(ExecuteSaveConfig);
            LoadConfigCommand = new RelayCommand(ExecuteLoadConfig);
            ResetConfigCommand = new RelayCommand(ExecuteResetConfig);
            AddTargetTagCommand = new RelayCommand(ExecuteAddTargetTag);
            ClearTargetTagsCommand = new RelayCommand(ExecuteClearTargetTags);
            ImportTargetTagsCommand = new RelayCommand(ExecuteImportTargetTags);
            ExportTargetTagsCommand = new RelayCommand(ExecuteExportTargetTags);
        }

        private void OnTargetTagServicePropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(TargetTagService.TargetTagCount) ||
                e.PropertyName == nameof(TargetTagService.MatchedTagCount) ||
                e.PropertyName == nameof(TargetTagService.UnmatchedTagCount))
            {
                UpdateTargetTagSummary();
            }
        }

        private void UpdateTargetTagSummary()
        {
            TargetTagSummary = $"目标标签：{_targetTagService.TargetTagCount}个，" +
                              $"已匹配：{_targetTagService.MatchedTagCount}个，" +
                              $"未匹配：{_targetTagService.UnmatchedTagCount}个";
        }

        #endregion

        #region 命令实现（占位符）

        private bool CanExecuteStartInventory()
        {
            return !IsInventoryRunning && _readerManager.IsConnected();
        }

        private void ExecuteStartInventory()
        {
            if (IsInventoryRunning)
            {
                // 停止盘点
                StopInventory();
            }
            else
            {
                // 开始盘点
                StartInventory();
            }
        }

        private void StartInventory()
        {
            try
            {
                // 验证配置
                if (!ValidateInventoryConfig())
                {
                    return;
                }

                IsInventoryRunning = true;

                // 重置统计信息
                Command1Statistics = "盘点命令1统计：准备中...";
                Command2Statistics = "盘点命令2统计：等待执行...";
                TotalStatistics = "总计统计：准备中...";

                // 重置目标标签匹配状态
                _targetTagService.ResetAllMatchStatus();

                _addLogAction?.Invoke($"🚀 开始智能盘点策略 - 循环{CycleCount}次，间隔{IntervalSeconds}秒");
                _addLogAction?.Invoke($"📋 目标标签数量：{_targetTagService.TargetTagCount}个");

                // 执行智能盘点策略
                _ = System.Threading.Tasks.Task.Run(async () => await ExecuteSmartInventoryStrategy());
            }
            catch (Exception ex)
            {
                IsInventoryRunning = false;
                System.Windows.MessageBox.Show($"启动盘点失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                _addLogAction?.Invoke($"❌ 启动盘点失败: {ex.Message}");
            }
        }

        private void StopInventory()
        {
            try
            {
                IsInventoryRunning = false;
                _addLogAction?.Invoke("🛑 盘点已停止");
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 停止盘点失败: {ex.Message}");
            }
        }

        private bool ValidateInventoryConfig()
        {
            // 检查是否有启用的命令1
            var enabledCommands = Command1Configs.Where(c => c.IsEnabled && !string.IsNullOrWhiteSpace(c.HexCommand)).ToList();
            if (enabledCommands.Count == 0)
            {
                System.Windows.MessageBox.Show("请至少启用一个盘点命令1并设置16进制命令！", "配置错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            // 要求至少启用两个命令以实现轮换
            if (enabledCommands.Count < 2)
            {
                System.Windows.MessageBox.Show(
                    "请至少勾选两个盘点命令1并设置16进制命令！\n\n命令轮换需要至少两个命令才能正常工作。",
                    "配置错误",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Warning);
                return false;
            }

            // 检查命令2
            if (string.IsNullOrWhiteSpace(Command2Hex))
            {
                System.Windows.MessageBox.Show("请设置盘点命令2的16进制命令！", "配置错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            // 检查目标标签
            if (_targetTagService.TargetTagCount == 0)
            {
                var result = System.Windows.MessageBox.Show(
                    "目标标签列表为空，是否继续盘点？\n\n没有目标标签时无法进行匹配比对。",
                    "确认盘点",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result != System.Windows.MessageBoxResult.Yes)
                {
                    return false;
                }
            }

            return true;
        }

        private async System.Threading.Tasks.Task ExecuteSmartInventoryStrategy()
        {
            try
            {
                _addLogAction?.Invoke("🚀 开始执行智能盘点策略");

                // 重置命令轮换状态
                ResetCommandRotation();

                // 获取已启用的命令列表
                var enabledCommands = Command1Configs.Where(c => c.IsEnabled && !string.IsNullOrWhiteSpace(c.HexCommand)).ToList();
                if (enabledCommands.Count == 0)
                {
                    _addLogAction?.Invoke("❌ 没有启用的盘点命令1");
                    IsInventoryRunning = false;
                    return;
                }

                // 初始化统计数据
                var totalStartTime = DateTime.Now;
                int totalCommand1ReadCount = 0;
                int totalCommand2ReadCount = 0;
                int totalCommand1TagCount = 0;
                int totalCommand2TagCount = 0;

                // 外层循环：用户设置的循环次数
                for (int cycle = 1; cycle <= CycleCount && IsInventoryRunning; cycle++)
                {
                    _addLogAction?.Invoke($"📋 开始第 {cycle}/{CycleCount} 次盘点循环");

                    // 选择当前循环使用的命令（轮换机制）
                    var currentCommand = SelectNextCommand(enabledCommands, cycle);
                    UpdateCommandRotationInfo(currentCommand, cycle, enabledCommands.Count);

                    // 执行单次完整盘点
                    var cycleResult = await ExecuteSingleInventoryCycle(currentCommand, cycle);

                    // 累计统计数据
                    totalCommand1ReadCount += cycleResult.Command1ReadCount;
                    totalCommand2ReadCount += cycleResult.Command2ReadCount;
                    totalCommand1TagCount += cycleResult.Command1TagCount;
                    totalCommand2TagCount += cycleResult.Command2TagCount;

                    // 更新统计显示
                    UpdateStatisticsDisplay(totalStartTime, totalCommand1ReadCount, totalCommand2ReadCount,
                                          totalCommand1TagCount, totalCommand2TagCount);

                    // 检查是否所有目标标签都已匹配
                    if (_targetTagService.UnmatchedTagCount == 0 && _targetTagService.TargetTagCount > 0)
                    {
                        _addLogAction?.Invoke("🎉 所有目标标签都已匹配，提前结束盘点");
                        break;
                    }

                    // 循环间隔（最后一次循环不需要等待）
                    if (cycle < CycleCount && IsInventoryRunning && IntervalSeconds > 0)
                    {
                        _addLogAction?.Invoke($"⏱️ 等待 {IntervalSeconds} 秒后开始下一次循环");
                        await System.Threading.Tasks.Task.Delay(TimeSpan.FromSeconds(IntervalSeconds));
                    }
                }

                // 盘点完成
                var totalDuration = DateTime.Now - totalStartTime;
                _addLogAction?.Invoke($"✅ 智能盘点策略执行完成，总耗时：{totalDuration:mm\\:ss\\.fff}");
                _addLogAction?.Invoke($"📊 最终统计：目标标签 {_targetTagService.TargetTagCount} 个，已匹配 {_targetTagService.MatchedTagCount} 个");

                // 显示命令轮换统计
                var rotationStats = GetCommandRotationStats(enabledCommands);
                _addLogAction?.Invoke($"🔄 {rotationStats}");
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 智能盘点策略执行失败: {ex.Message}");
            }
            finally
            {
                IsInventoryRunning = false;
            }
        }

        private void ExecuteSaveConfig()
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "保存盘点策略配置",
                    Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    FilterIndex = 1,
                    FileName = $"盘点策略配置_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var config = new InventoryConfig
                    {
                        CycleCount = CycleCount,
                        IntervalSeconds = IntervalSeconds,
                        Command1Configs = Command1Configs.ToList(),
                        SelectedSession = SelectedSession,
                        SelectedAction = SelectedAction,
                        SelectedMemBank = SelectedMemBank,
                        Command2Hex = Command2Hex
                    };

                    var json = System.Text.Json.JsonSerializer.Serialize(config, new System.Text.Json.JsonSerializerOptions
                    {
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });

                    System.IO.File.WriteAllText(saveFileDialog.FileName, json, System.Text.Encoding.UTF8);

                    System.Windows.MessageBox.Show($"配置已保存到文件：\n{saveFileDialog.FileName}", "保存成功",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    _addLogAction?.Invoke("💾 盘点策略配置已保存");
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"保存配置失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                _addLogAction?.Invoke($"❌ 保存配置失败: {ex.Message}");
            }
        }

        private void ExecuteLoadConfig()
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "加载盘点策略配置",
                    Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    FilterIndex = 1,
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var json = System.IO.File.ReadAllText(openFileDialog.FileName, System.Text.Encoding.UTF8);
                    var config = System.Text.Json.JsonSerializer.Deserialize<InventoryConfig>(json);

                    if (config != null)
                    {
                        // 加载配置到界面
                        CycleCount = config.CycleCount;
                        IntervalSeconds = config.IntervalSeconds;
                        SelectedSession = config.SelectedSession ?? "S0";
                        SelectedAction = config.SelectedAction ?? "00";
                        SelectedMemBank = config.SelectedMemBank ?? "EPC";
                        Command2Hex = config.Command2Hex ?? "";

                        // 加载命令配置
                        if (config.Command1Configs != null && config.Command1Configs.Count > 0)
                        {
                            for (int i = 0; i < Math.Min(config.Command1Configs.Count, Command1Configs.Count); i++)
                            {
                                Command1Configs[i].IsEnabled = config.Command1Configs[i].IsEnabled;
                                Command1Configs[i].HexCommand = config.Command1Configs[i].HexCommand ?? "";
                            }
                        }

                        System.Windows.MessageBox.Show($"配置已从文件加载：\n{openFileDialog.FileName}", "加载成功",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                        _addLogAction?.Invoke("📂 盘点策略配置已加载");
                    }
                    else
                    {
                        System.Windows.MessageBox.Show("配置文件格式无效！", "加载失败",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"加载配置失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                _addLogAction?.Invoke($"❌ 加载配置失败: {ex.Message}");
            }
        }

        private void ExecuteResetConfig()
        {
            try
            {
                var result = System.Windows.MessageBox.Show(
                    "确定要重置所有配置为默认值吗？\n\n此操作不可撤销！",
                    "确认重置",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    // 重置为默认值
                    CycleCount = 1;
                    IntervalSeconds = 1.0;
                    SelectedSession = "S0";
                    SelectedAction = "00";
                    SelectedMemBank = "EPC";
                    Command2Hex = "";

                    // 重置命令配置
                    foreach (var config in Command1Configs)
                    {
                        config.IsEnabled = false;
                        config.HexCommand = "";
                    }
                    // 默认启用前两个命令
                    if (Command1Configs.Count > 0)
                    {
                        Command1Configs[0].IsEnabled = true;
                    }
                    if (Command1Configs.Count > 1)
                    {
                        Command1Configs[1].IsEnabled = true;
                    }

                    System.Windows.MessageBox.Show("配置已重置为默认值！", "重置成功",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    _addLogAction?.Invoke("🔄 盘点策略配置已重置为默认值");
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"重置配置失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                _addLogAction?.Invoke($"❌ 重置配置失败: {ex.Message}");
            }
        }

        private void ExecuteAddTargetTag()
        {
            try
            {
                // 创建输入对话框
                string? input = Views.Dialogs.InputDialog.ShowDialog(
                    "添加目标标签",
                    "请输入目标标签的EPC号：\n\n支持格式：\n- 完整EPC（如：E2801160600002040000001F）\n- 多个EPC用换行分隔",
                    "");

                if (string.IsNullOrWhiteSpace(input))
                {
                    return; // 用户取消或输入为空
                }

                // 处理多行输入，支持多种分隔符
                var epcs = input.Split(new[] { '\r', '\n', ',', ';', '\t', ' ' }, StringSplitOptions.RemoveEmptyEntries)
                               .Select(epc => epc.Trim().ToUpper())
                               .Where(epc => !string.IsNullOrWhiteSpace(epc))
                               .Select(epc => epc.Trim('"', '\'')) // 移除可能的引号
                               .Where(epc => !string.IsNullOrWhiteSpace(epc) && epc.Length > 4) // 基本长度验证
                               .ToList();

                if (epcs.Count == 0)
                {
                    System.Windows.MessageBox.Show("请输入有效的EPC号！", "输入错误",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                    return;
                }

                // 添加到目标标签列表
                int addedCount = _targetTagService.AddTargetTags(epcs);
                int skippedCount = epcs.Count - addedCount;

                // 显示结果
                string message = $"成功添加 {addedCount} 个目标标签！";
                if (skippedCount > 0)
                {
                    message += $"\n跳过重复标签：{skippedCount} 个";
                }
                message += $"\n\n目标列表总数：{_targetTagService.TargetTagCount} 个";

                System.Windows.MessageBox.Show(message, "添加成功",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                _addLogAction?.Invoke($"✅ 已添加 {addedCount} 个目标标签（跳过重复 {skippedCount} 个）");
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"添加目标标签失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                _addLogAction?.Invoke($"❌ 添加目标标签失败: {ex.Message}");
            }
        }

        private void ExecuteClearTargetTags()
        {
            try
            {
                if (_targetTagService.TargetTagCount == 0)
                {
                    System.Windows.MessageBox.Show("目标标签列表已经为空。", "提示",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    return;
                }

                var result = System.Windows.MessageBox.Show(
                    $"确定要清空所有 {_targetTagService.TargetTagCount} 个目标标签吗？\n\n此操作不可撤销！",
                    "确认清空",
                    System.Windows.MessageBoxButton.YesNo,
                    System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    int clearedCount = _targetTagService.TargetTagCount;
                    _targetTagService.ClearTargetTags();

                    System.Windows.MessageBox.Show($"已清空 {clearedCount} 个目标标签！", "清空成功",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    _addLogAction?.Invoke($"🗑️ 已清空 {clearedCount} 个目标标签");
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"清空目标标签失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                _addLogAction?.Invoke($"❌ 清空目标标签失败: {ex.Message}");
            }
        }

        private void ExecuteImportTargetTags()
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "导入目标标签列表",
                    Filter = "CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                    FilterIndex = 1,
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var epcs = new List<string>();

                    // 读取文件内容
                    var lines = System.IO.File.ReadAllLines(openFileDialog.FileName);

                    foreach (var line in lines)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                            continue;

                        // 支持多种分隔符：逗号、分号、制表符、空格
                        var parts = line.Split(new char[] { ',', ';', '\t', ' ' }, StringSplitOptions.RemoveEmptyEntries);

                        // 如果没有分隔符，将整行作为一个EPC处理
                        if (parts.Length == 0)
                        {
                            parts = new string[] { line };
                        }

                        foreach (var part in parts)
                        {
                            var epc = part.Trim().ToUpper();
                            // 放宽EPC验证：只要不为空且长度大于4就认为是有效的
                            if (!string.IsNullOrWhiteSpace(epc) && epc.Length > 4)
                            {
                                // 移除可能的引号
                                epc = epc.Trim('"', '\'');
                                if (!string.IsNullOrWhiteSpace(epc))
                                {
                                    epcs.Add(epc);
                                }
                            }
                        }
                    }

                    if (epcs.Count == 0)
                    {
                        System.Windows.MessageBox.Show("文件中没有找到有效的EPC数据！", "导入失败",
                            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                        return;
                    }

                    // 添加到目标标签列表
                    int addedCount = _targetTagService.AddTargetTags(epcs);
                    int skippedCount = epcs.Count - addedCount;

                    // 显示结果
                    string message = $"从文件导入成功！\n\n";
                    message += $"文件中找到：{epcs.Count} 个EPC\n";
                    message += $"成功添加：{addedCount} 个\n";
                    if (skippedCount > 0)
                    {
                        message += $"跳过重复：{skippedCount} 个\n";
                    }
                    message += $"\n目标列表总数：{_targetTagService.TargetTagCount} 个";

                    System.Windows.MessageBox.Show(message, "导入成功",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    _addLogAction?.Invoke($"📥 已从文件导入 {addedCount} 个目标标签（跳过重复 {skippedCount} 个）");
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"导入目标标签失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                _addLogAction?.Invoke($"❌ 导入目标标签失败: {ex.Message}");
            }
        }

        private void ExecuteExportTargetTags()
        {
            try
            {
                if (_targetTagService.TargetTagCount == 0)
                {
                    System.Windows.MessageBox.Show("目标标签列表为空，无法导出。", "提示",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    return;
                }

                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "导出目标标签列表",
                    Filter = "CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt",
                    FilterIndex = 1,
                    FileName = $"目标标签列表_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var lines = new List<string>();

                    // 添加CSV头部
                    if (saveFileDialog.FileName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
                    {
                        lines.Add("序号,EPC,匹配状态,首次发现时间,读取次数");

                        foreach (var tag in _targetTagService.TargetTags)
                        {
                            lines.Add($"{tag.Index},{tag.Epc},{tag.MatchStatusText},{tag.FirstFoundTimeText},{tag.ReadCount}");
                        }
                    }
                    else
                    {
                        // 纯文本格式，每行一个EPC
                        foreach (var tag in _targetTagService.TargetTags)
                        {
                            lines.Add(tag.Epc);
                        }
                    }

                    System.IO.File.WriteAllLines(saveFileDialog.FileName, lines, System.Text.Encoding.UTF8);

                    System.Windows.MessageBox.Show(
                        $"成功导出 {_targetTagService.TargetTagCount} 个目标标签到文件：\n{saveFileDialog.FileName}",
                        "导出成功",
                        System.Windows.MessageBoxButton.OK,
                        System.Windows.MessageBoxImage.Information);

                    _addLogAction?.Invoke($"📤 已导出 {_targetTagService.TargetTagCount} 个目标标签到文件");
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"导出目标标签失败:\n{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);

                _addLogAction?.Invoke($"❌ 导出目标标签失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行单次完整盘点循环
        /// </summary>
        /// <param name="command1">使用的盘点命令1</param>
        /// <param name="cycleNumber">循环序号</param>
        /// <returns>盘点结果统计</returns>
        private async System.Threading.Tasks.Task<InventoryCycleResult> ExecuteSingleInventoryCycle(CommandConfig command1, int cycleNumber)
        {
            var result = new InventoryCycleResult();

            try
            {
                // 第一步：清除所有标签过滤
                _addLogAction?.Invoke("🧹 清除所有标签过滤");
                await ClearAllTagFilters();

                // 第二步：执行盘点命令1
                _addLogAction?.Invoke($"⚡ 执行盘点命令1：{command1.HexCommand}");
                var command1StartTime = DateTime.Now;

                // 执行盘点命令1并验证结果
                var command1Result = await ExecuteInventoryCommandWithValidation(command1.HexCommand, "盘点命令1");
                var command1Duration = DateTime.Now - command1StartTime;

                result.Command1Duration = command1Duration;

                // 检查命令1执行结果
                if (!command1Result.IsSuccess)
                {
                    _addLogAction?.Invoke($"❌ 盘点命令1执行失败：{command1Result.ErrorMessage}");
                    _addLogAction?.Invoke($"🛑 终止智能盘点策略");
                    result.IsSuccess = false;
                    result.ErrorMessage = $"盘点命令1执行失败：{command1Result.ErrorMessage}";
                    return result;
                }

                result.Command1ReadCount = command1Result.ReadCount;
                result.Command1TagCount = command1Result.TagCount;

                _addLogAction?.Invoke($"✅ 盘点命令1完成，耗时：{command1Duration:ss\\.fff}秒，发现标签：{result.Command1TagCount}个");

                // 第三步：分析未匹配标签
                var unmatchedEpcs = _targetTagService.GetUnmatchedEpcs();
                _addLogAction?.Invoke($"📋 分析结果：未匹配标签 {unmatchedEpcs.Count} 个");

                // 第四步：如果有未匹配标签，执行内层循环（批量过滤处理）
                if (unmatchedEpcs.Count > 0)
                {
                    var command2Result = await ExecuteBatchFilterInventory(unmatchedEpcs);
                    result.Command2Duration = command2Result.TotalDuration;
                    result.Command2ReadCount = command2Result.TotalReadCount;
                    result.Command2TagCount = command2Result.TotalTagCount;
                }
                else
                {
                    _addLogAction?.Invoke("✅ 所有目标标签已匹配，跳过盘点命令2");
                }

                return result;
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 第{cycleNumber}次盘点循环失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 执行批量过滤盘点（内层循环）
        /// </summary>
        /// <param name="unmatchedEpcs">未匹配的EPC列表</param>
        /// <returns>批量过滤盘点结果</returns>
        private async System.Threading.Tasks.Task<BatchFilterResult> ExecuteBatchFilterInventory(List<string> unmatchedEpcs)
        {
            var result = new BatchFilterResult();
            var startTime = DateTime.Now;

            try
            {
                // 智能分批策略
                var batchStrategy = DetermineBatchStrategy(unmatchedEpcs.Count);
                _addLogAction?.Invoke($"🔄 开始批量过滤处理：{unmatchedEpcs.Count}个未匹配标签");
                _addLogAction?.Invoke($"📊 分批策略：{batchStrategy.Description}");

                var batches = CreateOptimizedBatches(unmatchedEpcs, batchStrategy);

                for (int batchIndex = 0; batchIndex < batches.Count && IsInventoryRunning; batchIndex++)
                {
                    var batch = batches[batchIndex];
                    _addLogAction?.Invoke($"📦 处理第{batchIndex + 1}/{batches.Count}批：{batch.Epcs.Count}个标签");

                    // 执行单批次过滤盘点
                    var batchResult = await ExecuteSingleBatchFilter(batch, batchIndex + 1);

                    // 累计结果
                    result.TotalReadCount += batchResult.ReadCount;
                    result.TotalTagCount += batchResult.TagCount;
                    result.TotalFilterSetCount += batchResult.FilterSetCount;
                    result.TotalFilterSuccessCount += batchResult.FilterSuccessCount;

                    // 批次间协调延迟
                    if (batchIndex < batches.Count - 1 && IsInventoryRunning)
                    {
                        await System.Threading.Tasks.Task.Delay(batchStrategy.InterBatchDelay);
                    }
                }

                result.TotalDuration = DateTime.Now - startTime;
                result.BatchCount = batches.Count;

                _addLogAction?.Invoke($"🎯 批量过滤处理完成");
                _addLogAction?.Invoke($"📊 处理统计：{batches.Count}批次，总耗时：{result.TotalDuration:mm\\:ss\\.fff}");
                _addLogAction?.Invoke($"📊 过滤统计：设置{result.TotalFilterSetCount}个，成功{result.TotalFilterSuccessCount}个");
                _addLogAction?.Invoke($"📊 盘点统计：读取{result.TotalReadCount}次，发现{result.TotalTagCount}个标签");

                return result;
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 批量过滤处理失败: {ex.Message}");
                result.TotalDuration = DateTime.Now - startTime;
                return result;
            }
        }

        /// <summary>
        /// 清除所有标签过滤
        /// </summary>
        private async System.Threading.Tasks.Task ClearAllTagFilters()
        {
            try
            {
                _addLogAction?.Invoke("🧹 清除所有标签过滤");

                // 调用ReaderManager清除所有掩码
                var response = _readerManager.ClearTagMask(0); // 0表示清除所有掩码

                if (response != null)
                {
                    _addLogAction?.Invoke("✅ 所有标签过滤已清除");
                }
                else
                {
                    _addLogAction?.Invoke("⚠️ 清除标签过滤命令执行完成，但无响应");
                }

                // 稍等一下确保命令执行完成
                await System.Threading.Tasks.Task.Delay(100);
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 清除标签过滤失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置标签过滤
        /// </summary>
        /// <param name="epcs">要过滤的EPC列表</param>
        /// <param name="batchNumber">批次号</param>
        private async System.Threading.Tasks.Task SetTagFilters(List<string> epcs, int batchNumber)
        {
            try
            {
                _addLogAction?.Invoke($"🔍 设置第{batchNumber}批标签过滤：{string.Join(", ", epcs.Take(3))}{(epcs.Count > 3 ? "..." : "")}");

                int successCount = 0;
                int failCount = 0;

                // 为每个EPC设置标签过滤，最多5个
                for (int i = 0; i < epcs.Count && i < 5; i++)
                {
                    var epc = epcs[i];
                    var maskId = i + 1;

                    try
                    {
                        // 创建TagMask对象
                        var mask = CreateTagMaskFromEpc(epc, maskId);

                        // 调用ReaderManager设置掩码
                        var response = _readerManager.SetTagMask(mask);

                        if (response != null)
                        {
                            successCount++;
                            _addLogAction?.Invoke($"  ✅ MaskID{maskId}: {epc} - 设置成功");
                        }
                        else
                        {
                            failCount++;
                            _addLogAction?.Invoke($"  ❌ MaskID{maskId}: {epc} - 设置失败");
                        }

                        // 稍等一下避免命令冲突
                        await System.Threading.Tasks.Task.Delay(50);
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        _addLogAction?.Invoke($"  ❌ MaskID{maskId}: {epc} - 异常: {ex.Message}");
                    }
                }

                _addLogAction?.Invoke($"📊 第{batchNumber}批过滤设置完成：成功{successCount}个，失败{failCount}个");
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 设置标签过滤失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行盘点命令（直接发送原始命令）
        /// </summary>
        /// <param name="hexCommand">16进制命令</param>
        private async System.Threading.Tasks.Task ExecuteInventoryCommand(string hexCommand)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(hexCommand))
                {
                    _addLogAction?.Invoke("❌ 盘点命令为空");
                    return;
                }

                _addLogAction?.Invoke($"⚡ 执行盘点命令: {hexCommand}");

                // 直接转换为字节数组，不做任何验证或限制
                var cleanHex = hexCommand.Replace(" ", "").Replace("-", "").Replace(":", "");

                if (cleanHex.Length % 2 != 0)
                {
                    _addLogAction?.Invoke("❌ 16进制命令长度必须为偶数");
                    return;
                }

                var commandBytes = new byte[cleanHex.Length / 2];
                for (int i = 0; i < commandBytes.Length; i++)
                {
                    commandBytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
                }

                _addLogAction?.Invoke($"📤 直接发送原始命令：{commandBytes.Length}字节");

                // 提取命令和数据部分
                if (commandBytes.Length < 4)
                {
                    _addLogAction?.Invoke("❌ 命令长度不足");
                    return;
                }

                byte cmd = commandBytes[3];
                byte[]? data = null;
                if (commandBytes.Length > 4)
                {
                    data = new byte[commandBytes.Length - 4];
                    Array.Copy(commandBytes, 4, data, 0, data.Length);
                }

                // 使用ReaderManager的现有方法发送
                byte[]? response = null;
                if (cmd == 0x8A || cmd == 0x8B)
                {
                    // 盘点命令使用SendInventoryCommand
                    response = _readerManager.SendInventoryCommand(cmd, data);
                }
                else
                {
                    // 其他命令使用SendCommand
                    response = _readerManager.SendCommand(cmd, data);
                }

                if (response != null)
                {
                    _addLogAction?.Invoke("✅ 盘点命令发送成功");

                    // 等待命令执行完成（通过CommandCompleted事件通知）
                    // 标签数据会通过TagDataReceived事件自动处理匹配逻辑
                    await System.Threading.Tasks.Task.Delay(100); // 短暂延迟确保命令开始执行
                }
                else
                {
                    _addLogAction?.Invoke("❌ 盘点命令发送失败");
                }
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 执行盘点命令失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析盘点命令（直接发送用户命令，不做限制）
        /// </summary>
        /// <param name="hexCommand">16进制命令字符串</param>
        /// <returns>解析后的命令信息</returns>
        private ParsedCommand? ParseInventoryCommand(string hexCommand)
        {
            try
            {
                // 移除空格和其他分隔符
                var cleanHex = hexCommand.Replace(" ", "").Replace("-", "").Replace(":", "");

                // 检查长度是否为偶数
                if (cleanHex.Length % 2 != 0)
                {
                    _addLogAction?.Invoke("❌ 16进制命令长度必须为偶数");
                    return null;
                }

                // 转换为字节数组
                var bytes = new byte[cleanHex.Length / 2];
                for (int i = 0; i < bytes.Length; i++)
                {
                    bytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
                }

                // 基本检查：至少需要Head+Len+Address+Cmd
                if (bytes.Length < 4)
                {
                    _addLogAction?.Invoke("❌ 命令长度不足，至少需要4字节");
                    return null;
                }

                byte address = bytes[2];
                byte cmd = bytes[3];

                // 提取数据部分（从第4字节开始到结束）
                byte[]? data = null;
                if (bytes.Length > 4)
                {
                    int dataLength = bytes.Length - 4;
                    data = new byte[dataLength];
                    Array.Copy(bytes, 4, data, 0, dataLength);
                    _addLogAction?.Invoke($"✅ 命令解析：Cmd=0x{cmd:X2}, Address=0x{address:X2}, DataLen={dataLength}");
                }
                else
                {
                    _addLogAction?.Invoke($"✅ 命令解析：Cmd=0x{cmd:X2}, Address=0x{address:X2}, 无数据");
                }

                return new ParsedCommand
                {
                    Command = cmd,
                    Address = address,
                    Data = data
                };
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 解析盘点命令失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 选择下一个要使用的命令（轮换机制）
        /// </summary>
        /// <param name="enabledCommands">已启用的命令列表</param>
        /// <param name="cycleNumber">当前循环序号</param>
        /// <returns>选中的命令</returns>
        private CommandConfig SelectNextCommand(List<CommandConfig> enabledCommands, int cycleNumber)
        {
            if (enabledCommands.Count == 0)
            {
                throw new InvalidOperationException("没有可用的已启用命令");
            }

            // 使用循环序号来确定命令索引，确保轮换的一致性
            _currentCommandIndex = (cycleNumber - 1) % enabledCommands.Count;
            var selectedCommand = enabledCommands[_currentCommandIndex];

            _addLogAction?.Invoke($"⚡ 命令轮换：选择第{_currentCommandIndex + 1}/{enabledCommands.Count}个命令");
            _addLogAction?.Invoke($"📋 使用盘点命令1：{selectedCommand.DisplayName} - {selectedCommand.HexCommand}");

            return selectedCommand;
        }

        /// <summary>
        /// 更新命令轮换信息显示
        /// </summary>
        /// <param name="currentCommand">当前使用的命令</param>
        /// <param name="cycleNumber">循环序号</param>
        /// <param name="totalCommands">总命令数</param>
        private void UpdateCommandRotationInfo(CommandConfig currentCommand, int cycleNumber, int totalCommands)
        {
            // 更新当前命令信息
            CurrentCommandInfo = $"当前命令：{currentCommand.DisplayName} ({_currentCommandIndex + 1}/{totalCommands})";

            // 记录轮换历史
            var rotationEntry = $"循环{cycleNumber}: {currentCommand.DisplayName}";
            _rotationLog.Add(rotationEntry);

            // 保持历史记录不超过10条
            if (_rotationLog.Count > 10)
            {
                _rotationLog.RemoveAt(0);
            }

            // 更新轮换历史显示
            CommandRotationHistory = string.Join(" → ", _rotationLog);

            _addLogAction?.Invoke($"🔄 命令轮换状态：{CurrentCommandInfo}");
        }

        /// <summary>
        /// 重置命令轮换状态
        /// </summary>
        private void ResetCommandRotation()
        {
            _currentCommandIndex = 0;
            CurrentCommandInfo = "当前命令：未选择";
            CommandRotationHistory = "";
            _rotationLog.Clear();
            _addLogAction?.Invoke("🔄 命令轮换状态已重置");
        }

        /// <summary>
        /// 获取命令轮换统计信息
        /// </summary>
        /// <param name="enabledCommands">已启用的命令列表</param>
        /// <returns>轮换统计信息</returns>
        private string GetCommandRotationStats(List<CommandConfig> enabledCommands)
        {
            if (enabledCommands.Count == 0)
            {
                return "无可用命令";
            }

            var stats = new Dictionary<string, int>();
            foreach (var entry in _rotationLog)
            {
                var commandName = entry.Split(':')[1].Trim();
                stats[commandName] = stats.GetValueOrDefault(commandName, 0) + 1;
            }

            var statsList = stats.Select(kvp => $"{kvp.Key}({kvp.Value}次)").ToList();
            return $"轮换统计：{string.Join(", ", statsList)}";
        }

        /// <summary>
        /// 根据EPC创建TagMask对象
        /// </summary>
        /// <param name="epc">EPC字符串</param>
        /// <param name="maskId">掩码ID (1-5)</param>
        /// <returns>TagMask对象</returns>
        private RFID_UI.Models.TagMask CreateTagMaskFromEpc(string epc, int maskId)
        {
            var mask = new RFID_UI.Models.TagMask();

            // 设置掩码ID
            mask.MaskId = maskId;

            // 解析用户配置的Session参数
            mask.Target = SelectedSession switch
            {
                "S0" => RFID_UI.Models.MaskTarget.InventoriedS0,
                "S1" => RFID_UI.Models.MaskTarget.InventoriedS1,
                "S2" => RFID_UI.Models.MaskTarget.InventoriedS2,
                "S3" => RFID_UI.Models.MaskTarget.InventoriedS3,
                "SL" => RFID_UI.Models.MaskTarget.SL,
                _ => RFID_UI.Models.MaskTarget.InventoriedS0
            };

            // 解析用户配置的Action参数
            if (byte.TryParse(SelectedAction, out byte actionValue))
            {
                mask.Action = (RFID_UI.Models.MaskAction)actionValue;
            }
            else
            {
                mask.Action = RFID_UI.Models.MaskAction.AssertSL_DeassertSL; // 默认00
            }

            // 解析用户配置的MemBank参数
            mask.MemBank = SelectedMemBank switch
            {
                "保留区" => RFID_UI.Models.MemoryBank.Reserved,
                "EPC" => RFID_UI.Models.MemoryBank.EPC,
                "TID" => RFID_UI.Models.MemoryBank.TID,
                "USER" => RFID_UI.Models.MemoryBank.USER,
                _ => RFID_UI.Models.MemoryBank.EPC
            };

            // 起始地址固定为32 (EPC区域的起始位置)
            mask.StartingAddress = 32;

            // 设置掩码值（EPC）
            mask.MaskValueHex = epc;

            // Truncate设置为false（不截断）
            mask.Truncate = false;

            // 设置描述
            mask.Description = $"智能盘点过滤 - {epc}";

            return mask;
        }

        /// <summary>
        /// 确定批次策略
        /// </summary>
        /// <param name="totalEpcCount">总EPC数量</param>
        /// <returns>批次策略</returns>
        private BatchStrategy DetermineBatchStrategy(int totalEpcCount)
        {
            var strategy = new BatchStrategy();

            if (totalEpcCount <= 5)
            {
                // 小批量：单批次处理
                strategy.BatchSize = totalEpcCount;
                strategy.InterBatchDelay = 0;
                strategy.Description = $"单批次处理（{totalEpcCount}个标签）";
                strategy.UseOptimizedGrouping = false;
            }
            else if (totalEpcCount <= 15)
            {
                // 中批量：标准分批
                strategy.BatchSize = 5;
                strategy.InterBatchDelay = 100;
                strategy.Description = $"标准分批处理（每批5个，{(int)Math.Ceiling((double)totalEpcCount / 5)}批次）";
                strategy.UseOptimizedGrouping = false;
            }
            else if (totalEpcCount <= 50)
            {
                // 大批量：优化分批
                strategy.BatchSize = 5;
                strategy.InterBatchDelay = 150;
                strategy.Description = $"优化分批处理（每批5个，{(int)Math.Ceiling((double)totalEpcCount / 5)}批次，优化分组）";
                strategy.UseOptimizedGrouping = true;
            }
            else
            {
                // 超大批量：快速分批
                strategy.BatchSize = 5;
                strategy.InterBatchDelay = 200;
                strategy.Description = $"快速分批处理（每批5个，{(int)Math.Ceiling((double)totalEpcCount / 5)}批次，延长间隔）";
                strategy.UseOptimizedGrouping = true;
            }

            return strategy;
        }

        /// <summary>
        /// 创建优化的批次
        /// </summary>
        /// <param name="epcs">EPC列表</param>
        /// <param name="strategy">批次策略</param>
        /// <returns>批次列表</returns>
        private List<EpcBatch> CreateOptimizedBatches(List<string> epcs, BatchStrategy strategy)
        {
            var batches = new List<EpcBatch>();

            if (strategy.UseOptimizedGrouping)
            {
                // 优化分组：按EPC相似度分组
                var groupedEpcs = GroupEpcsBySimilarity(epcs);

                foreach (var group in groupedEpcs)
                {
                    // 将每组按批次大小分割
                    for (int i = 0; i < group.Count; i += strategy.BatchSize)
                    {
                        var batchEpcs = group.Skip(i).Take(strategy.BatchSize).ToList();
                        batches.Add(new EpcBatch
                        {
                            Epcs = batchEpcs,
                            Priority = 1, // 相似EPC优先级较高
                            Description = $"相似EPC组批次（{batchEpcs.Count}个）"
                        });
                    }
                }
            }
            else
            {
                // 标准分组：按顺序分批
                for (int i = 0; i < epcs.Count; i += strategy.BatchSize)
                {
                    var batchEpcs = epcs.Skip(i).Take(strategy.BatchSize).ToList();
                    batches.Add(new EpcBatch
                    {
                        Epcs = batchEpcs,
                        Priority = 0,
                        Description = $"标准批次（{batchEpcs.Count}个）"
                    });
                }
            }

            // 按优先级排序
            batches = batches.OrderByDescending(b => b.Priority).ToList();

            return batches;
        }

        /// <summary>
        /// 按相似度分组EPC
        /// </summary>
        /// <param name="epcs">EPC列表</param>
        /// <returns>分组后的EPC列表</returns>
        private List<List<string>> GroupEpcsBySimilarity(List<string> epcs)
        {
            var groups = new List<List<string>>();
            var processed = new HashSet<string>();

            foreach (var epc in epcs)
            {
                if (processed.Contains(epc))
                    continue;

                var group = new List<string> { epc };
                processed.Add(epc);

                // 查找相似的EPC（前缀相同）
                var prefix = epc.Length >= 8 ? epc.Substring(0, 8) : epc;

                foreach (var otherEpc in epcs)
                {
                    if (processed.Contains(otherEpc))
                        continue;

                    var otherPrefix = otherEpc.Length >= 8 ? otherEpc.Substring(0, 8) : otherEpc;

                    if (prefix == otherPrefix)
                    {
                        group.Add(otherEpc);
                        processed.Add(otherEpc);
                    }
                }

                groups.Add(group);
            }

            return groups;
        }

        /// <summary>
        /// 更新统计显示
        /// </summary>
        private void UpdateStatisticsDisplay(DateTime startTime, int totalCommand1ReadCount, int totalCommand2ReadCount,
                                           int totalCommand1TagCount, int totalCommand2TagCount)
        {
            var totalDuration = DateTime.Now - startTime;
            var totalReadCount = totalCommand1ReadCount + totalCommand2ReadCount;
            var totalTagCount = totalCommand1TagCount + totalCommand2TagCount;

            Command1Statistics = $"盘点命令1统计：耗时 {totalDuration:mm\\:ss\\.fff}  读取次数 {totalCommand1ReadCount}  发现标签 {totalCommand1TagCount}个";
            Command2Statistics = $"盘点命令2统计：耗时 {totalDuration:mm\\:ss\\.fff}  读取次数 {totalCommand2ReadCount}  发现标签 {totalCommand2TagCount}个";
            TotalStatistics = $"总计统计：    总耗时 {totalDuration:mm\\:ss\\.fff}  总读取 {totalReadCount}   目标匹配 {_targetTagService.MatchedTagCount}/{_targetTagService.TargetTagCount}";
        }

        /// <summary>
        /// 执行单批次过滤盘点
        /// </summary>
        /// <param name="batch">EPC批次</param>
        /// <param name="batchNumber">批次号</param>
        /// <returns>单批次结果</returns>
        private async System.Threading.Tasks.Task<SingleBatchResult> ExecuteSingleBatchFilter(EpcBatch batch, int batchNumber)
        {
            var result = new SingleBatchResult();
            var startTime = DateTime.Now;

            try
            {
                _addLogAction?.Invoke($"🔍 {batch.Description}");
                _addLogAction?.Invoke($"📋 EPC列表：{string.Join(", ", batch.Epcs.Take(3))}{(batch.Epcs.Count > 3 ? "..." : "")}");

                // 第一步：清除之前的过滤设置
                await ClearAllTagFilters();

                // 第二步：设置当前批次的标签过滤
                await SetTagFiltersWithResult(batch.Epcs, batchNumber, result);

                // 第三步：执行盘点命令2
                _addLogAction?.Invoke($"⚡ 执行盘点命令2：{Command2Hex}");
                var inventoryStartTime = DateTime.Now;

                // 执行盘点命令2并验证结果
                var command2Result = await ExecuteInventoryCommandWithValidation(Command2Hex, $"第{batchNumber}批盘点命令2");
                var inventoryDuration = DateTime.Now - inventoryStartTime;

                // 检查命令2执行结果
                if (!command2Result.IsSuccess)
                {
                    _addLogAction?.Invoke($"⚠️ 第{batchNumber}批盘点命令2执行失败：{command2Result.ErrorMessage}");
                    // 批次失败不终止整个流程，继续下一批次
                }

                result.ReadCount = command2Result.ReadCount;
                result.TagCount = command2Result.TagCount;
                result.Duration = DateTime.Now - startTime;

                _addLogAction?.Invoke($"✅ 第{batchNumber}批完成");
                _addLogAction?.Invoke($"📊 批次统计：耗时{result.Duration:ss\\.fff}秒，读取{result.ReadCount}次，发现{result.TagCount}个标签");
                _addLogAction?.Invoke($"📊 过滤统计：设置{result.FilterSetCount}个，成功{result.FilterSuccessCount}个");

                return result;
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 第{batchNumber}批处理失败: {ex.Message}");
                result.Duration = DateTime.Now - startTime;
                return result;
            }
        }

        /// <summary>
        /// 设置标签过滤并返回结果统计
        /// </summary>
        /// <param name="epcs">要过滤的EPC列表</param>
        /// <param name="batchNumber">批次号</param>
        /// <param name="result">结果对象（用于累计统计）</param>
        private async System.Threading.Tasks.Task SetTagFiltersWithResult(List<string> epcs, int batchNumber, SingleBatchResult result)
        {
            try
            {
                _addLogAction?.Invoke($"🔧 设置过滤条件：{epcs.Count}个EPC");

                // 为每个EPC设置标签过滤，最多5个
                for (int i = 0; i < epcs.Count && i < 5; i++)
                {
                    var epc = epcs[i];
                    var maskId = i + 1;

                    try
                    {
                        result.FilterSetCount++;

                        // 创建TagMask对象
                        var mask = CreateTagMaskFromEpc(epc, maskId);

                        // 调用ReaderManager设置掩码
                        var response = _readerManager.SetTagMask(mask);

                        if (response != null)
                        {
                            result.FilterSuccessCount++;
                            _addLogAction?.Invoke($"  ✅ MaskID{maskId}: {epc.Substring(0, Math.Min(epc.Length, 12))}...");
                        }
                        else
                        {
                            _addLogAction?.Invoke($"  ❌ MaskID{maskId}: {epc.Substring(0, Math.Min(epc.Length, 12))}... - 设置失败");
                        }

                        // 稍等一下避免命令冲突
                        await System.Threading.Tasks.Task.Delay(50);
                    }
                    catch (Exception ex)
                    {
                        _addLogAction?.Invoke($"  ❌ MaskID{maskId}: {epc.Substring(0, Math.Min(epc.Length, 12))}... - 异常: {ex.Message}");
                    }
                }

                _addLogAction?.Invoke($"🎯 过滤设置完成：{result.FilterSuccessCount}/{result.FilterSetCount}个成功");
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 设置标签过滤失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理RFID标签数据接收事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">标签数据事件参数</param>
        private void OnTagDataReceived(object? sender, TagDataEventArgs e)
        {
            try
            {
                // 只在盘点运行时处理标签数据
                if (!IsInventoryRunning)
                    return;

                // 处理标签匹配逻辑
                ProcessTagDataForMatching(e);
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 处理标签数据异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理命令完成事件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">命令完成事件参数</param>
        private void OnCommandCompleted(object? sender, CommandCompletedEventArgs e)
        {
            try
            {
                if (e.IsSuccess)
                {
                    _addLogAction?.Invoke($"✅ 命令0x{e.Command:X2}执行完成：{e.Message}");
                }
                else
                {
                    _addLogAction?.Invoke($"❌ 命令0x{e.Command:X2}执行失败：{e.Message}");
                }

                // 通知等待中的命令执行方法
                _commandCompletionSource?.TrySetResult(e);
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 处理命令完成事件异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理标签数据进行匹配判断
        /// </summary>
        /// <param name="tagData">标签数据</param>
        private void ProcessTagDataForMatching(TagDataEventArgs tagData)
        {
            try
            {
                // 检查是否为目标标签
                var targetTag = _targetTagService.TargetTags.FirstOrDefault(t =>
                    t.Epc.Equals(tagData.EPC, StringComparison.OrdinalIgnoreCase));

                if (targetTag != null)
                {
                    // 找到目标标签
                    ProcessMatchedTargetTag(targetTag, tagData);
                }
                else
                {
                    // 非目标标签，记录发现但不处理
                    ProcessNonTargetTag(tagData);
                }
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 标签匹配处理异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理匹配的目标标签
        /// </summary>
        /// <param name="targetTag">目标标签</param>
        /// <param name="tagData">标签数据</param>
        private void ProcessMatchedTargetTag(TargetTag targetTag, TagDataEventArgs tagData)
        {
            try
            {
                bool wasMatched = targetTag.IsMatched;

                // 更新目标标签状态
                _targetTagService.UpdateTagMatchStatus(
                    tagData.EPC,
                    true,
                    targetTag.FirstFoundTime ?? tagData.Timestamp,
                    targetTag.ReadCount + 1
                );

                // 记录匹配日志
                if (!wasMatched)
                {
                    // 首次匹配
                    _addLogAction?.Invoke($"🎯 发现目标标签: {tagData.EPC} (天线{tagData.Antenna + 1}, RSSI:{tagData.RSSI})");

                    // 更新匹配统计
                    UpdateMatchingStatistics();
                }
                else
                {
                    // 重复读取，只在详细模式下记录
                    if (_addLogAction != null && ShouldLogDetailedMatching())
                    {
                        _addLogAction($"🔄 重复读取目标标签: {tagData.EPC} (读取次数:{targetTag.ReadCount})");
                    }
                }
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 处理匹配目标标签异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理非目标标签
        /// </summary>
        /// <param name="tagData">标签数据</param>
        private void ProcessNonTargetTag(TagDataEventArgs tagData)
        {
            try
            {
                // 非目标标签，只在详细模式下记录
                if (_addLogAction != null && ShouldLogDetailedMatching())
                {
                    _addLogAction($"📋 发现非目标标签: {tagData.EPC} (天线{tagData.Antenna + 1}, RSSI:{tagData.RSSI})");
                }

                // 可以在这里添加非目标标签的统计逻辑
                // 例如：记录干扰标签数量等
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 处理非目标标签异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新匹配统计信息
        /// </summary>
        private void UpdateMatchingStatistics()
        {
            try
            {
                // 更新目标标签摘要
                UpdateTargetTagSummary();

                // 检查是否所有目标标签都已匹配
                if (_targetTagService.TargetTagCount > 0 &&
                    _targetTagService.UnmatchedTagCount == 0)
                {
                    _addLogAction?.Invoke($"🎉 所有目标标签已匹配完成！({_targetTagService.MatchedTagCount}/{_targetTagService.TargetTagCount})");
                }
                else
                {
                    _addLogAction?.Invoke($"📊 匹配进度: {_targetTagService.MatchedTagCount}/{_targetTagService.TargetTagCount} (剩余{_targetTagService.UnmatchedTagCount}个)");
                }
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"❌ 更新匹配统计异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否应该记录详细匹配日志
        /// </summary>
        /// <returns>是否记录详细日志</returns>
        private bool ShouldLogDetailedMatching()
        {
            // 可以根据用户设置或当前模式决定是否记录详细日志
            // 目前简单返回false，避免日志过多
            return false;
        }

        /// <summary>
        /// 获取当前匹配统计信息
        /// </summary>
        /// <returns>匹配统计信息</returns>
        public MatchingStatistics GetMatchingStatistics()
        {
            return new MatchingStatistics
            {
                TotalTargetTags = _targetTagService.TargetTagCount,
                MatchedTags = _targetTagService.MatchedTagCount,
                UnmatchedTags = _targetTagService.UnmatchedTagCount,
                MatchingRate = _targetTagService.TargetTagCount > 0
                    ? (double)_targetTagService.MatchedTagCount / _targetTagService.TargetTagCount * 100
                    : 0
            };
        }

        /// <summary>
        /// 执行盘点命令并验证结果
        /// </summary>
        /// <param name="hexCommand">16进制命令</param>
        /// <param name="commandName">命令名称</param>
        /// <returns>命令执行结果</returns>
        private async System.Threading.Tasks.Task<CommandExecutionResult> ExecuteInventoryCommandWithValidation(string hexCommand, string commandName)
        {
            var result = new CommandExecutionResult();
            var startTime = DateTime.Now;

            try
            {
                if (string.IsNullOrWhiteSpace(hexCommand))
                {
                    result.ErrorMessage = "命令为空";
                    return result;
                }

                _addLogAction?.Invoke($"⚡ 执行{commandName}：{hexCommand}");

                // 记录执行前的标签统计
                int beforeTagCount = _targetTagService.MatchedTagCount;

                // 转换为字节数组
                var cleanHex = hexCommand.Replace(" ", "").Replace("-", "").Replace(":", "");
                if (cleanHex.Length % 2 != 0)
                {
                    result.ErrorMessage = "16进制命令长度必须为偶数";
                    return result;
                }

                var commandBytes = new byte[cleanHex.Length / 2];
                for (int i = 0; i < commandBytes.Length; i++)
                {
                    commandBytes[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
                }

                if (commandBytes.Length < 4)
                {
                    result.ErrorMessage = "命令长度不足";
                    return result;
                }

                byte cmd = commandBytes[3];
                byte[]? data = null;
                if (commandBytes.Length > 4)
                {
                    data = new byte[commandBytes.Length - 4];
                    Array.Copy(commandBytes, 4, data, 0, data.Length);
                }

                _addLogAction?.Invoke($"📤 发送{commandName}到读写器");

                // 创建命令完成等待任务
                _commandCompletionSource = new TaskCompletionSource<CommandCompletedEventArgs>();

                // 发送命令到读写器
                byte[]? response = null;
                if (cmd == 0x8A || cmd == 0x8B)
                {
                    response = _readerManager.SendInventoryCommand(cmd, data);
                }
                else
                {
                    response = _readerManager.SendCommand(cmd, data);
                }

                // 验证命令发送结果
                if (response == null)
                {
                    result.ErrorMessage = "读写器无响应或命令发送失败";
                    result.HasTimeout = true;
                    _addLogAction?.Invoke($"❌ {commandName}发送失败：读写器无响应");
                    return result;
                }

                _addLogAction?.Invoke($"✅ {commandName}发送成功，等待命令完成...");

                // 等待命令完成事件，最多等待10秒
                var timeoutTask = System.Threading.Tasks.Task.Delay(10000);
                var completedTask = await System.Threading.Tasks.Task.WhenAny(_commandCompletionSource.Task, timeoutTask);

                result.Duration = DateTime.Now - startTime;

                if (completedTask == timeoutTask)
                {
                    // 超时
                    result.ErrorMessage = "命令执行超时";
                    result.HasTimeout = true;
                    _addLogAction?.Invoke($"❌ {commandName}执行超时");
                    return result;
                }

                // 获取命令完成结果
                var commandResult = await _commandCompletionSource.Task;

                if (!commandResult.IsSuccess)
                {
                    result.ErrorMessage = commandResult.Message ?? "命令执行失败";
                    result.HasError = true;
                    _addLogAction?.Invoke($"❌ {commandName}执行失败：{result.ErrorMessage}");
                    return result;
                }

                // 计算标签统计
                int afterTagCount = _targetTagService.MatchedTagCount;
                int newTagCount = afterTagCount - beforeTagCount;

                // 从命令完成响应中提取读取次数（如果可用）
                result.ReadCount = ExtractReadCountFromMessage(commandResult.Message) ?? 50;
                result.TagCount = Math.Max(0, newTagCount);
                result.IsSuccess = true;

                _addLogAction?.Invoke($"📊 {commandName}统计：新发现{result.TagCount}个目标标签，读取{result.ReadCount}次");

                return result;
            }
            catch (Exception ex)
            {
                result.Duration = DateTime.Now - startTime;
                result.ErrorMessage = ex.Message;
                result.HasError = true;
                _addLogAction?.Invoke($"❌ {commandName}执行异常：{ex.Message}");
                return result;
            }
        }

        #endregion

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == true)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
            else
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
                });
            }
        }

        /// <summary>
        /// 线程安全的UI更新方法
        /// </summary>
        private void SafeUpdateUI(Action action)
        {
            if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == true)
            {
                action();
            }
            else
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(action);
            }
        }

        /// <summary>
        /// 从命令完成消息中提取读取次数
        /// </summary>
        /// <param name="message">命令完成消息</param>
        /// <returns>读取次数，如果无法提取则返回null</returns>
        private int? ExtractReadCountFromMessage(string? message)
        {
            if (string.IsNullOrEmpty(message))
                return null;

            try
            {
                // 查找"读取XXX次"的模式
                var match = System.Text.RegularExpressions.Regex.Match(message, @"读取(\d+)次");
                if (match.Success && int.TryParse(match.Groups[1].Value, out int readCount))
                {
                    return readCount;
                }
            }
            catch (Exception ex)
            {
                _addLogAction?.Invoke($"⚠️ 提取读取次数失败: {ex.Message}");
            }

            return null;
        }
    }

    /// <summary>
    /// 单次盘点循环结果
    /// </summary>
    public class InventoryCycleResult
    {
        public bool IsSuccess { get; set; } = true;
        public string ErrorMessage { get; set; } = "";
        public TimeSpan Command1Duration { get; set; }
        public int Command1ReadCount { get; set; }
        public int Command1TagCount { get; set; }
        public TimeSpan Command2Duration { get; set; }
        public int Command2ReadCount { get; set; }
        public int Command2TagCount { get; set; }
    }

    /// <summary>
    /// 批量过滤盘点结果
    /// </summary>
    public class BatchFilterResult
    {
        public TimeSpan TotalDuration { get; set; }
        public int TotalReadCount { get; set; }
        public int TotalTagCount { get; set; }
        public int BatchCount { get; set; }
        public int TotalFilterSetCount { get; set; }
        public int TotalFilterSuccessCount { get; set; }
    }

    /// <summary>
    /// 单批次过滤结果
    /// </summary>
    public class SingleBatchResult
    {
        public TimeSpan Duration { get; set; }
        public int ReadCount { get; set; }
        public int TagCount { get; set; }
        public int FilterSetCount { get; set; }
        public int FilterSuccessCount { get; set; }
    }

    /// <summary>
    /// 批次策略
    /// </summary>
    public class BatchStrategy
    {
        public int BatchSize { get; set; } = 5;
        public int InterBatchDelay { get; set; } = 100; // 毫秒
        public string Description { get; set; } = "";
        public bool UseOptimizedGrouping { get; set; } = false;
    }

    /// <summary>
    /// EPC批次
    /// </summary>
    public class EpcBatch
    {
        public List<string> Epcs { get; set; } = new();
        public int Priority { get; set; } = 0;
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// 匹配统计信息
    /// </summary>
    public class MatchingStatistics
    {
        public int TotalTargetTags { get; set; }
        public int MatchedTags { get; set; }
        public int UnmatchedTags { get; set; }
        public double MatchingRate { get; set; }
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 解析后的命令信息
    /// </summary>
    public class ParsedCommand
    {
        public byte Command { get; set; }
        public byte Address { get; set; } = 0x01;
        public byte[]? Data { get; set; }
    }

    /// <summary>
    /// 命令执行结果
    /// </summary>
    public class CommandExecutionResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = "";
        public int ReadCount { get; set; }
        public int TagCount { get; set; }
        public TimeSpan Duration { get; set; }
        public bool HasTimeout { get; set; }
        public bool HasError { get; set; }
    }

    /// <summary>
    /// 盘点策略配置模型（用于序列化）
    /// </summary>
    public class InventoryConfig
    {
        public int CycleCount { get; set; } = 1;
        public double IntervalSeconds { get; set; } = 1.0;
        public List<CommandConfig> Command1Configs { get; set; } = new();
        public string SelectedSession { get; set; } = "S0";
        public string SelectedAction { get; set; } = "00";
        public string SelectedMemBank { get; set; } = "EPC";
        public string Command2Hex { get; set; } = "";
    }

    /// <summary>
    /// 命令配置模型
    /// </summary>
    public class CommandConfig : INotifyPropertyChanged
    {
        private string _displayName = "";
        private bool _isEnabled = false;
        private string _hexCommand = "";

        public string DisplayName
        {
            get => _displayName;
            set
            {
                _displayName = value;
                OnPropertyChanged();
            }
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                _isEnabled = value;
                OnPropertyChanged();
            }
        }

        public string HexCommand
        {
            get => _hexCommand;
            set
            {
                _hexCommand = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
