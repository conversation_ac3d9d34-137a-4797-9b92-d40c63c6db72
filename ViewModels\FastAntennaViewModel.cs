using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace RFID_UI.ViewModels
{
    public class FastAntennaViewModel : INotifyPropertyChanged
    {
        // 天线配置
        private string _antennaA = "0";
        private string _antennaB = "1";
        private string _antennaC = "2";
        private string _antennaD = "3";
        private string _antennaE = "4";
        private string _antennaF = "5";
        private string _antennaG = "6";
        private string _antennaH = "7";

        // 天线启用状态 - 默认全部勾选
        private bool _antennaAEnabled = true;
        private bool _antennaBEnabled = true;
        private bool _antennaCEnabled = true;
        private bool _antennaDEnabled = true;
        private bool _antennaEEnabled = true;
        private bool _antennaFEnabled = true;
        private bool _antennaGEnabled = true;
        private bool _antennaHEnabled = true;

        // 轮询次数 - 改为字符串支持用户输入
        private string _stayTimeA = "1";
        private string _stayTimeB = "1";
        private string _stayTimeC = "1";
        private string _stayTimeD = "1";
        private string _stayTimeE = "1";
        private string _stayTimeF = "1";
        private string _stayTimeG = "1";
        private string _stayTimeH = "1";

        // 批量设置轮询次数
        private string _batchStayTime = "1";

        // 高级参数 - Session选择
        private string _selectedSession = "S1";

        // Target选择
        private bool _isTargetASelected = true;
        private bool _isTargetBSelected = false;

        // 其他高级参数
        private bool _enablePhase = false;
        private string _intervalTime = "0";
        private string _repeatCount = "1";
        private string _reserveByte = "00";

        // SL参数
        private bool _enableSL = false;
        private string _selectedSL = "00";

        // 临时功率参数
        private bool _enableTempPower = false;
        private string _tempPowerValue = "30"; // 默认30dBm，范围20-33

        public ObservableCollection<string> AntennaOptions { get; }
        public ObservableCollection<string> SessionOptions { get; }
        public ObservableCollection<string> SLOptions { get; }
        public ObservableCollection<string> TargetOptions { get; }

        // 命令
        public ICommand ApplyBatchStayTimeCommand { get; }

        // 天线启用状态属性
        public bool AntennaAEnabled
        {
            get => _antennaAEnabled;
            set { _antennaAEnabled = value; OnPropertyChanged(); }
        }

        public bool AntennaBEnabled
        {
            get => _antennaBEnabled;
            set { _antennaBEnabled = value; OnPropertyChanged(); }
        }

        public bool AntennaCEnabled
        {
            get => _antennaCEnabled;
            set { _antennaCEnabled = value; OnPropertyChanged(); }
        }

        public bool AntennaDEnabled
        {
            get => _antennaDEnabled;
            set { _antennaDEnabled = value; OnPropertyChanged(); }
        }

        public bool AntennaEEnabled
        {
            get => _antennaEEnabled;
            set { _antennaEEnabled = value; OnPropertyChanged(); }
        }

        public bool AntennaFEnabled
        {
            get => _antennaFEnabled;
            set { _antennaFEnabled = value; OnPropertyChanged(); }
        }

        public bool AntennaGEnabled
        {
            get => _antennaGEnabled;
            set { _antennaGEnabled = value; OnPropertyChanged(); }
        }

        public bool AntennaHEnabled
        {
            get => _antennaHEnabled;
            set { _antennaHEnabled = value; OnPropertyChanged(); }
        }

        // 天线配置属性（保留用于内部逻辑）
        public string AntennaA
        {
            get => _antennaA;
            set { _antennaA = value; OnPropertyChanged(); }
        }

        public string AntennaB
        {
            get => _antennaB;
            set { _antennaB = value; OnPropertyChanged(); }
        }

        public string AntennaC
        {
            get => _antennaC;
            set { _antennaC = value; OnPropertyChanged(); }
        }

        public string AntennaD
        {
            get => _antennaD;
            set { _antennaD = value; OnPropertyChanged(); }
        }

        public string AntennaE
        {
            get => _antennaE;
            set { _antennaE = value; OnPropertyChanged(); }
        }

        public string AntennaF
        {
            get => _antennaF;
            set { _antennaF = value; OnPropertyChanged(); }
        }

        public string AntennaG
        {
            get => _antennaG;
            set { _antennaG = value; OnPropertyChanged(); }
        }

        public string AntennaH
        {
            get => _antennaH;
            set { _antennaH = value; OnPropertyChanged(); }
        }

        // 停留时间属性 - 改为字符串类型
        public string StayTimeA
        {
            get => _stayTimeA;
            set { _stayTimeA = value; OnPropertyChanged(); }
        }

        public string StayTimeB
        {
            get => _stayTimeB;
            set { _stayTimeB = value; OnPropertyChanged(); }
        }

        public string StayTimeC
        {
            get => _stayTimeC;
            set { _stayTimeC = value; OnPropertyChanged(); }
        }

        public string StayTimeD
        {
            get => _stayTimeD;
            set { _stayTimeD = value; OnPropertyChanged(); }
        }

        public string StayTimeE
        {
            get => _stayTimeE;
            set { _stayTimeE = value; OnPropertyChanged(); }
        }

        public string StayTimeF
        {
            get => _stayTimeF;
            set { _stayTimeF = value; OnPropertyChanged(); }
        }

        public string StayTimeG
        {
            get => _stayTimeG;
            set { _stayTimeG = value; OnPropertyChanged(); }
        }

        public string StayTimeH
        {
            get => _stayTimeH;
            set { _stayTimeH = value; OnPropertyChanged(); }
        }

        // 批量设置停留时间属性
        public string BatchStayTime
        {
            get => _batchStayTime;
            set { _batchStayTime = value; OnPropertyChanged(); }
        }

        // Session选择属性
        public string SelectedSession
        {
            get => _selectedSession;
            set { _selectedSession = value; OnPropertyChanged(); }
        }

        // Target选择属性
        public bool IsTargetASelected
        {
            get => _isTargetASelected;
            set
            {
                _isTargetASelected = value;
                if (value) { _isTargetBSelected = false; }
                OnPropertyChanged();
            }
        }

        public bool IsTargetBSelected
        {
            get => _isTargetBSelected;
            set
            {
                _isTargetBSelected = value;
                if (value) { _isTargetASelected = false; }
                OnPropertyChanged();
            }
        }

        public bool EnablePhase
        {
            get => _enablePhase;
            set { _enablePhase = value; OnPropertyChanged(); }
        }

        public string IntervalTime
        {
            get => _intervalTime;
            set { _intervalTime = value; OnPropertyChanged(); }
        }

        public string RepeatCount
        {
            get => _repeatCount;
            set { _repeatCount = value; OnPropertyChanged(); }
        }

        public string ReserveByte
        {
            get => _reserveByte;
            set { _reserveByte = value; OnPropertyChanged(); }
        }

        public bool EnableSL
        {
            get => _enableSL;
            set { _enableSL = value; OnPropertyChanged(); }
        }

        public string SelectedSL
        {
            get => _selectedSL;
            set { _selectedSL = value; OnPropertyChanged(); }
        }

        public bool EnableTempPower
        {
            get => _enableTempPower;
            set { _enableTempPower = value; OnPropertyChanged(); }
        }

        public string TempPowerValue
        {
            get => _tempPowerValue;
            set { _tempPowerValue = value; OnPropertyChanged(); }
        }

        public FastAntennaViewModel()
        {
            // 初始化选项
            AntennaOptions = new ObservableCollection<string>
            {
                "0", "1", "2", "3", "4", "5", "6", "7", "禁用(0xFF)"
            };

            SessionOptions = new ObservableCollection<string>
            {
                "S0", "S1", "S2", "S3"
            };

            SLOptions = new ObservableCollection<string>
            {
                "00", "01", "02", "03"
            };

            TargetOptions = new ObservableCollection<string>
            {
                "A", "B"
            };

            // 初始化命令
            ApplyBatchStayTimeCommand = new RelayCommand(ApplyBatchStayTime);
        }

        /// <summary>
        /// 批量设置轮询次数
        /// </summary>
        private void ApplyBatchStayTime()
        {
            if (!string.IsNullOrWhiteSpace(_batchStayTime))
            {
                StayTimeA = _batchStayTime;
                StayTimeB = _batchStayTime;
                StayTimeC = _batchStayTime;
                StayTimeD = _batchStayTime;
                StayTimeE = _batchStayTime;
                StayTimeF = _batchStayTime;
                StayTimeG = _batchStayTime;
                StayTimeH = _batchStayTime;
            }
        }

        /// <summary>
        /// 获取天线配置数组，用于构建命令数据包
        /// </summary>
        public AntennaConfig[] GetAntennaConfigs()
        {
            var configs = new List<AntennaConfig>();

            // 只添加启用的天线，天线编号固定为0-7
            if (_antennaAEnabled) configs.Add(new AntennaConfig { AntennaNumber = 0, StayTime = ParseStayTime(_stayTimeA) });
            if (_antennaBEnabled) configs.Add(new AntennaConfig { AntennaNumber = 1, StayTime = ParseStayTime(_stayTimeB) });
            if (_antennaCEnabled) configs.Add(new AntennaConfig { AntennaNumber = 2, StayTime = ParseStayTime(_stayTimeC) });
            if (_antennaDEnabled) configs.Add(new AntennaConfig { AntennaNumber = 3, StayTime = ParseStayTime(_stayTimeD) });
            if (_antennaEEnabled) configs.Add(new AntennaConfig { AntennaNumber = 4, StayTime = ParseStayTime(_stayTimeE) });
            if (_antennaFEnabled) configs.Add(new AntennaConfig { AntennaNumber = 5, StayTime = ParseStayTime(_stayTimeF) });
            if (_antennaGEnabled) configs.Add(new AntennaConfig { AntennaNumber = 6, StayTime = ParseStayTime(_stayTimeG) });
            if (_antennaHEnabled) configs.Add(new AntennaConfig { AntennaNumber = 7, StayTime = ParseStayTime(_stayTimeH) });

            return configs.ToArray();
        }

        /// <summary>
        /// 解析轮询次数字符串为字节值
        /// </summary>
        private byte ParseStayTime(string stayTimeStr)
        {
            if (byte.TryParse(stayTimeStr, out byte result) && result >= 1 && result <= 255)
            {
                return result;
            }
            return 1; // 默认值
        }

        /// <summary>
        /// 获取Session值
        /// </summary>
        public byte GetSessionValue()
        {
            return _selectedSession switch
            {
                "S0" => 0,
                "S1" => 1,
                "S2" => 2,
                "S3" => 3,
                _ => 1
            };
        }

        /// <summary>
        /// 获取Target值
        /// </summary>
        public byte GetTargetValue()
        {
            return _isTargetASelected ? (byte)0 : (byte)1;
        }

        /// <summary>
        /// 获取SL值
        /// </summary>
        public byte GetSLValue()
        {
            return _selectedSL switch
            {
                "00" => 0,
                "01" => 1,
                "02" => 2,
                "03" => 3,
                _ => 0
            };
        }

        /// <summary>
        /// 重置所有参数到默认值
        /// </summary>
        public void ResetToDefaults()
        {
            // 重置天线启用状态 - 默认全部勾选
            AntennaAEnabled = AntennaBEnabled = AntennaCEnabled = AntennaDEnabled = true;
            AntennaEEnabled = AntennaFEnabled = AntennaGEnabled = AntennaHEnabled = true;

            // 重置轮询次数
            StayTimeA = StayTimeB = StayTimeC = StayTimeD = "1";
            StayTimeE = StayTimeF = StayTimeG = StayTimeH = "1";

            // 重置批量设置
            BatchStayTime = "1";

            // 重置Session选择 - 默认S1
            SelectedSession = "S1";

            // 重置Target选择 - 默认A
            IsTargetASelected = true;
            IsTargetBSelected = false;

            // 重置其他参数
            EnablePhase = false;
            IntervalTime = "0";
            RepeatCount = "1";
            ReserveByte = "00";

            // 重置SL参数
            EnableSL = false;
            SelectedSL = "00";

            // 重置临时功率参数
            EnableTempPower = false;
            TempPowerValue = "30";
        }

        private byte ParseAntennaValue(string value)
        {
            if (value == "禁用(0xFF)")
                return 0xFF;
            
            if (byte.TryParse(value, out byte result))
                return result;
            
            return 0xFF; // 默认禁用
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }


}
