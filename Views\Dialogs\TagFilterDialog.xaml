<Window x:Class="RFID_UI.Views.Dialogs.TagFilterDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
        mc:Ignorable="d"
        Title="标签过滤设置"
        Width="1400" Height="800"
        MinWidth="1400" MinHeight="800"
        ResizeMode="CanResize"
        WindowStartupLocation="CenterOwner">

    <Grid Margin="20">
        <StackPanel>
            <TextBlock Text="标签过滤设置" FontSize="16" FontWeight="Bold" Margin="0,0,0,20"/>

            <!-- 基础设置区域 -->
            <GroupBox Header="设置过滤" Margin="0,0,0,20">
                <StackPanel Margin="10">
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="过滤ID:" Width="80" VerticalAlignment="Center"/>
                        <ComboBox Width="120" ItemsSource="{Binding MaskIdOptions}"
                                 SelectedItem="{Binding SelectedMaskId}"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="Session ID:" Width="80" VerticalAlignment="Center"/>
                        <ComboBox Width="120" ItemsSource="{Binding SessionIdOptions}"
                                 SelectedItem="{Binding SelectedSessionId}"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="过滤行为:" Width="80" VerticalAlignment="Center"/>
                        <ComboBox Width="120" ItemsSource="{Binding ActionOptions}"
                                 SelectedItem="{Binding SelectedAction}"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="过滤区域:" Width="80" VerticalAlignment="Center"/>
                        <ComboBox Width="120" ItemsSource="{Binding MemBankOptions}"
                                 SelectedItem="{Binding SelectedMemBank}"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="起始地址:" Width="80" VerticalAlignment="Center"/>
                        <TextBox Width="120" Text="{Binding StartAddress}"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="过滤值:" Width="80" VerticalAlignment="Center"/>
                        <TextBox Width="200" Text="{Binding MaskValue}"/>
                        <TextBlock Text="长度:" Margin="10,0,5,0" VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding CalculatedMaskLength}" VerticalAlignment="Center"/>
                        <TextBlock Text="bit" Margin="2,0,0,0" VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" Margin="0,10">
                        <Button Content="设置过滤" Command="{Binding SetFilterCommand}" Margin="0,0,10,0"/>
                        <Button Content="清除过滤" Command="{Binding ClearFilterCommand}" Margin="0,0,10,0"/>
                        <Button Content="查询过滤" Command="{Binding QueryFilterCommand}"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- 简化的列表区域 -->
            <GroupBox Header="过滤列表">
                <ListBox ItemsSource="{Binding MaskList}" Height="260" Margin="10">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding MaskId}" Width="80"/>
                                <TextBlock Text="{Binding SessionId}" Width="60"/>
                                <TextBlock Text="{Binding Action}" Width="60"/>
                                <TextBlock Text="{Binding MemBank}" Width="80"/>
                                <TextBlock Text="{Binding MaskValue}" Width="200"/>
                            </StackPanel>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </GroupBox>
        </StackPanel>
    </Grid>
</Window>
