using System.Windows;
using RFID_UI.ViewModels;

namespace RFID_UI.Views.Dialogs
{
    /// <summary>
    /// TagFilterDialog.xaml 的交互逻辑
    /// </summary>
    public partial class TagFilterDialog : Window
    {
        public TagFilterDialog(ReaderManager readerManager, System.Action<string>? addLogAction = null)
        {
            try
            {
                addLogAction?.Invoke("🔧 TagFilterDialog: 开始初始化组件...");
                InitializeComponent();
                addLogAction?.Invoke("✅ TagFilterDialog: InitializeComponent完成");

                addLogAction?.Invoke("🔧 TagFilterDialog: 创建TagFilterDialogViewModel...");
                DataContext = new TagFilterDialogViewModel(readerManager, addLogAction);

                addLogAction?.Invoke("✅ TagFilterDialog: 初始化完成（简化ViewModel）");
            }
            catch (Exception ex)
            {
                addLogAction?.Invoke($"❌ TagFilterDialog初始化失败: {ex.Message}");
                addLogAction?.Invoke($"❌ 异常详情: {ex}");
                throw; // 重新抛出异常
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 窗口加载时自动查询当前掩码
            if (DataContext is TagFilterDialogViewModel viewModel)
            {
                viewModel.QueryFilterCommand.Execute(null);
            }
        }
    }
}
